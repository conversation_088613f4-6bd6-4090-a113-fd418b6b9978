.jj-flash-note__popper[data-v-44225974] {
      position: absolute;
      border: none;
      outline: 0;
      text-align: center;
      width: 28px;
      height: 28px;
      background: #f7f8fa;
      border: 1px solid #e5e6eb;
      border-radius: 2px;
      padding: 4px
    }

    .jj-flash-note__popper .icon[data-v-44225974] {
      pointer-events: none
    }

    .jj-flash-note__popup.vdr-container {
      position: absolute;
      box-sizing: border-box
    }

    .jj-flash-note__popup.vdr-container .vdr-handle-tl {
      top: -4px;
      left: -4px;
      cursor: nwse-resize
    }

    .jj-flash-note__popup.vdr-container .vdr-handle-tm {
      top: -2px;
      left: 50%;
      margin-left: -3px;
      cursor: ns-resize
    }

    .jj-flash-note__popup.vdr-container .vdr-handle-tr {
      top: -4px;
      right: -4px;
      cursor: nesw-resize
    }

    .jj-flash-note__popup.vdr-container .vdr-handle-ml {
      top: 50%;
      margin-top: -3px;
      left: -2px;
      cursor: ew-resize
    }

    .jj-flash-note__popup.vdr-container .vdr-handle-mr {
      top: 50%;
      margin-top: -3px;
      right: -2px;
      cursor: ew-resize
    }

    .jj-flash-note__popup.vdr-container .vdr-handle-bl {
      bottom: -4px;
      left: -4px;
      cursor: nesw-resize
    }

    .jj-flash-note__popup.vdr-container .vdr-handle-bm {
      bottom: -2px;
      left: 50%;
      margin-left: -4px;
      cursor: ns-resize
    }

    .jj-flash-note__popup.vdr-container .vdr-handle-br {
      bottom: -4px;
      right: -4px;
      cursor: nwse-resize
    }

    .jj-flash-note__popup.vdr-container .vdr-handle {
      box-sizing: border-box;
      position: absolute;
      width: 7px;
      height: 7px
    }

    .jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-tl.handle-tl {
      top: 0;
      left: 0
    }

    .jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-tr.handle-tr {
      top: 0;
      right: 0
    }

    .jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-bl.handle-bl {
      bottom: 0;
      left: 0
    }

    .jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-br.handle-br {
      bottom: 0;
      right: 0
    }

    .jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-bm.handle-bm,
    .jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-tm.handle-tm {
      left: 10px;
      right: 10px;
      width: unset;
      margin-left: 0
    }

    .jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-ml.handle-ml,
    .jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-mr.handle-mr {
      top: 10px;
      bottom: 10px;
      height: unset;
      margin-top: 0
    }

    .jj-flash-note__popup[data-v-45165cc7] {
      position: absolute;
      border: 1px solid #e4e6eb;
      filter: drop-shadow(0 2px 15px rgba(0, 0, 0, .2));
      border-radius: 4px;
      overflow: hidden;
      z-index: 9999;
      background-color: #fff
    }

    .jj-flash-note__frame[data-v-45165cc7] {
      border: none
    }

    .jj-flash-note__app[data-v-6ad74fae] {
      z-index: 9999;
      position: fixed;
      left: 0;
      top: 0
    }

    .jj-flash-note__app .mask[data-v-6ad74fae] {
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      z-index: 9000;
      background-color: rgba(0, 0, 0, .4);
      opacity: 1
    }

    .jj-flash-note__app .fade-enter-active[data-v-6ad74fae],
    .jj-flash-note__app .fade-leave-active[data-v-6ad74fae] {
      transition: opacity .15s ease
    }

    .jj-flash-note__app .fade-enter-from[data-v-6ad74fae],
    .jj-flash-note__app .fade-leave-to[data-v-6ad74fae] {
      opacity: 0
    }

    [data-v-41285de6]:root {
      --jjext-color-brand: #1e80ff;
      --jjext-color-brand-light: #e8f3ff;
      --jjext-color-nav-title: #86909c;
      --jjext-color-nav-popup-bg: #ffffff;
      --jjext-color-primary: #1d2129;
      --jjext-color-secondary-app: #4e5969;
      --jjext-color-thirdly: #86909c;
      --jjext-color-hover: #1e80ff;
      --jjext-color-hover-thirdly: #86909c;
      --jjext-color-dropdown-text: #1e80ff;
      --jjext-color-divider: #e5e6eb;
      --jjext-color-main-bg: #f4f5f5;
      --jjext-color-secondary-bg: #ffffff;
      --jjext-color-thirdly-bg: #f4f5f5;
      --jjext-color-hover-bg: #e8f3ff;
      --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(232, 243, 255, 0) 0%,
          rgba(232, 243, 255, 0.8) 25.09%,
          #e8f3ff 50.16%,
          rgba(232, 243, 255, 0.8) 75.47%,
          rgba(232, 243, 255, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #ffffff;
      --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
      --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
      --jjext-color-tips: #f53f3f;
      --jjext-color-fourthly: #c9cdd4;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #e5e6eb;
      --jjext-color-icon-search: #ffffff;
      --jjext-color-navbar-icon: #1e80ff;
      --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
      --jjext-color-layout-title: #4e5969;
      --jjext-color-layout-title-active: #1e80ff;
      --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
      --jjext-color-layout-icon-fill: #ffffff;
      --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-layer-4: #ffffff;
      --jjext-color-font-brand1-normal: #1e80ff;
      --jjext-color-font-brand-4: #abcdff;
      --jjext-color-font-1: #252933;
      --jjext-color-font-2: #515767;
      --jjext-color-font-3: #8a919f;
      --jjext-color-font-4: #c2c8d1;
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
      --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-gray-0: #fff;
      --jjext-color-gray-1-1: #e4e6eb;
      --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-gray-1-3: #e4e6eb;
      --jjext-color-gray-2: #f2f3f5;
      --jjext-color-gray-3: #f7f8fa;
      --jjext-color-background: #f2f3f5;
      --jjext-color-layer-1: #fff;
      --jjext-color-layer-2-1: #f7f8fa;
      --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
      --jjext-color-layer-3-fill: #f2f3f5;
      --jjext-color-layer-3-border: #e4e6eb;
      --jjext-color-layer-4-dropdown: #fff;
      --jjext-color-layer-5: #fff;
      --jjext-color-brand-1-normal: #1e80ff;
      --jjext-color-brand-2-hover: #1171ee;
      --jjext-color-brand-3-click: #0060dd;
      --jjext-color-brand-4-disable: #abcdff;
      --jjext-color-brand-5-light: #eaf2ff;
      --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
      --jjext-color-mask-2: #fff;
      --jjext-color-mask-3: none;
      --jjext-color-mask-6: #ffffff;
      --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
      --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
      --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
      --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
      --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
      --jjext-color-font_danger: #ff5132;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
      --jjext-color-popup: #fff;
      --jjext-color-popover: rgba(0, 0, 0, 0.8)
    }

    :root .dark[data-v-41285de6] {
      --jjext-color-brand: #1352a3;
      --jjext-color-nav-title: #e3e3e3;
      --jjext-color-nav-popup-bg: #1352a3;
      --jjext-color-primary: #e3e3e3;
      --jjext-color-secondary-app: #a9a9a9;
      --jjext-color-thirdly: #7d7d7f;
      --jjext-color-hover: #eeeeee;
      --jjext-color-hover-thirdly: #878789;
      --jjext-color-dropdown-text: #878789;
      --jjext-color-divider: #4a4a4a;
      --jjext-color-main-bg: #121212;
      --jjext-color-secondary-bg: #272727;
      --jjext-color-thirdly-bg: #3a3a3a;
      --jjext-color-hover-bg: #3a3a3a;
      --jjext-color-comment-bg: #313131;
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(58, 58, 58, 0) 0%,
          rgba(58, 58, 58, 0.8) 25.09%,
          #3a3a3a 50.16%,
          rgba(58, 58, 58, 0.8) 75.47%,
          rgba(58, 58, 58, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #e3e3e3;
      --jjext-color-nav-bg: rgb(30, 30, 30);
      --jjext-color-nav-selected-border: #4a4a4a;
      --jjext-color-tips: #bc3030;
      --jjext-color-fourthly: #878789;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #3a3a3a;
      --jjext-color-icon-search: #e3e3e3;
      --jjext-color-navbar-icon: #e3e3e3;
      --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
      --jjext-color-layout-title: #eeeeee;
      --jjext-color-layout-title-active: #eeeeee;
      --jjext-color-layout-icon-outline: #131313;
      --jjext-color-layout-icon-fill: #e3e3e3;
      --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-layer-4: #2f2f2f;
      --jjext-color-font-brand1-normal: #4495ff;
      --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
      --jjext-color-font-1: rgba(255, 255, 255, 0.9);
      --jjext-color-font-2: rgba(255, 255, 255, 0.7);
      --jjext-color-font-3: rgba(255, 255, 255, 0.55);
      --jjext-color-font-4: rgba(255, 255, 255, 0.45);
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
      --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-gray-0: #000;
      --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
      --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-gray-1-3: #464646;
      --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
      --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
      --jjext-color-background: #000;
      --jjext-color-layer-1: #181818;
      --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
      --jjext-color-layer-4-dropdown: #2f2f2f;
      --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
      --jjext-color-brand-1-normal: #2986ff;
      --jjext-color-brand-2-hover: #1473ed;
      --jjext-color-brand-3-click: #0563dd;
      --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
      --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
      --jjext-color-mask-2: #282828;
      --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
      --jjext-color-mask-6: #181818;
      --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
      --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
      --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
      --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
      --jjext-color-font_danger: #f85959;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
      --jjext-color-popup: #282828;
      --jjext-color-popover: #323232
    }

    .juejin-search[data-v-41285de6] {
      display: flex;
      width: 682px;
      height: 46px;
      border-radius: 2px;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      position: relative
    }

    .juejin-search .search-anim[data-v-41285de6] {
      position: absolute;
      left: 8px;
      width: 28px;
      height: 28px;
      object-fit: contain;
      animation-play-state: paused
    }

    .juejin-search .search-anim.slide-right-enter-active[data-v-41285de6],
    .juejin-search .search-anim.slide-right-leave-active[data-v-41285de6] {
      transition: width .3s linear
    }

    .juejin-search .search-anim.slide-right-enter-from[data-v-41285de6],
    .juejin-search .search-anim.slide-right-leave-to[data-v-41285de6] {
      width: 0
    }

    .juejin-search .juejin-search-logo[data-v-41285de6] {
      right: 16px;
      position: absolute;
      width: 23px;
      height: 18px;
      object-fit: contain
    }

    .juejin-search .juejin-search-logo path[data-v-41285de6] {
      transition: all .3s linear
    }

    .juejin-search #juejin-search-input-global[data-v-41285de6] {
      height: 100%;
      width: 100%
    }

    .juejin-search #juejin-search-input-global .input[data-v-41285de6] {
      padding: 0 39px 0 33px;
      width: 100%;
      height: 100%;
      outline: 0;
      border: none;
      border-radius: 2px;
      color: var(--jjext-color-font-1);
      font-size: 18px;
      line-height: 22px;
      font-weight: 500;
      caret-color: transparent;
      box-sizing: border-box;
      background-color: var(--jjext-color-layer-4-plugin)
    }

    .juejin-search #juejin-search-input-global .input.active[data-v-41285de6] {
      border: 2px solid var(--jjext-color-font-brand-4)
    }

    .juejin-search #juejin-search-input-global .input.animation-stopped[data-v-41285de6] {
      caret-color: #1e80ff;
      padding-left: 16px
    }

    .juejin-search #juejin-search-input-global .input[data-v-41285de6]::placeholder {
      font-weight: 400;
      color: #86909c
    }

    .calculator[data-v-4faf9c0e] {
      display: flex;
      align-items: center;
      height: 36px;
      padding: 0 16px;
      cursor: pointer
    }

    .calculator .result[data-v-4faf9c0e] {
      font-size: 14px;
      text-align: start;
      font-weight: 500;
      line-height: 22px;
      color: #1d2129;
      margin: 0 12px;
      text-overflow: ellipsis;
      flex: 1 0 auto;
      overflow: hidden;
      white-space: nowrap;
      max-width: 494px
    }

    .calculator .hint[data-v-4faf9c0e] {
      font-size: 14px;
      line-height: 22px;
      color: #8a919f
    }

    [data-v-cc26d79c]:root {
      --jjext-color-brand: #1e80ff;
      --jjext-color-brand-light: #e8f3ff;
      --jjext-color-nav-title: #86909c;
      --jjext-color-nav-popup-bg: #ffffff;
      --jjext-color-primary: #1d2129;
      --jjext-color-secondary-app: #4e5969;
      --jjext-color-thirdly: #86909c;
      --jjext-color-hover: #1e80ff;
      --jjext-color-hover-thirdly: #86909c;
      --jjext-color-dropdown-text: #1e80ff;
      --jjext-color-divider: #e5e6eb;
      --jjext-color-main-bg: #f4f5f5;
      --jjext-color-secondary-bg: #ffffff;
      --jjext-color-thirdly-bg: #f4f5f5;
      --jjext-color-hover-bg: #e8f3ff;
      --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(232, 243, 255, 0) 0%,
          rgba(232, 243, 255, 0.8) 25.09%,
          #e8f3ff 50.16%,
          rgba(232, 243, 255, 0.8) 75.47%,
          rgba(232, 243, 255, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #ffffff;
      --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
      --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
      --jjext-color-tips: #f53f3f;
      --jjext-color-fourthly: #c9cdd4;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #e5e6eb;
      --jjext-color-icon-search: #ffffff;
      --jjext-color-navbar-icon: #1e80ff;
      --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
      --jjext-color-layout-title: #4e5969;
      --jjext-color-layout-title-active: #1e80ff;
      --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
      --jjext-color-layout-icon-fill: #ffffff;
      --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-layer-4: #ffffff;
      --jjext-color-font-brand1-normal: #1e80ff;
      --jjext-color-font-brand-4: #abcdff;
      --jjext-color-font-1: #252933;
      --jjext-color-font-2: #515767;
      --jjext-color-font-3: #8a919f;
      --jjext-color-font-4: #c2c8d1;
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
      --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-gray-0: #fff;
      --jjext-color-gray-1-1: #e4e6eb;
      --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-gray-1-3: #e4e6eb;
      --jjext-color-gray-2: #f2f3f5;
      --jjext-color-gray-3: #f7f8fa;
      --jjext-color-background: #f2f3f5;
      --jjext-color-layer-1: #fff;
      --jjext-color-layer-2-1: #f7f8fa;
      --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
      --jjext-color-layer-3-fill: #f2f3f5;
      --jjext-color-layer-3-border: #e4e6eb;
      --jjext-color-layer-4-dropdown: #fff;
      --jjext-color-layer-5: #fff;
      --jjext-color-brand-1-normal: #1e80ff;
      --jjext-color-brand-2-hover: #1171ee;
      --jjext-color-brand-3-click: #0060dd;
      --jjext-color-brand-4-disable: #abcdff;
      --jjext-color-brand-5-light: #eaf2ff;
      --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
      --jjext-color-mask-2: #fff;
      --jjext-color-mask-3: none;
      --jjext-color-mask-6: #ffffff;
      --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
      --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
      --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
      --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
      --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
      --jjext-color-font_danger: #ff5132;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
      --jjext-color-popup: #fff;
      --jjext-color-popover: rgba(0, 0, 0, 0.8)
    }

    :root .dark[data-v-cc26d79c] {
      --jjext-color-brand: #1352a3;
      --jjext-color-nav-title: #e3e3e3;
      --jjext-color-nav-popup-bg: #1352a3;
      --jjext-color-primary: #e3e3e3;
      --jjext-color-secondary-app: #a9a9a9;
      --jjext-color-thirdly: #7d7d7f;
      --jjext-color-hover: #eeeeee;
      --jjext-color-hover-thirdly: #878789;
      --jjext-color-dropdown-text: #878789;
      --jjext-color-divider: #4a4a4a;
      --jjext-color-main-bg: #121212;
      --jjext-color-secondary-bg: #272727;
      --jjext-color-thirdly-bg: #3a3a3a;
      --jjext-color-hover-bg: #3a3a3a;
      --jjext-color-comment-bg: #313131;
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(58, 58, 58, 0) 0%,
          rgba(58, 58, 58, 0.8) 25.09%,
          #3a3a3a 50.16%,
          rgba(58, 58, 58, 0.8) 75.47%,
          rgba(58, 58, 58, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #e3e3e3;
      --jjext-color-nav-bg: rgb(30, 30, 30);
      --jjext-color-nav-selected-border: #4a4a4a;
      --jjext-color-tips: #bc3030;
      --jjext-color-fourthly: #878789;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #3a3a3a;
      --jjext-color-icon-search: #e3e3e3;
      --jjext-color-navbar-icon: #e3e3e3;
      --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
      --jjext-color-layout-title: #eeeeee;
      --jjext-color-layout-title-active: #eeeeee;
      --jjext-color-layout-icon-outline: #131313;
      --jjext-color-layout-icon-fill: #e3e3e3;
      --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-layer-4: #2f2f2f;
      --jjext-color-font-brand1-normal: #4495ff;
      --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
      --jjext-color-font-1: rgba(255, 255, 255, 0.9);
      --jjext-color-font-2: rgba(255, 255, 255, 0.7);
      --jjext-color-font-3: rgba(255, 255, 255, 0.55);
      --jjext-color-font-4: rgba(255, 255, 255, 0.45);
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
      --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-gray-0: #000;
      --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
      --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-gray-1-3: #464646;
      --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
      --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
      --jjext-color-background: #000;
      --jjext-color-layer-1: #181818;
      --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
      --jjext-color-layer-4-dropdown: #2f2f2f;
      --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
      --jjext-color-brand-1-normal: #2986ff;
      --jjext-color-brand-2-hover: #1473ed;
      --jjext-color-brand-3-click: #0563dd;
      --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
      --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
      --jjext-color-mask-2: #282828;
      --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
      --jjext-color-mask-6: #181818;
      --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
      --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
      --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
      --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
      --jjext-color-font_danger: #f85959;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
      --jjext-color-popup: #282828;
      --jjext-color-popover: #323232
    }

    .search-action[data-v-cc26d79c] {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      user-select: none;
      cursor: pointer;
      height: 36px;
      border-left: 4px solid transparent;
      border-top: 4px solid transparent;
      border-bottom: 4px solid transparent;
      transition: all .15s linear;
      padding: 0 16px 0 12px
    }

    .search-action.active[data-v-cc26d79c] {
      border-left-color: var(--jjext-color-font-brand1-normal);
      background-color: #f4f5f5
    }

    .search-action .search-content[data-v-cc26d79c] {
      display: flex;
      align-items: center;
      flex: 1 0 auto;
      margin-right: 16px
    }

    .search-action .search-content .search-content__logo[data-v-cc26d79c] {
      width: 28px;
      height: 28px
    }

    .search-action .search-content .search-content__engine[data-v-cc26d79c],
    .search-action .search-content .search-content__keyword[data-v-cc26d79c] {
      font-size: 14px;
      font-weight: 500;
      line-height: 22px
    }

    .search-action .search-content .search-content__keyword[data-v-cc26d79c] {
      color: var(--jjext-color-font-1);
      margin: 0 4px 0 12px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      max-width: 396px
    }

    .search-action .search-content .search-content__engine[data-v-cc26d79c] {
      color: var(--jjext-color-font-brand1-normal)
    }

    .search-action .hint[data-v-cc26d79c] {
      font-size: 14px;
      line-height: 22px;
      color: var(--jjext-color-font-brand1-normal)
    }

    em[data-v-b1604592] {
      font-style: normal;
      color: #f53f3f
    }

    [data-v-68bcb17a]:root {
      --jjext-color-brand: #1e80ff;
      --jjext-color-brand-light: #e8f3ff;
      --jjext-color-nav-title: #86909c;
      --jjext-color-nav-popup-bg: #ffffff;
      --jjext-color-primary: #1d2129;
      --jjext-color-secondary-app: #4e5969;
      --jjext-color-thirdly: #86909c;
      --jjext-color-hover: #1e80ff;
      --jjext-color-hover-thirdly: #86909c;
      --jjext-color-dropdown-text: #1e80ff;
      --jjext-color-divider: #e5e6eb;
      --jjext-color-main-bg: #f4f5f5;
      --jjext-color-secondary-bg: #ffffff;
      --jjext-color-thirdly-bg: #f4f5f5;
      --jjext-color-hover-bg: #e8f3ff;
      --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(232, 243, 255, 0) 0%,
          rgba(232, 243, 255, 0.8) 25.09%,
          #e8f3ff 50.16%,
          rgba(232, 243, 255, 0.8) 75.47%,
          rgba(232, 243, 255, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #ffffff;
      --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
      --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
      --jjext-color-tips: #f53f3f;
      --jjext-color-fourthly: #c9cdd4;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #e5e6eb;
      --jjext-color-icon-search: #ffffff;
      --jjext-color-navbar-icon: #1e80ff;
      --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
      --jjext-color-layout-title: #4e5969;
      --jjext-color-layout-title-active: #1e80ff;
      --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
      --jjext-color-layout-icon-fill: #ffffff;
      --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-layer-4: #ffffff;
      --jjext-color-font-brand1-normal: #1e80ff;
      --jjext-color-font-brand-4: #abcdff;
      --jjext-color-font-1: #252933;
      --jjext-color-font-2: #515767;
      --jjext-color-font-3: #8a919f;
      --jjext-color-font-4: #c2c8d1;
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
      --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-gray-0: #fff;
      --jjext-color-gray-1-1: #e4e6eb;
      --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-gray-1-3: #e4e6eb;
      --jjext-color-gray-2: #f2f3f5;
      --jjext-color-gray-3: #f7f8fa;
      --jjext-color-background: #f2f3f5;
      --jjext-color-layer-1: #fff;
      --jjext-color-layer-2-1: #f7f8fa;
      --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
      --jjext-color-layer-3-fill: #f2f3f5;
      --jjext-color-layer-3-border: #e4e6eb;
      --jjext-color-layer-4-dropdown: #fff;
      --jjext-color-layer-5: #fff;
      --jjext-color-brand-1-normal: #1e80ff;
      --jjext-color-brand-2-hover: #1171ee;
      --jjext-color-brand-3-click: #0060dd;
      --jjext-color-brand-4-disable: #abcdff;
      --jjext-color-brand-5-light: #eaf2ff;
      --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
      --jjext-color-mask-2: #fff;
      --jjext-color-mask-3: none;
      --jjext-color-mask-6: #ffffff;
      --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
      --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
      --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
      --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
      --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
      --jjext-color-font_danger: #ff5132;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
      --jjext-color-popup: #fff;
      --jjext-color-popover: rgba(0, 0, 0, 0.8)
    }

    :root .dark[data-v-68bcb17a] {
      --jjext-color-brand: #1352a3;
      --jjext-color-nav-title: #e3e3e3;
      --jjext-color-nav-popup-bg: #1352a3;
      --jjext-color-primary: #e3e3e3;
      --jjext-color-secondary-app: #a9a9a9;
      --jjext-color-thirdly: #7d7d7f;
      --jjext-color-hover: #eeeeee;
      --jjext-color-hover-thirdly: #878789;
      --jjext-color-dropdown-text: #878789;
      --jjext-color-divider: #4a4a4a;
      --jjext-color-main-bg: #121212;
      --jjext-color-secondary-bg: #272727;
      --jjext-color-thirdly-bg: #3a3a3a;
      --jjext-color-hover-bg: #3a3a3a;
      --jjext-color-comment-bg: #313131;
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(58, 58, 58, 0) 0%,
          rgba(58, 58, 58, 0.8) 25.09%,
          #3a3a3a 50.16%,
          rgba(58, 58, 58, 0.8) 75.47%,
          rgba(58, 58, 58, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #e3e3e3;
      --jjext-color-nav-bg: rgb(30, 30, 30);
      --jjext-color-nav-selected-border: #4a4a4a;
      --jjext-color-tips: #bc3030;
      --jjext-color-fourthly: #878789;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #3a3a3a;
      --jjext-color-icon-search: #e3e3e3;
      --jjext-color-navbar-icon: #e3e3e3;
      --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
      --jjext-color-layout-title: #eeeeee;
      --jjext-color-layout-title-active: #eeeeee;
      --jjext-color-layout-icon-outline: #131313;
      --jjext-color-layout-icon-fill: #e3e3e3;
      --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-layer-4: #2f2f2f;
      --jjext-color-font-brand1-normal: #4495ff;
      --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
      --jjext-color-font-1: rgba(255, 255, 255, 0.9);
      --jjext-color-font-2: rgba(255, 255, 255, 0.7);
      --jjext-color-font-3: rgba(255, 255, 255, 0.55);
      --jjext-color-font-4: rgba(255, 255, 255, 0.45);
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
      --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-gray-0: #000;
      --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
      --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-gray-1-3: #464646;
      --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
      --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
      --jjext-color-background: #000;
      --jjext-color-layer-1: #181818;
      --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
      --jjext-color-layer-4-dropdown: #2f2f2f;
      --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
      --jjext-color-brand-1-normal: #2986ff;
      --jjext-color-brand-2-hover: #1473ed;
      --jjext-color-brand-3-click: #0563dd;
      --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
      --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
      --jjext-color-mask-2: #282828;
      --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
      --jjext-color-mask-6: #181818;
      --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
      --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
      --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
      --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
      --jjext-color-font_danger: #f85959;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
      --jjext-color-popup: #282828;
      --jjext-color-popover: #323232
    }

    .search-suggest[data-v-68bcb17a] {
      background: var(--jjext-color-layer-4);
      padding: 0 4px 8px 4px
    }

    .search-suggest .calculator.active[data-v-68bcb17a],
    .search-suggest .search-action.active[data-v-68bcb17a] {
      background: var(--jjext-color-layer-2-1)
    }

    .search-suggest .calculator[data-v-68bcb17a] {
      transition: background-color .15s linear
    }

    .search-suggest .list[data-v-68bcb17a] {
      display: flex;
      border-top: 1px solid var(--jjext-color-layer-gray-1-2);
      flex-direction: column;
      padding-top: 4px
    }

    .search-suggest .list .item[data-v-68bcb17a] {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 36px;
      cursor: pointer
    }

    .search-suggest .list .item .content[data-v-68bcb17a] {
      color: var(--jjext-color-font-1);
      font-size: 14px
    }

    .search-suggest .list .item.active[data-v-68bcb17a] {
      background: var(--jjext-color-layer-2-1)
    }

    .search-suggest .list .tool-item[data-v-68bcb17a] {
      position: relative;
      padding: 0 9px 0 4px
    }

    .search-suggest .list .tool-item .tool-icon[data-v-68bcb17a] {
      width: 24px;
      height: 24px;
      background-size: 100% 100%;
      background-position: 0 0;
      background-repeat: no-repeat
    }

    .search-suggest .list .tool-item .content[data-v-68bcb17a] {
      margin-left: 8px
    }

    .search-suggest .list .tool-item .icon-tool-arrow[data-v-68bcb17a] {
      opacity: 0;
      transition: all .15s linear;
      position: absolute;
      stroke: var(--jjext-color-font-brand1-normal);
      top: 50%;
      transform: translateY(-50%);
      right: 9px
    }

    .search-suggest .list .tool-item.active .icon-tool-arrow[data-v-68bcb17a] {
      opacity: 1
    }

    .search-suggest .list .suggest-item[data-v-68bcb17a] {
      padding: 0 7px;
      transition: background-color .15s linear
    }

    .search-suggest .list .suggest-item .icon-search[data-v-68bcb17a] {
      stroke: var(--jjext-color-font-4)
    }

    .search-suggest .list .suggest-item .content[data-v-68bcb17a] {
      margin: 0 0 0 12px
    }

    .search-suggest .list .suggest-item[data-v-68bcb17a] .highlight-keyword {
      color: var(--jjext-color-font-3)
    }

    .search-suggest .setting-hint[data-v-68bcb17a] {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin: 8px 16px 0 16px
    }

    .search-suggest .setting-hint .text[data-v-68bcb17a] {
      color: #8a919f;
      line-height: 22px;
      cursor: pointer;
      user-select: none
    }

    .search-suggest .setting-hint .text[data-v-68bcb17a]:hover:not(.disabled) {
      color: #1e80ff;
      transition: all .15s linear
    }

    .search-suggest .setting-hint .text.disabled[data-v-68bcb17a] {
      cursor: initial
    }

    :root {
      --jjext-color-input-bg: #f4f5f5;
      --jjext-color-input-error-bg: #ffece8;
      --jjext-color-input-placeholder: #86909c;
      --jjext-color-input-text: #4e5969;
      --jjext-color-input-icon: #f53f3f
    }

    :root .dark {
      --jjext-color-input-bg: rgba(255, 255, 255, 0.12);
      --jjext-color-input-error-bg: rgba(255, 81, 50, 0.15);
      --jjext-color-input-placeholder: #e3e3e3;
      --jjext-color-input-text: #e3e3e3;
      --jjext-color-input-icon: #ff6247
    }

    [data-v-341e7439]:root {
      --jjext-color-brand: #1e80ff;
      --jjext-color-brand-light: #e8f3ff;
      --jjext-color-nav-title: #86909c;
      --jjext-color-nav-popup-bg: #ffffff;
      --jjext-color-primary: #1d2129;
      --jjext-color-secondary-app: #4e5969;
      --jjext-color-thirdly: #86909c;
      --jjext-color-hover: #1e80ff;
      --jjext-color-hover-thirdly: #86909c;
      --jjext-color-dropdown-text: #1e80ff;
      --jjext-color-divider: #e5e6eb;
      --jjext-color-main-bg: #f4f5f5;
      --jjext-color-secondary-bg: #ffffff;
      --jjext-color-thirdly-bg: #f4f5f5;
      --jjext-color-hover-bg: #e8f3ff;
      --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(232, 243, 255, 0) 0%,
          rgba(232, 243, 255, 0.8) 25.09%,
          #e8f3ff 50.16%,
          rgba(232, 243, 255, 0.8) 75.47%,
          rgba(232, 243, 255, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #ffffff;
      --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
      --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
      --jjext-color-tips: #f53f3f;
      --jjext-color-fourthly: #c9cdd4;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #e5e6eb;
      --jjext-color-icon-search: #ffffff;
      --jjext-color-navbar-icon: #1e80ff;
      --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
      --jjext-color-layout-title: #4e5969;
      --jjext-color-layout-title-active: #1e80ff;
      --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
      --jjext-color-layout-icon-fill: #ffffff;
      --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-layer-4: #ffffff;
      --jjext-color-font-brand1-normal: #1e80ff;
      --jjext-color-font-brand-4: #abcdff;
      --jjext-color-font-1: #252933;
      --jjext-color-font-2: #515767;
      --jjext-color-font-3: #8a919f;
      --jjext-color-font-4: #c2c8d1;
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
      --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-gray-0: #fff;
      --jjext-color-gray-1-1: #e4e6eb;
      --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-gray-1-3: #e4e6eb;
      --jjext-color-gray-2: #f2f3f5;
      --jjext-color-gray-3: #f7f8fa;
      --jjext-color-background: #f2f3f5;
      --jjext-color-layer-1: #fff;
      --jjext-color-layer-2-1: #f7f8fa;
      --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
      --jjext-color-layer-3-fill: #f2f3f5;
      --jjext-color-layer-3-border: #e4e6eb;
      --jjext-color-layer-4-dropdown: #fff;
      --jjext-color-layer-5: #fff;
      --jjext-color-brand-1-normal: #1e80ff;
      --jjext-color-brand-2-hover: #1171ee;
      --jjext-color-brand-3-click: #0060dd;
      --jjext-color-brand-4-disable: #abcdff;
      --jjext-color-brand-5-light: #eaf2ff;
      --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
      --jjext-color-mask-2: #fff;
      --jjext-color-mask-3: none;
      --jjext-color-mask-6: #ffffff;
      --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
      --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
      --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
      --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
      --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
      --jjext-color-font_danger: #ff5132;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
      --jjext-color-popup: #fff;
      --jjext-color-popover: rgba(0, 0, 0, 0.8)
    }

    :root .dark[data-v-341e7439] {
      --jjext-color-brand: #1352a3;
      --jjext-color-nav-title: #e3e3e3;
      --jjext-color-nav-popup-bg: #1352a3;
      --jjext-color-primary: #e3e3e3;
      --jjext-color-secondary-app: #a9a9a9;
      --jjext-color-thirdly: #7d7d7f;
      --jjext-color-hover: #eeeeee;
      --jjext-color-hover-thirdly: #878789;
      --jjext-color-dropdown-text: #878789;
      --jjext-color-divider: #4a4a4a;
      --jjext-color-main-bg: #121212;
      --jjext-color-secondary-bg: #272727;
      --jjext-color-thirdly-bg: #3a3a3a;
      --jjext-color-hover-bg: #3a3a3a;
      --jjext-color-comment-bg: #313131;
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(58, 58, 58, 0) 0%,
          rgba(58, 58, 58, 0.8) 25.09%,
          #3a3a3a 50.16%,
          rgba(58, 58, 58, 0.8) 75.47%,
          rgba(58, 58, 58, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #e3e3e3;
      --jjext-color-nav-bg: rgb(30, 30, 30);
      --jjext-color-nav-selected-border: #4a4a4a;
      --jjext-color-tips: #bc3030;
      --jjext-color-fourthly: #878789;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #3a3a3a;
      --jjext-color-icon-search: #e3e3e3;
      --jjext-color-navbar-icon: #e3e3e3;
      --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
      --jjext-color-layout-title: #eeeeee;
      --jjext-color-layout-title-active: #eeeeee;
      --jjext-color-layout-icon-outline: #131313;
      --jjext-color-layout-icon-fill: #e3e3e3;
      --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-layer-4: #2f2f2f;
      --jjext-color-font-brand1-normal: #4495ff;
      --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
      --jjext-color-font-1: rgba(255, 255, 255, 0.9);
      --jjext-color-font-2: rgba(255, 255, 255, 0.7);
      --jjext-color-font-3: rgba(255, 255, 255, 0.55);
      --jjext-color-font-4: rgba(255, 255, 255, 0.45);
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
      --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-gray-0: #000;
      --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
      --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-gray-1-3: #464646;
      --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
      --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
      --jjext-color-background: #000;
      --jjext-color-layer-1: #181818;
      --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
      --jjext-color-layer-4-dropdown: #2f2f2f;
      --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
      --jjext-color-brand-1-normal: #2986ff;
      --jjext-color-brand-2-hover: #1473ed;
      --jjext-color-brand-3-click: #0563dd;
      --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
      --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
      --jjext-color-mask-2: #282828;
      --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
      --jjext-color-mask-6: #181818;
      --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
      --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
      --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
      --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
      --jjext-color-font_danger: #f85959;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
      --jjext-color-popup: #282828;
      --jjext-color-popover: #323232
    }

    .input-option[data-v-341e7439] {
      display: flex;
      flex-direction: column
    }

    .input-option span.error[data-v-341e7439] {
      margin-left: 6.6666666667rem;
      font-size: 1rem;
      line-height: 20px;
      display: inline-block;
      height: 20px;
      color: var(--jjext-color-tips)
    }

    .input-wrapper[data-v-341e7439] {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%
    }

    .input-wrapper label[data-v-341e7439] {
      width: 4em;
      font-size: 1.1666666667rem;
      line-height: 1.8333333333rem;
      color: var(--jjext-color-thirdly);
      margin-right: 1rem
    }

    .input-wrapper .input[data-v-341e7439] {
      flex: 1 0 auto;
      position: relative
    }

    .input-wrapper .input.error .input-inner[data-v-341e7439] {
      background-color: var(--jjext-color-input-error-bg)
    }

    .input-wrapper .input.error .btn-clear[data-v-341e7439] {
      color: var(--jjext-color-input-icon)
    }

    .input-wrapper .input .input-inner[data-v-341e7439] {
      background: var(--jjext-color-input-bg);
      border-radius: 2px;
      color: var(--jjext-color-input-text);
      font-size: 1.0833333333rem;
      line-height: 1.8333333333rem;
      height: 2.3333333333rem;
      padding: 0 8px;
      outline: 0;
      border: none;
      width: 100%
    }

    .input-wrapper .input .input-inner[data-v-341e7439]::placeholder {
      color: var(--jjext-color-input-placeholder)
    }

    .input-wrapper .btn-clear[data-v-341e7439] {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
      background: 0 0;
      border: none;
      outline: 0;
      color: var(--jjext-color-fourthly)
    }

    .input-wrapper .btn-clear[data-v-341e7439]::before {
      font-size: 10px;
      line-height: 10px
    }

    [data-v-5a92de1e]:root {
      --jjext-color-brand: #1e80ff;
      --jjext-color-brand-light: #e8f3ff;
      --jjext-color-nav-title: #86909c;
      --jjext-color-nav-popup-bg: #ffffff;
      --jjext-color-primary: #1d2129;
      --jjext-color-secondary-app: #4e5969;
      --jjext-color-thirdly: #86909c;
      --jjext-color-hover: #1e80ff;
      --jjext-color-hover-thirdly: #86909c;
      --jjext-color-dropdown-text: #1e80ff;
      --jjext-color-divider: #e5e6eb;
      --jjext-color-main-bg: #f4f5f5;
      --jjext-color-secondary-bg: #ffffff;
      --jjext-color-thirdly-bg: #f4f5f5;
      --jjext-color-hover-bg: #e8f3ff;
      --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(232, 243, 255, 0) 0%,
          rgba(232, 243, 255, 0.8) 25.09%,
          #e8f3ff 50.16%,
          rgba(232, 243, 255, 0.8) 75.47%,
          rgba(232, 243, 255, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #ffffff;
      --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
      --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
      --jjext-color-tips: #f53f3f;
      --jjext-color-fourthly: #c9cdd4;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #e5e6eb;
      --jjext-color-icon-search: #ffffff;
      --jjext-color-navbar-icon: #1e80ff;
      --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
      --jjext-color-layout-title: #4e5969;
      --jjext-color-layout-title-active: #1e80ff;
      --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
      --jjext-color-layout-icon-fill: #ffffff;
      --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-layer-4: #ffffff;
      --jjext-color-font-brand1-normal: #1e80ff;
      --jjext-color-font-brand-4: #abcdff;
      --jjext-color-font-1: #252933;
      --jjext-color-font-2: #515767;
      --jjext-color-font-3: #8a919f;
      --jjext-color-font-4: #c2c8d1;
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
      --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-gray-0: #fff;
      --jjext-color-gray-1-1: #e4e6eb;
      --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-gray-1-3: #e4e6eb;
      --jjext-color-gray-2: #f2f3f5;
      --jjext-color-gray-3: #f7f8fa;
      --jjext-color-background: #f2f3f5;
      --jjext-color-layer-1: #fff;
      --jjext-color-layer-2-1: #f7f8fa;
      --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
      --jjext-color-layer-3-fill: #f2f3f5;
      --jjext-color-layer-3-border: #e4e6eb;
      --jjext-color-layer-4-dropdown: #fff;
      --jjext-color-layer-5: #fff;
      --jjext-color-brand-1-normal: #1e80ff;
      --jjext-color-brand-2-hover: #1171ee;
      --jjext-color-brand-3-click: #0060dd;
      --jjext-color-brand-4-disable: #abcdff;
      --jjext-color-brand-5-light: #eaf2ff;
      --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
      --jjext-color-mask-2: #fff;
      --jjext-color-mask-3: none;
      --jjext-color-mask-6: #ffffff;
      --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
      --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
      --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
      --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
      --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
      --jjext-color-font_danger: #ff5132;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
      --jjext-color-popup: #fff;
      --jjext-color-popover: rgba(0, 0, 0, 0.8)
    }

    :root .dark[data-v-5a92de1e] {
      --jjext-color-brand: #1352a3;
      --jjext-color-nav-title: #e3e3e3;
      --jjext-color-nav-popup-bg: #1352a3;
      --jjext-color-primary: #e3e3e3;
      --jjext-color-secondary-app: #a9a9a9;
      --jjext-color-thirdly: #7d7d7f;
      --jjext-color-hover: #eeeeee;
      --jjext-color-hover-thirdly: #878789;
      --jjext-color-dropdown-text: #878789;
      --jjext-color-divider: #4a4a4a;
      --jjext-color-main-bg: #121212;
      --jjext-color-secondary-bg: #272727;
      --jjext-color-thirdly-bg: #3a3a3a;
      --jjext-color-hover-bg: #3a3a3a;
      --jjext-color-comment-bg: #313131;
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(58, 58, 58, 0) 0%,
          rgba(58, 58, 58, 0.8) 25.09%,
          #3a3a3a 50.16%,
          rgba(58, 58, 58, 0.8) 75.47%,
          rgba(58, 58, 58, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #e3e3e3;
      --jjext-color-nav-bg: rgb(30, 30, 30);
      --jjext-color-nav-selected-border: #4a4a4a;
      --jjext-color-tips: #bc3030;
      --jjext-color-fourthly: #878789;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #3a3a3a;
      --jjext-color-icon-search: #e3e3e3;
      --jjext-color-navbar-icon: #e3e3e3;
      --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
      --jjext-color-layout-title: #eeeeee;
      --jjext-color-layout-title-active: #eeeeee;
      --jjext-color-layout-icon-outline: #131313;
      --jjext-color-layout-icon-fill: #e3e3e3;
      --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-layer-4: #2f2f2f;
      --jjext-color-font-brand1-normal: #4495ff;
      --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
      --jjext-color-font-1: rgba(255, 255, 255, 0.9);
      --jjext-color-font-2: rgba(255, 255, 255, 0.7);
      --jjext-color-font-3: rgba(255, 255, 255, 0.55);
      --jjext-color-font-4: rgba(255, 255, 255, 0.45);
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
      --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-gray-0: #000;
      --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
      --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-gray-1-3: #464646;
      --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
      --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
      --jjext-color-background: #000;
      --jjext-color-layer-1: #181818;
      --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
      --jjext-color-layer-4-dropdown: #2f2f2f;
      --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
      --jjext-color-brand-1-normal: #2986ff;
      --jjext-color-brand-2-hover: #1473ed;
      --jjext-color-brand-3-click: #0563dd;
      --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
      --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
      --jjext-color-mask-2: #282828;
      --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
      --jjext-color-mask-6: #181818;
      --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
      --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
      --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
      --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
      --jjext-color-font_danger: #f85959;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
      --jjext-color-popup: #282828;
      --jjext-color-popover: #323232
    }

    [data-v-5a92de1e] {
      box-sizing: border-box
    }

    .color-tool[data-v-5a92de1e] {
      padding: 0 16px !important
    }

    .color-tool .row[data-v-5a92de1e] {
      display: flex;
      align-items: center
    }

    .color-tool .color-picker[data-v-5a92de1e] {
      cursor: pointer;
      outline: 0;
      border: none;
      padding: 0;
      margin: 0;
      border-radius: 2px;
      background-color: transparent;
      width: 92px;
      height: 40px
    }

    .color-tool .color-picker[data-v-5a92de1e]::-webkit-color-swatch-wrapper {
      padding: 3px;
      border: 1px solid transparent;
      border-radius: 4px;
      transition: all .15s linear
    }

    .color-tool .color-picker[data-v-5a92de1e]::-webkit-color-swatch-wrapper:hover {
      border: 1px solid #bedaff
    }

    .color-tool .color-picker[data-v-5a92de1e]::-webkit-color-swatch {
      border-radius: 2px;
      border: none
    }

    .color-tool .input[data-v-5a92de1e] {
      transform: translateY(10px);
      flex: 1 1 auto;
      margin: 0 12px
    }

    .color-tool .input[data-v-5a92de1e] input.input-inner {
      height: 40px;
      padding-left: 16px;
      font-size: 14px;
      color: var(--jjext-color-primary);
      box-sizing: border-box;
      background: var(--jjext-color-main-bg)
    }

    .color-tool .input[data-v-5a92de1e] label {
      display: none
    }

    .color-tool .input[data-v-5a92de1e] span.error {
      margin-left: 16px
    }

    .color-tool .input[data-v-5a92de1e] .input-wrapper .btn-clear {
      right: 8px
    }

    .color-tool .input[data-v-5a92de1e] .input-wrapper .btn-clear::before {
      font-size: 14px;
      color: #c9cdd4
    }

    .color-tool button[data-v-5a92de1e] {
      outline: 0;
      border: none;
      background-color: unset;
      width: 93px;
      height: 40px;
      font-size: 14px
    }

    .color-tool .btn-convert[data-v-5a92de1e] {
      background: var(--jjext-color-brand);
      border-radius: 2px;
      color: #fff;
      transition: all .15s linear
    }

    .color-tool .btn-convert[data-v-5a92de1e]:hover {
      background: #5399ff
    }

    .color-tool .btn-convert[data-v-5a92de1e]:active {
      background: #0060dd
    }

    .color-tool .btn-copy[data-v-5a92de1e] {
      background: rgba(30, 128, 255, .05);
      border: 1px solid rgba(30, 128, 255, .3);
      border-radius: 2px;
      color: var(--jjext-color-brand);
      transition: all .15s linear
    }

    .color-tool .btn-copy[data-v-5a92de1e]:hover {
      background: rgba(30, 128, 255, .1);
      border-color: rgba(30, 128, 255, .45)
    }

    .color-tool .btn-copy[data-v-5a92de1e]:active {
      background: rgba(30, 128, 255, .2);
      border-color: rgba(30, 128, 255, .6)
    }

    .color-tool .display[data-v-5a92de1e] {
      flex: 1;
      text-align: start;
      background-color: var(--jjext-color-main-bg);
      height: 40px;
      margin: 0 12px;
      border-radius: 2px;
      line-height: 40px;
      padding-left: 16px;
      font-size: 14px;
      color: var(--jjext-color-primary)
    }

    .color-tool .label[data-v-5a92de1e] {
      width: 92px;
      font-size: 16px;
      font-weight: 500;
      color: var(--jjext-color-primary);
      text-align: end
    }

    .color-tool .row[data-v-5a92de1e]:not(:first-of-type) {
      margin-top: 16px
    }

    [data-v-6b3fcf66]:root {
      --jjext-color-brand: #1e80ff;
      --jjext-color-brand-light: #e8f3ff;
      --jjext-color-nav-title: #86909c;
      --jjext-color-nav-popup-bg: #ffffff;
      --jjext-color-primary: #1d2129;
      --jjext-color-secondary-app: #4e5969;
      --jjext-color-thirdly: #86909c;
      --jjext-color-hover: #1e80ff;
      --jjext-color-hover-thirdly: #86909c;
      --jjext-color-dropdown-text: #1e80ff;
      --jjext-color-divider: #e5e6eb;
      --jjext-color-main-bg: #f4f5f5;
      --jjext-color-secondary-bg: #ffffff;
      --jjext-color-thirdly-bg: #f4f5f5;
      --jjext-color-hover-bg: #e8f3ff;
      --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(232, 243, 255, 0) 0%,
          rgba(232, 243, 255, 0.8) 25.09%,
          #e8f3ff 50.16%,
          rgba(232, 243, 255, 0.8) 75.47%,
          rgba(232, 243, 255, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #ffffff;
      --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
      --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
      --jjext-color-tips: #f53f3f;
      --jjext-color-fourthly: #c9cdd4;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #e5e6eb;
      --jjext-color-icon-search: #ffffff;
      --jjext-color-navbar-icon: #1e80ff;
      --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
      --jjext-color-layout-title: #4e5969;
      --jjext-color-layout-title-active: #1e80ff;
      --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
      --jjext-color-layout-icon-fill: #ffffff;
      --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-layer-4: #ffffff;
      --jjext-color-font-brand1-normal: #1e80ff;
      --jjext-color-font-brand-4: #abcdff;
      --jjext-color-font-1: #252933;
      --jjext-color-font-2: #515767;
      --jjext-color-font-3: #8a919f;
      --jjext-color-font-4: #c2c8d1;
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
      --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-gray-0: #fff;
      --jjext-color-gray-1-1: #e4e6eb;
      --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-gray-1-3: #e4e6eb;
      --jjext-color-gray-2: #f2f3f5;
      --jjext-color-gray-3: #f7f8fa;
      --jjext-color-background: #f2f3f5;
      --jjext-color-layer-1: #fff;
      --jjext-color-layer-2-1: #f7f8fa;
      --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
      --jjext-color-layer-3-fill: #f2f3f5;
      --jjext-color-layer-3-border: #e4e6eb;
      --jjext-color-layer-4-dropdown: #fff;
      --jjext-color-layer-5: #fff;
      --jjext-color-brand-1-normal: #1e80ff;
      --jjext-color-brand-2-hover: #1171ee;
      --jjext-color-brand-3-click: #0060dd;
      --jjext-color-brand-4-disable: #abcdff;
      --jjext-color-brand-5-light: #eaf2ff;
      --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
      --jjext-color-mask-2: #fff;
      --jjext-color-mask-3: none;
      --jjext-color-mask-6: #ffffff;
      --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
      --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
      --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
      --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
      --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
      --jjext-color-font_danger: #ff5132;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
      --jjext-color-popup: #fff;
      --jjext-color-popover: rgba(0, 0, 0, 0.8)
    }

    :root .dark[data-v-6b3fcf66] {
      --jjext-color-brand: #1352a3;
      --jjext-color-nav-title: #e3e3e3;
      --jjext-color-nav-popup-bg: #1352a3;
      --jjext-color-primary: #e3e3e3;
      --jjext-color-secondary-app: #a9a9a9;
      --jjext-color-thirdly: #7d7d7f;
      --jjext-color-hover: #eeeeee;
      --jjext-color-hover-thirdly: #878789;
      --jjext-color-dropdown-text: #878789;
      --jjext-color-divider: #4a4a4a;
      --jjext-color-main-bg: #121212;
      --jjext-color-secondary-bg: #272727;
      --jjext-color-thirdly-bg: #3a3a3a;
      --jjext-color-hover-bg: #3a3a3a;
      --jjext-color-comment-bg: #313131;
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(58, 58, 58, 0) 0%,
          rgba(58, 58, 58, 0.8) 25.09%,
          #3a3a3a 50.16%,
          rgba(58, 58, 58, 0.8) 75.47%,
          rgba(58, 58, 58, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #e3e3e3;
      --jjext-color-nav-bg: rgb(30, 30, 30);
      --jjext-color-nav-selected-border: #4a4a4a;
      --jjext-color-tips: #bc3030;
      --jjext-color-fourthly: #878789;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #3a3a3a;
      --jjext-color-icon-search: #e3e3e3;
      --jjext-color-navbar-icon: #e3e3e3;
      --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
      --jjext-color-layout-title: #eeeeee;
      --jjext-color-layout-title-active: #eeeeee;
      --jjext-color-layout-icon-outline: #131313;
      --jjext-color-layout-icon-fill: #e3e3e3;
      --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-layer-4: #2f2f2f;
      --jjext-color-font-brand1-normal: #4495ff;
      --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
      --jjext-color-font-1: rgba(255, 255, 255, 0.9);
      --jjext-color-font-2: rgba(255, 255, 255, 0.7);
      --jjext-color-font-3: rgba(255, 255, 255, 0.55);
      --jjext-color-font-4: rgba(255, 255, 255, 0.45);
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
      --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-gray-0: #000;
      --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
      --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-gray-1-3: #464646;
      --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
      --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
      --jjext-color-background: #000;
      --jjext-color-layer-1: #181818;
      --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
      --jjext-color-layer-4-dropdown: #2f2f2f;
      --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
      --jjext-color-brand-1-normal: #2986ff;
      --jjext-color-brand-2-hover: #1473ed;
      --jjext-color-brand-3-click: #0563dd;
      --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
      --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
      --jjext-color-mask-2: #282828;
      --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
      --jjext-color-mask-6: #181818;
      --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
      --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
      --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
      --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
      --jjext-color-font_danger: #f85959;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
      --jjext-color-popup: #282828;
      --jjext-color-popover: #323232
    }

    .quick-tool-drawer[data-v-6b3fcf66] {
      z-index: 750;
      position: fixed;
      right: 0;
      top: 0;
      bottom: 0;
      width: 60%;
      background: var(--jjext-color-thirdly-bg)
    }

    .quick-tool-drawer.dark .header .title[data-v-6b3fcf66] {
      color: #e3e3e3
    }

    .quick-tool-drawer .quick-tool-drawer__header__[data-v-6b3fcf66] {
      position: relative;
      height: 64px;
      padding: 0 16px;
      display: flex;
      flex-direction: row;
      align-items: center
    }

    .quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__icon__[data-v-6b3fcf66] {
      width: 40px;
      height: 40px
    }

    .quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__title__[data-v-6b3fcf66] {
      margin: 0 0 0 9px;
      padding: 0;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      color: var(--jjext-color-brand)
    }

    .quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__btn-close__[data-v-6b3fcf66] {
      cursor: pointer;
      position: absolute;
      right: 16px;
      top: 50%;
      font-size: 18px;
      transform: translateY(-50%)
    }

    .quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__btn-close__[data-v-6b3fcf66]::after {
      display: block;
      content: " ";
      position: absolute;
      padding: 10px;
      width: 100%;
      height: 100%;
      top: -50%;
      left: -50%
    }

    .quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__btn-close__ svg[data-v-6b3fcf66] {
      fill: var(--jjext-color-thirdly)
    }

    .quick-tool-drawer .quick-tool-drawer__tool__[data-v-6b3fcf66] {
      width: 100%;
      height: 100%;
      box-sizing: border-box
    }

    [data-v-19f1e2c8]:root {
      --jjext-color-brand: #1e80ff;
      --jjext-color-brand-light: #e8f3ff;
      --jjext-color-nav-title: #86909c;
      --jjext-color-nav-popup-bg: #ffffff;
      --jjext-color-primary: #1d2129;
      --jjext-color-secondary-app: #4e5969;
      --jjext-color-thirdly: #86909c;
      --jjext-color-hover: #1e80ff;
      --jjext-color-hover-thirdly: #86909c;
      --jjext-color-dropdown-text: #1e80ff;
      --jjext-color-divider: #e5e6eb;
      --jjext-color-main-bg: #f4f5f5;
      --jjext-color-secondary-bg: #ffffff;
      --jjext-color-thirdly-bg: #f4f5f5;
      --jjext-color-hover-bg: #e8f3ff;
      --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(232, 243, 255, 0) 0%,
          rgba(232, 243, 255, 0.8) 25.09%,
          #e8f3ff 50.16%,
          rgba(232, 243, 255, 0.8) 75.47%,
          rgba(232, 243, 255, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #ffffff;
      --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
      --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
      --jjext-color-tips: #f53f3f;
      --jjext-color-fourthly: #c9cdd4;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #e5e6eb;
      --jjext-color-icon-search: #ffffff;
      --jjext-color-navbar-icon: #1e80ff;
      --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
      --jjext-color-layout-title: #4e5969;
      --jjext-color-layout-title-active: #1e80ff;
      --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
      --jjext-color-layout-icon-fill: #ffffff;
      --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-layer-4: #ffffff;
      --jjext-color-font-brand1-normal: #1e80ff;
      --jjext-color-font-brand-4: #abcdff;
      --jjext-color-font-1: #252933;
      --jjext-color-font-2: #515767;
      --jjext-color-font-3: #8a919f;
      --jjext-color-font-4: #c2c8d1;
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
      --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-gray-0: #fff;
      --jjext-color-gray-1-1: #e4e6eb;
      --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-gray-1-3: #e4e6eb;
      --jjext-color-gray-2: #f2f3f5;
      --jjext-color-gray-3: #f7f8fa;
      --jjext-color-background: #f2f3f5;
      --jjext-color-layer-1: #fff;
      --jjext-color-layer-2-1: #f7f8fa;
      --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
      --jjext-color-layer-3-fill: #f2f3f5;
      --jjext-color-layer-3-border: #e4e6eb;
      --jjext-color-layer-4-dropdown: #fff;
      --jjext-color-layer-5: #fff;
      --jjext-color-brand-1-normal: #1e80ff;
      --jjext-color-brand-2-hover: #1171ee;
      --jjext-color-brand-3-click: #0060dd;
      --jjext-color-brand-4-disable: #abcdff;
      --jjext-color-brand-5-light: #eaf2ff;
      --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
      --jjext-color-mask-2: #fff;
      --jjext-color-mask-3: none;
      --jjext-color-mask-6: #ffffff;
      --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
      --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
      --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
      --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
      --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
      --jjext-color-font_danger: #ff5132;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
      --jjext-color-popup: #fff;
      --jjext-color-popover: rgba(0, 0, 0, 0.8)
    }

    :root .dark[data-v-19f1e2c8] {
      --jjext-color-brand: #1352a3;
      --jjext-color-nav-title: #e3e3e3;
      --jjext-color-nav-popup-bg: #1352a3;
      --jjext-color-primary: #e3e3e3;
      --jjext-color-secondary-app: #a9a9a9;
      --jjext-color-thirdly: #7d7d7f;
      --jjext-color-hover: #eeeeee;
      --jjext-color-hover-thirdly: #878789;
      --jjext-color-dropdown-text: #878789;
      --jjext-color-divider: #4a4a4a;
      --jjext-color-main-bg: #121212;
      --jjext-color-secondary-bg: #272727;
      --jjext-color-thirdly-bg: #3a3a3a;
      --jjext-color-hover-bg: #3a3a3a;
      --jjext-color-comment-bg: #313131;
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(58, 58, 58, 0) 0%,
          rgba(58, 58, 58, 0.8) 25.09%,
          #3a3a3a 50.16%,
          rgba(58, 58, 58, 0.8) 75.47%,
          rgba(58, 58, 58, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #e3e3e3;
      --jjext-color-nav-bg: rgb(30, 30, 30);
      --jjext-color-nav-selected-border: #4a4a4a;
      --jjext-color-tips: #bc3030;
      --jjext-color-fourthly: #878789;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #3a3a3a;
      --jjext-color-icon-search: #e3e3e3;
      --jjext-color-navbar-icon: #e3e3e3;
      --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
      --jjext-color-layout-title: #eeeeee;
      --jjext-color-layout-title-active: #eeeeee;
      --jjext-color-layout-icon-outline: #131313;
      --jjext-color-layout-icon-fill: #e3e3e3;
      --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-layer-4: #2f2f2f;
      --jjext-color-font-brand1-normal: #4495ff;
      --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
      --jjext-color-font-1: rgba(255, 255, 255, 0.9);
      --jjext-color-font-2: rgba(255, 255, 255, 0.7);
      --jjext-color-font-3: rgba(255, 255, 255, 0.55);
      --jjext-color-font-4: rgba(255, 255, 255, 0.45);
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
      --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-gray-0: #000;
      --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
      --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-gray-1-3: #464646;
      --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
      --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
      --jjext-color-background: #000;
      --jjext-color-layer-1: #181818;
      --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
      --jjext-color-layer-4-dropdown: #2f2f2f;
      --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
      --jjext-color-brand-1-normal: #2986ff;
      --jjext-color-brand-2-hover: #1473ed;
      --jjext-color-brand-3-click: #0563dd;
      --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
      --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
      --jjext-color-mask-2: #282828;
      --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
      --jjext-color-mask-6: #181818;
      --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
      --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
      --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
      --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
      --jjext-color-font_danger: #f85959;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
      --jjext-color-popup: #282828;
      --jjext-color-popover: #323232
    }

    .mask[data-v-19f1e2c8] {
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      z-index: 600;
      background-color: var(--jjext-color-mask)
    }

    .slide-left-enter-active,
    .slide-left-leave-active {
      transition: transform .3s linear
    }

    .slide-left-enter-from,
    .slide-left-leave-to {
      transform: translateX(100%)
    }

    [data-v-78e625ce]:root {
      --jjext-color-brand: #1e80ff;
      --jjext-color-brand-light: #e8f3ff;
      --jjext-color-nav-title: #86909c;
      --jjext-color-nav-popup-bg: #ffffff;
      --jjext-color-primary: #1d2129;
      --jjext-color-secondary-app: #4e5969;
      --jjext-color-thirdly: #86909c;
      --jjext-color-hover: #1e80ff;
      --jjext-color-hover-thirdly: #86909c;
      --jjext-color-dropdown-text: #1e80ff;
      --jjext-color-divider: #e5e6eb;
      --jjext-color-main-bg: #f4f5f5;
      --jjext-color-secondary-bg: #ffffff;
      --jjext-color-thirdly-bg: #f4f5f5;
      --jjext-color-hover-bg: #e8f3ff;
      --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(232, 243, 255, 0) 0%,
          rgba(232, 243, 255, 0.8) 25.09%,
          #e8f3ff 50.16%,
          rgba(232, 243, 255, 0.8) 75.47%,
          rgba(232, 243, 255, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #ffffff;
      --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
      --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
      --jjext-color-tips: #f53f3f;
      --jjext-color-fourthly: #c9cdd4;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #e5e6eb;
      --jjext-color-icon-search: #ffffff;
      --jjext-color-navbar-icon: #1e80ff;
      --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
      --jjext-color-layout-title: #4e5969;
      --jjext-color-layout-title-active: #1e80ff;
      --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
      --jjext-color-layout-icon-fill: #ffffff;
      --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-layer-4: #ffffff;
      --jjext-color-font-brand1-normal: #1e80ff;
      --jjext-color-font-brand-4: #abcdff;
      --jjext-color-font-1: #252933;
      --jjext-color-font-2: #515767;
      --jjext-color-font-3: #8a919f;
      --jjext-color-font-4: #c2c8d1;
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
      --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-gray-0: #fff;
      --jjext-color-gray-1-1: #e4e6eb;
      --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-gray-1-3: #e4e6eb;
      --jjext-color-gray-2: #f2f3f5;
      --jjext-color-gray-3: #f7f8fa;
      --jjext-color-background: #f2f3f5;
      --jjext-color-layer-1: #fff;
      --jjext-color-layer-2-1: #f7f8fa;
      --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
      --jjext-color-layer-3-fill: #f2f3f5;
      --jjext-color-layer-3-border: #e4e6eb;
      --jjext-color-layer-4-dropdown: #fff;
      --jjext-color-layer-5: #fff;
      --jjext-color-brand-1-normal: #1e80ff;
      --jjext-color-brand-2-hover: #1171ee;
      --jjext-color-brand-3-click: #0060dd;
      --jjext-color-brand-4-disable: #abcdff;
      --jjext-color-brand-5-light: #eaf2ff;
      --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
      --jjext-color-mask-2: #fff;
      --jjext-color-mask-3: none;
      --jjext-color-mask-6: #ffffff;
      --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
      --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
      --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
      --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
      --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
      --jjext-color-font_danger: #ff5132;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
      --jjext-color-popup: #fff;
      --jjext-color-popover: rgba(0, 0, 0, 0.8)
    }

    :root .dark[data-v-78e625ce] {
      --jjext-color-brand: #1352a3;
      --jjext-color-nav-title: #e3e3e3;
      --jjext-color-nav-popup-bg: #1352a3;
      --jjext-color-primary: #e3e3e3;
      --jjext-color-secondary-app: #a9a9a9;
      --jjext-color-thirdly: #7d7d7f;
      --jjext-color-hover: #eeeeee;
      --jjext-color-hover-thirdly: #878789;
      --jjext-color-dropdown-text: #878789;
      --jjext-color-divider: #4a4a4a;
      --jjext-color-main-bg: #121212;
      --jjext-color-secondary-bg: #272727;
      --jjext-color-thirdly-bg: #3a3a3a;
      --jjext-color-hover-bg: #3a3a3a;
      --jjext-color-comment-bg: #313131;
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(58, 58, 58, 0) 0%,
          rgba(58, 58, 58, 0.8) 25.09%,
          #3a3a3a 50.16%,
          rgba(58, 58, 58, 0.8) 75.47%,
          rgba(58, 58, 58, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #e3e3e3;
      --jjext-color-nav-bg: rgb(30, 30, 30);
      --jjext-color-nav-selected-border: #4a4a4a;
      --jjext-color-tips: #bc3030;
      --jjext-color-fourthly: #878789;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #3a3a3a;
      --jjext-color-icon-search: #e3e3e3;
      --jjext-color-navbar-icon: #e3e3e3;
      --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
      --jjext-color-layout-title: #eeeeee;
      --jjext-color-layout-title-active: #eeeeee;
      --jjext-color-layout-icon-outline: #131313;
      --jjext-color-layout-icon-fill: #e3e3e3;
      --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-layer-4: #2f2f2f;
      --jjext-color-font-brand1-normal: #4495ff;
      --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
      --jjext-color-font-1: rgba(255, 255, 255, 0.9);
      --jjext-color-font-2: rgba(255, 255, 255, 0.7);
      --jjext-color-font-3: rgba(255, 255, 255, 0.55);
      --jjext-color-font-4: rgba(255, 255, 255, 0.45);
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
      --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-gray-0: #000;
      --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
      --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-gray-1-3: #464646;
      --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
      --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
      --jjext-color-background: #000;
      --jjext-color-layer-1: #181818;
      --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
      --jjext-color-layer-4-dropdown: #2f2f2f;
      --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
      --jjext-color-brand-1-normal: #2986ff;
      --jjext-color-brand-2-hover: #1473ed;
      --jjext-color-brand-3-click: #0563dd;
      --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
      --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
      --jjext-color-mask-2: #282828;
      --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
      --jjext-color-mask-6: #181818;
      --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
      --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
      --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
      --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
      --jjext-color-font_danger: #f85959;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
      --jjext-color-popup: #282828;
      --jjext-color-popover: #323232
    }

    .search-app[data-v-78e625ce] {
      z-index: 9999;
      padding-top: 160px;
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      display: flex;
      align-items: flex-start;
      justify-content: center
    }

    .search-app.extension[data-v-78e625ce] {
      z-index: 500
    }

    @media (max-height:720px) {
      .search-app.tool-active[data-v-78e625ce] {
        padding-top: 80px
      }
    }

    @media (max-height:640px) {
      .search-app.tool-active[data-v-78e625ce] {
        padding-top: 30px
      }
    }

    .search-app .search-app__wrapper__[data-v-78e625ce] {
      overflow: hidden;
      border-radius: 4px;
      border: 1px solid var(--jjext-color-font-brand1-normal);
      background: var(--jjext-color-layer-4);
      box-shadow: 0 0 0 4px rgba(30, 128, 255, .2), 0 0 20px rgba(0, 0, 0, .15);
      backdrop-filter: blur(15px)
    }

    .search-app .search-app__wrapper__ .search-result[data-v-78e625ce] {
      margin-top: 8px
    }

    .search-app .search-app__wrapper__ .search-result .tool[data-v-78e625ce] {
      padding: 0 8px
    }

    .search-app .search-app__wrapper__[data-v-78e625ce] .search-suggest {
      padding: 0 0 8px 0
    }

    .search-app .search-app__wrapper__[data-v-78e625ce] .search-suggest .list {
      border-top: none;
      padding-left: 8px;
      padding-right: 8px
    }

    .search-app .search-app__wrapper__[data-v-78e625ce] .search-suggest .list .suggest-item {
      padding: 0 13px
    }

    .search-app .search-app__wrapper__[data-v-78e625ce] .search-suggest .list .suggest-item .content {
      margin: 0 0 0 17px
    }

    .search-app .search-app__wrapper__[data-v-78e625ce] .search-suggest .list .tool-item {
      padding: 0 9px 0 10px
    }

    .search-app .search-app__wrapper__[data-v-78e625ce] .search-suggest .list .tool-item .content {
      margin-left: 12px
    }

    .search-app .juejin-search[data-v-78e625ce] {
      margin: 8px
    }

    :root {
      --jjext-color-brand: #1e80ff;
      --jjext-color-brand-light: #e8f3ff;
      --jjext-color-nav-title: #86909c;
      --jjext-color-nav-popup-bg: #ffffff;
      --jjext-color-primary: #1d2129;
      --jjext-color-secondary-app: #4e5969;
      --jjext-color-thirdly: #86909c;
      --jjext-color-hover: #1e80ff;
      --jjext-color-hover-thirdly: #86909c;
      --jjext-color-dropdown-text: #1e80ff;
      --jjext-color-divider: #e5e6eb;
      --jjext-color-main-bg: #f4f5f5;
      --jjext-color-secondary-bg: #ffffff;
      --jjext-color-thirdly-bg: #f4f5f5;
      --jjext-color-hover-bg: #e8f3ff;
      --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(232, 243, 255, 0) 0%,
          rgba(232, 243, 255, 0.8) 25.09%,
          #e8f3ff 50.16%,
          rgba(232, 243, 255, 0.8) 75.47%,
          rgba(232, 243, 255, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #ffffff;
      --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
      --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
      --jjext-color-tips: #f53f3f;
      --jjext-color-fourthly: #c9cdd4;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #e5e6eb;
      --jjext-color-icon-search: #ffffff;
      --jjext-color-navbar-icon: #1e80ff;
      --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
      --jjext-color-layout-title: #4e5969;
      --jjext-color-layout-title-active: #1e80ff;
      --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
      --jjext-color-layout-icon-fill: #ffffff;
      --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-layer-4: #ffffff;
      --jjext-color-font-brand1-normal: #1e80ff;
      --jjext-color-font-brand-4: #abcdff;
      --jjext-color-font-1: #252933;
      --jjext-color-font-2: #515767;
      --jjext-color-font-3: #8a919f;
      --jjext-color-font-4: #c2c8d1;
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
      --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-gray-0: #fff;
      --jjext-color-gray-1-1: #e4e6eb;
      --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
      --jjext-color-gray-1-3: #e4e6eb;
      --jjext-color-gray-2: #f2f3f5;
      --jjext-color-gray-3: #f7f8fa;
      --jjext-color-background: #f2f3f5;
      --jjext-color-layer-1: #fff;
      --jjext-color-layer-2-1: #f7f8fa;
      --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
      --jjext-color-layer-3-fill: #f2f3f5;
      --jjext-color-layer-3-border: #e4e6eb;
      --jjext-color-layer-4-dropdown: #fff;
      --jjext-color-layer-5: #fff;
      --jjext-color-brand-1-normal: #1e80ff;
      --jjext-color-brand-2-hover: #1171ee;
      --jjext-color-brand-3-click: #0060dd;
      --jjext-color-brand-4-disable: #abcdff;
      --jjext-color-brand-5-light: #eaf2ff;
      --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
      --jjext-color-mask-2: #fff;
      --jjext-color-mask-3: none;
      --jjext-color-mask-6: #ffffff;
      --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
      --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
      --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
      --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
      --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
      --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
      --jjext-color-font_danger: #ff5132;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
      --jjext-color-popup: #fff;
      --jjext-color-popover: rgba(0, 0, 0, 0.8)
    }

    :root .dark {
      --jjext-color-brand: #1352a3;
      --jjext-color-nav-title: #e3e3e3;
      --jjext-color-nav-popup-bg: #1352a3;
      --jjext-color-primary: #e3e3e3;
      --jjext-color-secondary-app: #a9a9a9;
      --jjext-color-thirdly: #7d7d7f;
      --jjext-color-hover: #eeeeee;
      --jjext-color-hover-thirdly: #878789;
      --jjext-color-dropdown-text: #878789;
      --jjext-color-divider: #4a4a4a;
      --jjext-color-main-bg: #121212;
      --jjext-color-secondary-bg: #272727;
      --jjext-color-thirdly-bg: #3a3a3a;
      --jjext-color-hover-bg: #3a3a3a;
      --jjext-color-comment-bg: #313131;
      --jjext-hover-bg: linear-gradient(90deg,
          rgba(58, 58, 58, 0) 0%,
          rgba(58, 58, 58, 0.8) 25.09%,
          #3a3a3a 50.16%,
          rgba(58, 58, 58, 0.8) 75.47%,
          rgba(58, 58, 58, 0) 100%);
      --jjext-color-mask: rgba(0, 0, 0, 0.4);
      --jjext-color-quick-nav-text: #e3e3e3;
      --jjext-color-nav-bg: rgb(30, 30, 30);
      --jjext-color-nav-selected-border: #4a4a4a;
      --jjext-color-tips: #bc3030;
      --jjext-color-fourthly: #878789;
      --jjext-color-shadow: rgba(0, 0, 0, 0.16);
      --jjext-color-grey-triangle: #3a3a3a;
      --jjext-color-icon-search: #e3e3e3;
      --jjext-color-navbar-icon: #e3e3e3;
      --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
      --jjext-color-layout-title: #eeeeee;
      --jjext-color-layout-title-active: #eeeeee;
      --jjext-color-layout-icon-outline: #131313;
      --jjext-color-layout-icon-fill: #e3e3e3;
      --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-layer-4: #2f2f2f;
      --jjext-color-font-brand1-normal: #4495ff;
      --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
      --jjext-color-font-1: rgba(255, 255, 255, 0.9);
      --jjext-color-font-2: rgba(255, 255, 255, 0.7);
      --jjext-color-font-3: rgba(255, 255, 255, 0.55);
      --jjext-color-font-4: rgba(255, 255, 255, 0.45);
      --jjext-color-font-white: #fff;
      --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
      --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-gray-0: #000;
      --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
      --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
      --jjext-color-gray-1-3: #464646;
      --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
      --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
      --jjext-color-background: #000;
      --jjext-color-layer-1: #181818;
      --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
      --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
      --jjext-color-layer-4-dropdown: #2f2f2f;
      --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
      --jjext-color-brand-1-normal: #2986ff;
      --jjext-color-brand-2-hover: #1473ed;
      --jjext-color-brand-3-click: #0563dd;
      --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
      --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
      --jjext-color-mask-2: #282828;
      --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
      --jjext-color-mask-6: #181818;
      --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
      --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
      --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
      --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
      --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
      --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
      --jjext-color-font_danger: #f85959;
      --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
      --jjext-color-popup: #282828;
      --jjext-color-popover: #323232
    }