/* Generated on: Fri Feb 24 2023 02:41:03 GMT+0000 (Coordinated Universal Time) */
/* ==========================================================================
   normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css 
   ========================================================================== */
/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */html{font-family:sans-serif;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}body{margin:0}article,aside,details,figcaption,figure,footer,header,hgroup,main,menu,nav,section,summary{display:block}audio,canvas,progress,video{display:inline-block;vertical-align:baseline}audio:not([controls]){display:none;height:0}[hidden],template{display:none}a{background-color:transparent}a:active,a:hover{outline:0}abbr[title]{border-bottom:1px dotted}b,strong{font-weight:bold}dfn{font-style:italic}h1{font-size:2em;margin:.67em 0}mark{background:#ff0;color:#000}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sup{top:-0.5em}sub{bottom:-0.25em}img{border:0}svg:not(:root){overflow:hidden}figure{margin:1em 40px}hr{box-sizing:content-box;height:0}pre{overflow:auto}code,kbd,pre,samp{font-family:monospace, monospace;font-size:1em}button,input,optgroup,select,textarea{color:inherit;font:inherit;margin:0}button{overflow:visible}button,select{text-transform:none}button,html input[type="button"],input[type="reset"]{-webkit-appearance:button;cursor:pointer}button[disabled],html input[disabled]{cursor:default}button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}input{line-height:normal}input[type='checkbox'],input[type='radio']{box-sizing:border-box;padding:0}input[type='number']::-webkit-inner-spin-button,input[type='number']::-webkit-outer-spin-button{height:auto}input[type='search']{-webkit-appearance:none}input[type='search']::-webkit-search-cancel-button,input[type='search']::-webkit-search-decoration{-webkit-appearance:none}fieldset{border:1px solid #c0c0c0;margin:0 2px;padding:.35em .625em .75em}legend{border:0;padding:0}textarea{overflow:auto}optgroup{font-weight:bold}table{border-collapse:collapse;border-spacing:0}td,th{padding:0}

/* ==========================================================================
   Start of base Webflow CSS - If you're looking for some ultra-clean CSS, skip the boilerplate and see the unminified code below.
   ========================================================================== */
@font-face{font-family:'webflow-icons';src:url("data:application/x-font-ttf;charset=utf-8;base64,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") format('truetype');font-weight:normal;font-style:normal}[class^="w-icon-"],[class*=" w-icon-"]{font-family:'webflow-icons' !important;speak:none;font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.w-icon-slider-right:before{content:"\e600"}.w-icon-slider-left:before{content:"\e601"}.w-icon-nav-menu:before{content:"\e602"}.w-icon-arrow-down:before,.w-icon-dropdown-toggle:before{content:"\e603"}.w-icon-file-upload-remove:before{content:"\e900"}.w-icon-file-upload-icon:before{content:"\e903"}*{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}html{height:100%}body{margin:0;min-height:100%;background-color:#fff;font-family:Arial,sans-serif;font-size:14px;line-height:20px;color:#333}img{max-width:100%;vertical-align:middle;display:inline-block}html.w-mod-touch *{background-attachment:scroll !important}.w-block{display:block}.w-inline-block{max-width:100%;display:inline-block}.w-clearfix:before,.w-clearfix:after{content:" ";display:table;grid-column-start:1;grid-row-start:1;grid-column-end:2;grid-row-end:2}.w-clearfix:after{clear:both}.w-hidden{display:none}.w-button{display:inline-block;padding:9px 15px;background-color:#3898EC;color:white;border:0;line-height:inherit;text-decoration:none;cursor:pointer;border-radius:0}input.w-button{-webkit-appearance:button}html[data-w-dynpage] [data-w-cloak]{color:transparent !important}.w-webflow-badge,.w-webflow-badge *{position:static;left:auto;top:auto;right:auto;bottom:auto;z-index:auto;display:block;visibility:visible;overflow:visible;overflow-x:visible;overflow-y:visible;box-sizing:border-box;width:auto;height:auto;max-height:none;max-width:none;min-height:0;min-width:0;margin:0;padding:0;float:none;clear:none;border:0 none transparent;border-radius:0;background:none;background-image:none;background-position:0% 0%;background-size:auto auto;background-repeat:repeat;background-origin:padding-box;background-clip:border-box;background-attachment:scroll;background-color:transparent;box-shadow:none;opacity:1;transform:none;transition:none;direction:ltr;font-family:inherit;font-weight:inherit;color:inherit;font-size:inherit;line-height:inherit;font-style:inherit;font-variant:inherit;text-align:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:0;text-transform:inherit;list-style-type:disc;text-shadow:none;font-smoothing:auto;vertical-align:baseline;cursor:inherit;white-space:inherit;word-break:normal;word-spacing:normal;word-wrap:normal}.w-webflow-badge{position:fixed !important;display:inline-block !important;visibility:visible !important;z-index:2147483647 !important;top:auto !important;right:12px !important;bottom:12px !important;left:auto !important;color:#AAADB0 !important;background-color:#fff !important;border-radius:3px !important;padding:6px 8px 6px 6px !important;font-size:12px !important;opacity:1 !important;line-height:14px !important;text-decoration:none !important;transform:none !important;margin:0 !important;width:auto !important;height:auto !important;overflow:visible !important;white-space:nowrap;box-shadow:0 0 0 1px rgba(0,0,0,0.1),0 1px 3px rgba(0,0,0,0.1);cursor:pointer}.w-webflow-badge>img{display:inline-block !important;visibility:visible !important;opacity:1 !important;vertical-align:middle !important}h1,h2,h3,h4,h5,h6{font-weight:bold;margin-bottom:10px}h1{font-size:38px;line-height:44px;margin-top:20px}h2{font-size:32px;line-height:36px;margin-top:20px}h3{font-size:24px;line-height:30px;margin-top:20px}h4{font-size:18px;line-height:24px;margin-top:10px}h5{font-size:14px;line-height:20px;margin-top:10px}h6{font-size:12px;line-height:18px;margin-top:10px}p{margin-top:0;margin-bottom:10px}blockquote{margin:0 0 10px 0;padding:10px 20px;border-left:5px solid #E2E2E2;font-size:18px;line-height:22px}figure{margin:0;margin-bottom:10px}figcaption{margin-top:5px;text-align:center}ul,ol{margin-top:0px;margin-bottom:10px;padding-left:40px}.w-list-unstyled{padding-left:0;list-style:none}.w-embed:before,.w-embed:after{content:" ";display:table;grid-column-start:1;grid-row-start:1;grid-column-end:2;grid-row-end:2}.w-embed:after{clear:both}.w-video{width:100%;position:relative;padding:0}.w-video iframe,.w-video object,.w-video embed{position:absolute;top:0;left:0;width:100%;height:100%;border:none}fieldset{padding:0;margin:0;border:0}button,[type='button'],[type='reset']{border:0;cursor:pointer;-webkit-appearance:button}.w-form{margin:0 0 15px}.w-form-done{display:none;padding:20px;text-align:center;background-color:#dddddd}.w-form-fail{display:none;margin-top:10px;padding:10px;background-color:#ffdede}label{display:block;margin-bottom:5px;font-weight:bold}.w-input,.w-select{display:block;width:100%;height:38px;padding:8px 12px;margin-bottom:10px;font-size:14px;line-height:1.42857143;color:#333333;vertical-align:middle;background-color:#ffffff;border:1px solid #cccccc}.w-input:-moz-placeholder,.w-select:-moz-placeholder{color:#999}.w-input::-moz-placeholder,.w-select::-moz-placeholder{color:#999;opacity:1}.w-input:-ms-input-placeholder,.w-select:-ms-input-placeholder{color:#999}.w-input::-webkit-input-placeholder,.w-select::-webkit-input-placeholder{color:#999}.w-input:focus,.w-select:focus{border-color:#3898EC;outline:0}.w-input[disabled],.w-select[disabled],.w-input[readonly],.w-select[readonly],fieldset[disabled] .w-input,fieldset[disabled] .w-select{cursor:not-allowed}.w-input[disabled]:not(.w-input-disabled),.w-select[disabled]:not(.w-input-disabled),.w-input[readonly],.w-select[readonly],fieldset[disabled]:not(.w-input-disabled) .w-input,fieldset[disabled]:not(.w-input-disabled) .w-select{background-color:#eeeeee}textarea.w-input,textarea.w-select{height:auto}.w-select{background-color:#f3f3f3}.w-select[multiple]{height:auto}.w-form-label{display:inline-block;cursor:pointer;font-weight:normal;margin-bottom:0px}.w-radio{display:block;margin-bottom:5px;padding-left:20px}.w-radio:before,.w-radio:after{content:" ";display:table;grid-column-start:1;grid-row-start:1;grid-column-end:2;grid-row-end:2}.w-radio:after{clear:both}.w-radio-input{margin:4px 0 0;margin-top:1px \9;line-height:normal;float:left;margin-left:-20px}.w-radio-input{margin-top:3px}.w-file-upload{display:block;margin-bottom:10px}.w-file-upload-input{width:.1px;height:.1px;opacity:0;overflow:hidden;position:absolute;z-index:-100}.w-file-upload-default,.w-file-upload-uploading,.w-file-upload-success{display:inline-block;color:#333333}.w-file-upload-error{display:block;margin-top:10px}.w-file-upload-default.w-hidden,.w-file-upload-uploading.w-hidden,.w-file-upload-error.w-hidden,.w-file-upload-success.w-hidden{display:none}.w-file-upload-uploading-btn{display:flex;font-size:14px;font-weight:normal;cursor:pointer;margin:0;padding:8px 12px;border:1px solid #cccccc;background-color:#fafafa}.w-file-upload-file{display:flex;flex-grow:1;justify-content:space-between;margin:0;padding:8px 9px 8px 11px;border:1px solid #cccccc;background-color:#fafafa}.w-file-upload-file-name{font-size:14px;font-weight:normal;display:block}.w-file-remove-link{margin-top:3px;margin-left:10px;width:auto;height:auto;padding:3px;display:block;cursor:pointer}.w-icon-file-upload-remove{margin:auto;font-size:10px}.w-file-upload-error-msg{display:inline-block;color:#ea384c;padding:2px 0}.w-file-upload-info{display:inline-block;line-height:38px;padding:0 12px}.w-file-upload-label{display:inline-block;font-size:14px;font-weight:normal;cursor:pointer;margin:0;padding:8px 12px;border:1px solid #cccccc;background-color:#fafafa}.w-icon-file-upload-icon,.w-icon-file-upload-uploading{display:inline-block;margin-right:8px;width:20px}.w-icon-file-upload-uploading{height:20px}.w-container{margin-left:auto;margin-right:auto;max-width:940px}.w-container:before,.w-container:after{content:" ";display:table;grid-column-start:1;grid-row-start:1;grid-column-end:2;grid-row-end:2}.w-container:after{clear:both}.w-container .w-row{margin-left:-10px;margin-right:-10px}.w-row:before,.w-row:after{content:" ";display:table;grid-column-start:1;grid-row-start:1;grid-column-end:2;grid-row-end:2}.w-row:after{clear:both}.w-row .w-row{margin-left:0;margin-right:0}.w-col{position:relative;float:left;width:100%;min-height:1px;padding-left:10px;padding-right:10px}.w-col .w-col{padding-left:0;padding-right:0}.w-col-1{width:8.33333333%}.w-col-2{width:16.66666667%}.w-col-3{width:25%}.w-col-4{width:33.33333333%}.w-col-5{width:41.66666667%}.w-col-6{width:50%}.w-col-7{width:58.33333333%}.w-col-8{width:66.66666667%}.w-col-9{width:75%}.w-col-10{width:83.33333333%}.w-col-11{width:91.66666667%}.w-col-12{width:100%}.w-hidden-main{display:none !important}@media screen and (max-width:991px){.w-container{max-width:728px}.w-hidden-main{display:inherit !important}.w-hidden-medium{display:none !important}.w-col-medium-1{width:8.33333333%}.w-col-medium-2{width:16.66666667%}.w-col-medium-3{width:25%}.w-col-medium-4{width:33.33333333%}.w-col-medium-5{width:41.66666667%}.w-col-medium-6{width:50%}.w-col-medium-7{width:58.33333333%}.w-col-medium-8{width:66.66666667%}.w-col-medium-9{width:75%}.w-col-medium-10{width:83.33333333%}.w-col-medium-11{width:91.66666667%}.w-col-medium-12{width:100%}.w-col-stack{width:100%;left:auto;right:auto}}@media screen and (max-width:767px){.w-hidden-main{display:inherit !important}.w-hidden-medium{display:inherit !important}.w-hidden-small{display:none !important}.w-row,.w-container .w-row{margin-left:0;margin-right:0}.w-col{width:100%;left:auto;right:auto}.w-col-small-1{width:8.33333333%}.w-col-small-2{width:16.66666667%}.w-col-small-3{width:25%}.w-col-small-4{width:33.33333333%}.w-col-small-5{width:41.66666667%}.w-col-small-6{width:50%}.w-col-small-7{width:58.33333333%}.w-col-small-8{width:66.66666667%}.w-col-small-9{width:75%}.w-col-small-10{width:83.33333333%}.w-col-small-11{width:91.66666667%}.w-col-small-12{width:100%}}@media screen and (max-width:479px){.w-container{max-width:none}.w-hidden-main{display:inherit !important}.w-hidden-medium{display:inherit !important}.w-hidden-small{display:inherit !important}.w-hidden-tiny{display:none !important}.w-col{width:100%}.w-col-tiny-1{width:8.33333333%}.w-col-tiny-2{width:16.66666667%}.w-col-tiny-3{width:25%}.w-col-tiny-4{width:33.33333333%}.w-col-tiny-5{width:41.66666667%}.w-col-tiny-6{width:50%}.w-col-tiny-7{width:58.33333333%}.w-col-tiny-8{width:66.66666667%}.w-col-tiny-9{width:75%}.w-col-tiny-10{width:83.33333333%}.w-col-tiny-11{width:91.66666667%}.w-col-tiny-12{width:100%}}.w-widget{position:relative}.w-widget-map{width:100%;height:400px}.w-widget-map label{width:auto;display:inline}.w-widget-map img{max-width:inherit}.w-widget-map .gm-style-iw{text-align:center}.w-widget-map .gm-style-iw>button{display:none !important}.w-widget-twitter{overflow:hidden}.w-widget-twitter-count-shim{display:inline-block;vertical-align:top;position:relative;width:28px;height:20px;text-align:center;background:white;border:#758696 solid 1px;border-radius:3px}.w-widget-twitter-count-shim *{pointer-events:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.w-widget-twitter-count-shim .w-widget-twitter-count-inner{position:relative;font-size:15px;line-height:12px;text-align:center;color:#999;font-family:serif}.w-widget-twitter-count-shim .w-widget-twitter-count-clear{position:relative;display:block}.w-widget-twitter-count-shim.w--large{width:36px;height:28px}.w-widget-twitter-count-shim.w--large .w-widget-twitter-count-inner{font-size:18px;line-height:18px}.w-widget-twitter-count-shim:not(.w--vertical){margin-left:5px;margin-right:8px}.w-widget-twitter-count-shim:not(.w--vertical).w--large{margin-left:6px}.w-widget-twitter-count-shim:not(.w--vertical):before,.w-widget-twitter-count-shim:not(.w--vertical):after{top:50%;left:0;border:solid transparent;content:' ';height:0;width:0;position:absolute;pointer-events:none}.w-widget-twitter-count-shim:not(.w--vertical):before{border-color:rgba(117,134,150,0);border-right-color:#5d6c7b;border-width:4px;margin-left:-9px;margin-top:-4px}.w-widget-twitter-count-shim:not(.w--vertical).w--large:before{border-width:5px;margin-left:-10px;margin-top:-5px}.w-widget-twitter-count-shim:not(.w--vertical):after{border-color:rgba(255,255,255,0);border-right-color:white;border-width:4px;margin-left:-8px;margin-top:-4px}.w-widget-twitter-count-shim:not(.w--vertical).w--large:after{border-width:5px;margin-left:-9px;margin-top:-5px}.w-widget-twitter-count-shim.w--vertical{width:61px;height:33px;margin-bottom:8px}.w-widget-twitter-count-shim.w--vertical:before,.w-widget-twitter-count-shim.w--vertical:after{top:100%;left:50%;border:solid transparent;content:' ';height:0;width:0;position:absolute;pointer-events:none}.w-widget-twitter-count-shim.w--vertical:before{border-color:rgba(117,134,150,0);border-top-color:#5d6c7b;border-width:5px;margin-left:-5px}.w-widget-twitter-count-shim.w--vertical:after{border-color:rgba(255,255,255,0);border-top-color:white;border-width:4px;margin-left:-4px}.w-widget-twitter-count-shim.w--vertical .w-widget-twitter-count-inner{font-size:18px;line-height:22px}.w-widget-twitter-count-shim.w--vertical.w--large{width:76px}.w-background-video{position:relative;overflow:hidden;height:500px;color:white}.w-background-video>video{background-size:cover;background-position:50% 50%;position:absolute;margin:auto;width:100%;height:100%;right:-100%;bottom:-100%;top:-100%;left:-100%;object-fit:cover;z-index:-100}.w-background-video>video::-webkit-media-controls-start-playback-button{display:none !important;-webkit-appearance:none}.w-background-video--control{position:absolute;bottom:1em;right:1em;background-color:transparent;padding:0}.w-background-video--control>[hidden]{display:none !important}.w-slider{position:relative;height:300px;text-align:center;background:#dddddd;clear:both;-webkit-tap-highlight-color:rgba(0,0,0,0);tap-highlight-color:rgba(0,0,0,0)}.w-slider-mask{position:relative;display:block;overflow:hidden;z-index:1;left:0;right:0;height:100%;white-space:nowrap}.w-slide{position:relative;display:inline-block;vertical-align:top;width:100%;height:100%;white-space:normal;text-align:left}.w-slider-nav{position:absolute;z-index:2;top:auto;right:0;bottom:0;left:0;margin:auto;padding-top:10px;height:40px;text-align:center;-webkit-tap-highlight-color:rgba(0,0,0,0);tap-highlight-color:rgba(0,0,0,0)}.w-slider-nav.w-round>div{border-radius:100%}.w-slider-nav.w-num>div{width:auto;height:auto;padding:.2em .5em;font-size:inherit;line-height:inherit}.w-slider-nav.w-shadow>div{box-shadow:0 0 3px rgba(51,51,51,0.4)}.w-slider-nav-invert{color:#fff}.w-slider-nav-invert>div{background-color:rgba(34,34,34,0.4)}.w-slider-nav-invert>div.w-active{background-color:#222}.w-slider-dot{position:relative;display:inline-block;width:1em;height:1em;background-color:rgba(255,255,255,0.4);cursor:pointer;margin:0 3px .5em;transition:background-color 100ms,color 100ms}.w-slider-dot.w-active{background-color:#fff}.w-slider-dot:focus{outline:none;box-shadow:0 0 0 2px #fff}.w-slider-dot:focus.w-active{box-shadow:none}.w-slider-arrow-left,.w-slider-arrow-right{position:absolute;width:80px;top:0;right:0;bottom:0;left:0;margin:auto;cursor:pointer;overflow:hidden;color:white;font-size:40px;-webkit-tap-highlight-color:rgba(0,0,0,0);tap-highlight-color:rgba(0,0,0,0);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.w-slider-arrow-left [class^='w-icon-'],.w-slider-arrow-right [class^='w-icon-'],.w-slider-arrow-left [class*=' w-icon-'],.w-slider-arrow-right [class*=' w-icon-']{position:absolute}.w-slider-arrow-left:focus,.w-slider-arrow-right:focus{outline:0}.w-slider-arrow-left{z-index:3;right:auto}.w-slider-arrow-right{z-index:4;left:auto}.w-icon-slider-left,.w-icon-slider-right{top:0;right:0;bottom:0;left:0;margin:auto;width:1em;height:1em}.w-slider-aria-label{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}.w-slider-force-show{display:block !important}.w-dropdown{display:inline-block;position:relative;text-align:left;margin-left:auto;margin-right:auto;z-index:900}.w-dropdown-btn,.w-dropdown-toggle,.w-dropdown-link{position:relative;vertical-align:top;text-decoration:none;color:#222222;padding:20px;text-align:left;margin-left:auto;margin-right:auto;white-space:nowrap}.w-dropdown-toggle{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:inline-block;cursor:pointer;padding-right:40px}.w-dropdown-toggle:focus{outline:0}.w-icon-dropdown-toggle{position:absolute;top:0;right:0;bottom:0;margin:auto;margin-right:20px;width:1em;height:1em}.w-dropdown-list{position:absolute;background:#dddddd;display:none;min-width:100%}.w-dropdown-list.w--open{display:block}.w-dropdown-link{padding:10px 20px;display:block;color:#222222}.w-dropdown-link.w--current{color:#0082f3}.w-dropdown-link:focus{outline:0}@media screen and (max-width:767px){.w-nav-brand{padding-left:10px}}.w-lightbox-backdrop{color:#000;cursor:auto;font-family:serif;font-size:medium;font-style:normal;font-variant:normal;font-weight:normal;letter-spacing:normal;line-height:normal;list-style:disc;text-align:start;text-indent:0;text-shadow:none;text-transform:none;visibility:visible;white-space:normal;word-break:normal;word-spacing:normal;word-wrap:normal;position:fixed;top:0;right:0;bottom:0;left:0;color:#fff;font-family:"Helvetica Neue",Helvetica,Ubuntu,"Segoe UI",Verdana,sans-serif;font-size:17px;line-height:1.2;font-weight:300;text-align:center;background:rgba(0,0,0,0.9);z-index:2000;outline:0;opacity:0;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;-webkit-tap-highlight-color:transparent;-webkit-transform:translate(0, 0)}.w-lightbox-backdrop,.w-lightbox-container{height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.w-lightbox-content{position:relative;height:100vh;overflow:hidden}.w-lightbox-view{position:absolute;width:100vw;height:100vh;opacity:0}.w-lightbox-view:before{content:"";height:100vh}.w-lightbox-group,.w-lightbox-group .w-lightbox-view,.w-lightbox-group .w-lightbox-view:before{height:86vh}.w-lightbox-frame,.w-lightbox-view:before{display:inline-block;vertical-align:middle}.w-lightbox-figure{position:relative;margin:0}.w-lightbox-group .w-lightbox-figure{cursor:pointer}.w-lightbox-img{width:auto;height:auto;max-width:none}.w-lightbox-image{display:block;float:none;max-width:100vw;max-height:100vh}.w-lightbox-group .w-lightbox-image{max-height:86vh}.w-lightbox-caption{position:absolute;right:0;bottom:0;left:0;padding:.5em 1em;background:rgba(0,0,0,0.4);text-align:left;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.w-lightbox-embed{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;height:100%}.w-lightbox-control{position:absolute;top:0;width:4em;background-size:24px;background-repeat:no-repeat;background-position:center;cursor:pointer;-webkit-transition:all .3s;transition:all .3s}.w-lightbox-left{display:none;bottom:0;left:0;background-image:url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii0yMCAwIDI0IDQwIiB3aWR0aD0iMjQiIGhlaWdodD0iNDAiPjxnIHRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHBhdGggZD0ibTAgMGg1djIzaDIzdjVoLTI4eiIgb3BhY2l0eT0iLjQiLz48cGF0aCBkPSJtMSAxaDN2MjNoMjN2M2gtMjZ6IiBmaWxsPSIjZmZmIi8+PC9nPjwvc3ZnPg==")}.w-lightbox-right{display:none;right:0;bottom:0;background-image:url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii00IDAgMjQgNDAiIHdpZHRoPSIyNCIgaGVpZ2h0PSI0MCI+PGcgdHJhbnNmb3JtPSJyb3RhdGUoNDUpIj48cGF0aCBkPSJtMC0waDI4djI4aC01di0yM2gtMjN6IiBvcGFjaXR5PSIuNCIvPjxwYXRoIGQ9Im0xIDFoMjZ2MjZoLTN2LTIzaC0yM3oiIGZpbGw9IiNmZmYiLz48L2c+PC9zdmc+")}.w-lightbox-close{right:0;height:2.6em;background-image:url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii00IDAgMTggMTciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxNyI+PGcgdHJhbnNmb3JtPSJyb3RhdGUoNDUpIj48cGF0aCBkPSJtMCAwaDd2LTdoNXY3aDd2NWgtN3Y3aC01di03aC03eiIgb3BhY2l0eT0iLjQiLz48cGF0aCBkPSJtMSAxaDd2LTdoM3Y3aDd2M2gtN3Y3aC0zdi03aC03eiIgZmlsbD0iI2ZmZiIvPjwvZz48L3N2Zz4=");background-size:18px}.w-lightbox-strip{position:absolute;bottom:0;left:0;right:0;padding:0 1vh;line-height:0;white-space:nowrap;overflow-x:auto;overflow-y:hidden}.w-lightbox-item{display:inline-block;width:10vh;padding:2vh 1vh;box-sizing:content-box;cursor:pointer;-webkit-transform:translate3d(0, 0, 0)}.w-lightbox-active{opacity:.3}.w-lightbox-thumbnail{position:relative;height:10vh;background:#222;overflow:hidden}.w-lightbox-thumbnail-image{position:absolute;top:0;left:0}.w-lightbox-thumbnail .w-lightbox-tall{top:50%;width:100%;-webkit-transform:translate(0, -50%);-ms-transform:translate(0, -50%);transform:translate(0, -50%)}.w-lightbox-thumbnail .w-lightbox-wide{left:50%;height:100%;-webkit-transform:translate(-50%, 0);-ms-transform:translate(-50%, 0);transform:translate(-50%, 0)}.w-lightbox-spinner{position:absolute;top:50%;left:50%;box-sizing:border-box;width:40px;height:40px;margin-top:-20px;margin-left:-20px;border:5px solid rgba(0,0,0,0.4);border-radius:50%;-webkit-animation:spin .8s infinite linear;animation:spin .8s infinite linear}.w-lightbox-spinner:after{content:"";position:absolute;top:-4px;right:-4px;bottom:-4px;left:-4px;border:3px solid transparent;border-bottom-color:#fff;border-radius:50%}.w-lightbox-hide{display:none}.w-lightbox-noscroll{overflow:hidden}@media (min-width:768px){.w-lightbox-content{height:96vh;margin-top:2vh}.w-lightbox-view,.w-lightbox-view:before{height:96vh}.w-lightbox-group,.w-lightbox-group .w-lightbox-view,.w-lightbox-group .w-lightbox-view:before{height:84vh}.w-lightbox-image{max-width:96vw;max-height:96vh}.w-lightbox-group .w-lightbox-image{max-width:82.3vw;max-height:84vh}.w-lightbox-left,.w-lightbox-right{display:block;opacity:.5}.w-lightbox-close{opacity:.8}.w-lightbox-control:hover{opacity:1}}.w-lightbox-inactive,.w-lightbox-inactive:hover{opacity:0}.w-richtext:before,.w-richtext:after{content:" ";display:table;grid-column-start:1;grid-row-start:1;grid-column-end:2;grid-row-end:2}.w-richtext:after{clear:both}.w-richtext[contenteditable="true"]:before,.w-richtext[contenteditable="true"]:after{white-space:initial}.w-richtext ol,.w-richtext ul{overflow:hidden}.w-richtext .w-richtext-figure-selected.w-richtext-figure-type-video div:after,.w-richtext .w-richtext-figure-selected[data-rt-type="video"] div:after{outline:2px solid #2895f7}.w-richtext .w-richtext-figure-selected.w-richtext-figure-type-image div,.w-richtext .w-richtext-figure-selected[data-rt-type="image"] div{outline:2px solid #2895f7}.w-richtext figure.w-richtext-figure-type-video>div:after,.w-richtext figure[data-rt-type="video"]>div:after{content:'';position:absolute;display:none;left:0;top:0;right:0;bottom:0}.w-richtext figure{position:relative;max-width:60%}.w-richtext figure>div:before{cursor:default!important}.w-richtext figure img{width:100%}.w-richtext figure figcaption.w-richtext-figcaption-placeholder{opacity:.6}.w-richtext figure div{font-size:0px;color:transparent}.w-richtext figure.w-richtext-figure-type-image,.w-richtext figure[data-rt-type="image"]{display:table}.w-richtext figure.w-richtext-figure-type-image>div,.w-richtext figure[data-rt-type="image"]>div{display:inline-block}.w-richtext figure.w-richtext-figure-type-image>figcaption,.w-richtext figure[data-rt-type="image"]>figcaption{display:table-caption;caption-side:bottom}.w-richtext figure.w-richtext-figure-type-video,.w-richtext figure[data-rt-type="video"]{width:60%;height:0}.w-richtext figure.w-richtext-figure-type-video iframe,.w-richtext figure[data-rt-type="video"] iframe{position:absolute;top:0;left:0;width:100%;height:100%}.w-richtext figure.w-richtext-figure-type-video>div,.w-richtext figure[data-rt-type="video"]>div{width:100%}.w-richtext figure.w-richtext-align-center{margin-right:auto;margin-left:auto;clear:both}.w-richtext figure.w-richtext-align-center.w-richtext-figure-type-image>div,.w-richtext figure.w-richtext-align-center[data-rt-type="image"]>div{max-width:100%}.w-richtext figure.w-richtext-align-normal{clear:both}.w-richtext figure.w-richtext-align-fullwidth{width:100%;max-width:100%;text-align:center;clear:both;display:block;margin-right:auto;margin-left:auto}.w-richtext figure.w-richtext-align-fullwidth>div{display:inline-block;padding-bottom:inherit}.w-richtext figure.w-richtext-align-fullwidth>figcaption{display:block}.w-richtext figure.w-richtext-align-floatleft{float:left;margin-right:15px;clear:none}.w-richtext figure.w-richtext-align-floatright{float:right;margin-left:15px;clear:none}.w-nav{position:relative;background:#dddddd;z-index:1000}.w-nav:before,.w-nav:after{content:" ";display:table;grid-column-start:1;grid-row-start:1;grid-column-end:2;grid-row-end:2}.w-nav:after{clear:both}.w-nav-brand{position:relative;float:left;text-decoration:none;color:#333333}.w-nav-link{position:relative;display:inline-block;vertical-align:top;text-decoration:none;color:#222222;padding:20px;text-align:left;margin-left:auto;margin-right:auto}.w-nav-link.w--current{color:#0082f3}.w-nav-menu{position:relative;float:right}[data-nav-menu-open]{display:block !important;position:absolute;top:100%;left:0;right:0;background:#C8C8C8;text-align:center;overflow:visible;min-width:200px}.w--nav-link-open{display:block;position:relative}.w-nav-overlay{position:absolute;overflow:hidden;display:none;top:100%;left:0;right:0;width:100%}.w-nav-overlay [data-nav-menu-open]{top:0}.w-nav[data-animation="over-left"] .w-nav-overlay{width:auto}.w-nav[data-animation="over-left"] .w-nav-overlay,.w-nav[data-animation="over-left"] [data-nav-menu-open]{right:auto;z-index:1;top:0}.w-nav[data-animation="over-right"] .w-nav-overlay{width:auto}.w-nav[data-animation="over-right"] .w-nav-overlay,.w-nav[data-animation="over-right"] [data-nav-menu-open]{left:auto;z-index:1;top:0}.w-nav-button{position:relative;float:right;padding:18px;font-size:24px;display:none;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);tap-highlight-color:rgba(0,0,0,0);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.w-nav-button:focus{outline:0}.w-nav-button.w--open{background-color:#C8C8C8;color:white}.w-nav[data-collapse="all"] .w-nav-menu{display:none}.w-nav[data-collapse="all"] .w-nav-button{display:block}.w--nav-dropdown-open{display:block}.w--nav-dropdown-toggle-open{display:block}.w--nav-dropdown-list-open{position:static}@media screen and (max-width:991px){.w-nav[data-collapse="medium"] .w-nav-menu{display:none}.w-nav[data-collapse="medium"] .w-nav-button{display:block}}@media screen and (max-width:767px){.w-nav[data-collapse="small"] .w-nav-menu{display:none}.w-nav[data-collapse="small"] .w-nav-button{display:block}.w-nav-brand{padding-left:10px}}@media screen and (max-width:479px){.w-nav[data-collapse="tiny"] .w-nav-menu{display:none}.w-nav[data-collapse="tiny"] .w-nav-button{display:block}}.w-tabs{position:relative}.w-tabs:before,.w-tabs:after{content:" ";display:table;grid-column-start:1;grid-row-start:1;grid-column-end:2;grid-row-end:2}.w-tabs:after{clear:both}.w-tab-menu{position:relative}.w-tab-link{position:relative;display:inline-block;vertical-align:top;text-decoration:none;padding:9px 30px;text-align:left;cursor:pointer;color:#222222;background-color:#dddddd}.w-tab-link.w--current{background-color:#C8C8C8}.w-tab-link:focus{outline:0}.w-tab-content{position:relative;display:block;overflow:hidden}.w-tab-pane{position:relative;display:none}.w--tab-active{display:block}@media screen and (max-width:479px){.w-tab-link{display:block}}.w-ix-emptyfix:after{content:""}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}.w-dyn-empty{padding:10px;background-color:#dddddd}.w-dyn-hide{display:none !important}.w-dyn-bind-empty{display:none !important}.w-condition-invisible{display:none !important}

/* ==========================================================================
   Start of custom Webflow CSS
   ========================================================================== */
.w-layout-grid {
  display: -ms-grid;
  display: grid;
  grid-auto-columns: 1fr;
  -ms-grid-columns: 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-grid-rows: auto auto;
  grid-template-rows: auto auto;
  grid-row-gap: 16px;
  grid-column-gap: 16px;
}

.w-form-formradioinput--inputType-custom {
  border-top-width: 1px;
  border-bottom-width: 1px;
  border-left-width: 1px;
  border-right-width: 1px;
  border-top-color: #ccc;
  border-bottom-color: #ccc;
  border-left-color: #ccc;
  border-right-color: #ccc;
  border-top-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-right-style: solid;
  width: 12px;
  height: 12px;
  border-bottom-left-radius: 50%;
  border-bottom-right-radius: 50%;
  border-top-left-radius: 50%;
  border-top-right-radius: 50%;
}

.w-form-formradioinput--inputType-custom.w--redirected-focus {
  box-shadow: 0px 0px 3px 1px #3898ec;
}

.w-form-formradioinput--inputType-custom.w--redirected-checked {
  border-top-width: 4px;
  border-bottom-width: 4px;
  border-left-width: 4px;
  border-right-width: 4px;
  border-top-color: #3898ec;
  border-bottom-color: #3898ec;
  border-left-color: #3898ec;
  border-right-color: #3898ec;
}

.w-checkbox {
  display: block;
  margin-bottom: 5px;
  padding-left: 20px;
}

.w-checkbox::before {
  content: ' ';
  display: table;
  -ms-grid-column-span: 1;
  grid-column-end: 2;
  -ms-grid-column: 1;
  grid-column-start: 1;
  -ms-grid-row-span: 1;
  grid-row-end: 2;
  -ms-grid-row: 1;
  grid-row-start: 1;
}

.w-checkbox::after {
  content: ' ';
  display: table;
  -ms-grid-column-span: 1;
  grid-column-end: 2;
  -ms-grid-column: 1;
  grid-column-start: 1;
  -ms-grid-row-span: 1;
  grid-row-end: 2;
  -ms-grid-row: 1;
  grid-row-start: 1;
  clear: both;
}

.w-checkbox-input {
  float: left;
  margin-bottom: 0px;
  margin-left: -20px;
  margin-right: 0px;
  margin-top: 4px;
  line-height: normal;
}

.w-checkbox-input--inputType-custom {
  border-top-width: 1px;
  border-bottom-width: 1px;
  border-left-width: 1px;
  border-right-width: 1px;
  border-top-color: #ccc;
  border-bottom-color: #ccc;
  border-left-color: #ccc;
  border-right-color: #ccc;
  border-top-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-right-style: solid;
  width: 12px;
  height: 12px;
  border-bottom-left-radius: 2px;
  border-bottom-right-radius: 2px;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
}

.w-checkbox-input--inputType-custom.w--redirected-checked {
  background-color: #3898ec;
  border-top-color: #3898ec;
  border-bottom-color: #3898ec;
  border-left-color: #3898ec;
  border-right-color: #3898ec;
  background-image: url(https://d3e54v103j8qbb.cloudfront.net/static/custom-checkbox-checkmark.589d534424.svg);
  background-position: 50% 50%;
  background-size: cover;
  background-repeat: no-repeat;
}

.w-checkbox-input--inputType-custom.w--redirected-focus {
  box-shadow: 0px 0px 3px 1px #3898ec;
}

body {
  background-color: #fff;
  font-family: Inter, sans-serif;
  color: #1f1f1f;
  font-size: 16px;
  line-height: 20px;
  letter-spacing: -0.2px;
}

h1 {
  margin-top: 0px;
  margin-bottom: 24px;
  font-size: 45px;
  line-height: 1.25em;
  font-weight: 500;
}

h2 {
  margin-top: 0px;
  margin-bottom: 16px;
  font-size: 34px;
  line-height: 1.2em;
  font-weight: 500;
}

h3 {
  margin-top: 0px;
  margin-bottom: 16px;
  font-size: 26px;
  line-height: 1.4em;
}

h4 {
  margin-top: 0px;
  margin-bottom: 12px;
  font-size: 22px;
  line-height: 1.4em;
  font-weight: 500;
}

h5 {
  margin-top: 0px;
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 1.5em;
  font-weight: 500;
}

h6 {
  margin-top: 0px;
  margin-bottom: 5px;
  color: #99a4af;
  font-size: 13px;
  line-height: 1.5em;
  font-weight: 700;
}

p {
  margin-bottom: 16px;
  font-size: 16px;
  line-height: 1.5em;
  font-weight: 400;
}

a {
  -webkit-transition: color 200ms ease;
  transition: color 200ms ease;
  color: #3878ff;
  text-decoration: none;
}

ul {
  margin-top: 0px;
  margin-bottom: 16px;
  padding-left: 20px;
}

li {
  margin-bottom: 8px;
  opacity: 0.9;
  line-height: 1.4em;
}

img {
  display: inline-block;
  max-width: 100%;
}

label {
  display: block;
  margin-bottom: 6px;
  color: #626a72;
  font-size: 15px;
  font-weight: 400;
}

strong {
  font-weight: 600;
}

em {
  font-style: italic;
  letter-spacing: -0.5px;
}

blockquote {
  margin-bottom: 16px;
  padding: 10px 20px;
  border-left: 5px solid #3878ff;
  color: #33383f;
  font-size: 18px;
  line-height: 1.4em;
}

.wrapper {
  width: 100%;
  min-height: 80vh;
}

.section {
  position: relative;
  z-index: 0;
  padding-top: 80px;
  padding-bottom: 80px;
}

.section.light-grey {
  position: relative;
  background-color: #f2f5f7;
}

.section.small {
  padding-top: 40px;
  padding-bottom: 40px;
}

.section.small.overflow-none {
  overflow: hidden;
}

.section.hero-blue {
  overflow: hidden;
  padding-top: 110px;
  padding-bottom: 110px;
  background-color: #3878ff;
  color: #fff;
}

.section.light-blue {
  background-color: #79a4ff;
}

.section.no-top-padding {
  padding-top: 0px;
}

.section.grey {
  background-color: #eaeef1;
}

.section.lime {
  background-color: #dbfa6b;
}

.section.light-grey {
  position: relative;
  background-color: #f7f9fa;
}

.section.no-padding {
  padding-top: 0px;
  padding-bottom: 0px;
}

.button {
  margin-bottom: 16px;
  padding: 10px 36px;
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  border-radius: 0px;
  background-color: #3878ff;
  -webkit-transition: border-color 200ms ease, color 200ms ease, background-color 200ms ease, -webkit-transform 200ms ease;
  transition: border-color 200ms ease, color 200ms ease, background-color 200ms ease, -webkit-transform 200ms ease;
  transition: transform 200ms ease, border-color 200ms ease, color 200ms ease, background-color 200ms ease;
  transition: transform 200ms ease, border-color 200ms ease, color 200ms ease, background-color 200ms ease, -webkit-transform 200ms ease;
  color: #fff;
  font-size: 16px;
  line-height: 1.5;
  text-align: center;
}

.button:hover {
  background-color: #1b5ce5;
  color: #fff;
}

.button:active {
  background-color: #1248bb;
  box-shadow: 0 0 0 0 rgba(32, 32, 32, 0);
  -webkit-transform: translate(0px, 3px);
  -ms-transform: translate(0px, 3px);
  transform: translate(0px, 3px);
}

.button.no-margin {
  margin-right: 0px;
  margin-left: 0px;
}

.button.grey {
  background-color: #f2f5f7;
  box-shadow: none;
  color: #626a72;
}

.button.grey:hover {
  background-color: #c2cdd8;
  color: #1f1f1f;
}

.button.grey:active {
  background-color: #99a4af;
  color: #33383f;
}

.button.disabled {
  background-color: #f2f5f7;
  box-shadow: none;
  opacity: 0.7;
  color: #c2cdd8;
}

.button.disabled:active {
  -webkit-transform: none;
  -ms-transform: none;
  transform: none;
}

.button.outline {
  background-color: transparent;
  box-shadow: inset 0 0 0 1px #3878ff;
  color: #3878ff;
}

.button.outline:hover {
  background-color: #e6f2ff;
}

.button.outline:active {
  background-color: #3878ff;
  box-shadow: inset 0 0 0 2px #3878ff;
  color: #fff;
}

.button.white {
  background-color: #fff;
  -webkit-transition: opacity 200ms ease, border-color 200ms ease, color 200ms ease, background-color 200ms ease, -webkit-transform 200ms ease;
  transition: opacity 200ms ease, border-color 200ms ease, color 200ms ease, background-color 200ms ease, -webkit-transform 200ms ease;
  transition: opacity 200ms ease, transform 200ms ease, border-color 200ms ease, color 200ms ease, background-color 200ms ease;
  transition: opacity 200ms ease, transform 200ms ease, border-color 200ms ease, color 200ms ease, background-color 200ms ease, -webkit-transform 200ms ease;
  color: #1f1f1f;
}

.button.white:hover {
  opacity: 0.9;
}

.spacer {
  height: 48px;
  background-color: #eaeef1;
}

.spacer._64 {
  width: 64px;
  height: 64px;
  background-color: transparent;
}

.spacer._32 {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background-color: transparent;
}

.spacer._24 {
  width: 24px;
  height: 24px;
  background-color: transparent;
  text-transform: uppercase;
}

.spacer._48 {
  width: 48px;
  border-radius: 6px;
  background-color: transparent;
}

.spacer._128 {
  width: 128px;
  height: 128px;
  background-color: transparent;
}

.spacer._80 {
  width: 80px;
  height: 80px;
  background-color: transparent;
}

.spacer._96 {
  width: 96px;
  height: 96px;
  background-color: transparent;
}

.spacer._96 {
  height: 96px;
  background-color: rgba(94, 178, 244, 0);
}

.spacer._32 {
  height: 32px;
  background-color: transparent;
}

.spacer._16 {
  width: 16px;
  height: 16px;
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  background-color: transparent;
}

.text-box {
  max-width: 650px;
}

.text-box.center-align {
  margin-right: auto;
  margin-left: auto;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  -ms-grid-row-align: center;
  align-self: center;
  text-align: center;
}

.text-box._550px {
  max-width: 550px;
}

.text-box._400px {
  max-width: 400px;
}

.text-box._500px {
  max-width: 500px;
}

.text-box.centered {
  margin-right: auto;
  margin-left: auto;
}

.text-box._750px {
  max-width: 750px;
}

.text-box._750px.center-align {
  text-align: center;
}

.text-box.center-align {
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  -ms-grid-row-align: center;
  align-self: center;
  text-align: center;
}

.text-box._450px {
  max-width: 450px;
}

.paragraph-small {
  color: #626a72;
  font-size: 13px;
  line-height: 1.5em;
  font-weight: 400;
}

._12-columns {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-right: -16px;
  margin-left: -16px;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-content: stretch;
  -ms-flex-line-pack: stretch;
  align-content: stretch;
}

._12-columns.flex-horizontal {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

._12-columns.align-top {
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
}

._12-columns.align-left {
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.container {
  position: relative;
  display: block;
  width: 100%;
  max-width: 1330px;
  min-height: 30px;
  margin-right: auto;
  margin-left: auto;
  padding-right: 50px;
  padding-left: 50px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.color-block {
  width: 100%;
  height: 60px;
  margin-right: 0px;
  margin-bottom: 10px;
  margin-left: 0px;
  background-color: #1f1f1f;
}

.color-block.blue {
  background-color: #3878ff;
}

.color-block.grey {
  background-color: #626a72;
}

.color-block.light-grey {
  background-color: #99a4af;
}

.color-block.hard-blue {
  background-color: #1248bb;
}

.color-block.dark-grey {
  background-color: #33383f;
}

.color-block.back-grey {
  border-style: solid;
  border-width: 1px;
  border-color: #eaeef1;
  background-color: #f2f5f7;
}

.color-block.soft-blue {
  background-color: #79a4ff;
}

.color-block.soft-grey {
  background-color: #c2cdd8;
}

.color-block.silver {
  background-color: #eaeef1;
}

.color-block.white {
  border-style: solid;
  border-width: 1px;
  border-color: #eaeef1;
  background-color: #fff;
}

.color-block.lime {
  background-color: #dbfa6b;
}

.color-block.dark-blue {
  background-color: #1b5ce5;
}

.white {
  color: #fff;
}

.column-styleguide {
  width: 100%;
  height: 40px;
  margin-bottom: 20px;
  border-radius: 2px;
  background-color: #eaeef1;
}

.column {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-height: 32px;
  padding-right: 16px;
  padding-left: 16px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  -webkit-box-flex: 0;
  -webkit-flex: 0 auto;
  -ms-flex: 0 auto;
  flex: 0 auto;
}

.column.desk-10 {
  width: 83.33%;
}

.column.desk-12 {
  width: 100%;
}

.column.desk-11 {
  width: 91.66%;
}

.column.desk-6 {
  width: 50%;
}

.column.desk-5 {
  width: 41.66%;
}

.column.desk-1 {
  width: 8.33%;
  -webkit-box-flex: 0;
  -webkit-flex: 0 auto;
  -ms-flex: 0 auto;
  flex: 0 auto;
}

.column.desk-4 {
  width: 33.33%;
}

.column.desk-9 {
  width: 75%;
}

.column.desk-3 {
  width: 25%;
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}

.column.desk-2 {
  width: 16.66%;
}

.column.desk-7 {
  width: 58.333333333333336%;
}

.column.desk-8 {
  width: 66.66666666666667%;
}

.ds-block {
  margin-bottom: 32px;
  font-size: 18px;
}

.horizontal-line {
  width: 100%;
  height: 1px;
  margin-bottom: 36px;
  background-color: #eaeef1;
}

.horizontal-line.design-system {
  position: relative;
  bottom: -80px;
}

.rich-text {
  text-align: left;
}

.rich-text h3 {
  margin-top: 24px;
}

.rich-text p {
  margin-bottom: 24px;
  line-height: 1.8em;
}

.rich-text h4 {
  margin-top: 32px;
}

.rich-text h2 {
  margin-top: 32px;
}

.nav-content {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-left: 24px;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.nav-cta-button-container {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.menu-button.w--open {
  background-color: #fff;
  color: rgba(9, 106, 208, 0.2);
}

.nav-bar {
  position: -webkit-sticky;
  position: sticky;
  top: 0px;
  z-index: 200;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #fff;
}

.logo-div {
  display: block;
  margin-top: 4px;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex: 0;
  -webkit-flex: 0 auto;
  -ms-flex: 0 auto;
  flex: 0 auto;
}

.nav-logo {
  -webkit-transition: opacity 200ms ease;
  transition: opacity 200ms ease;
}

.nav-logo:hover {
  opacity: 0.75;
}

.footer-logo {
  margin-bottom: 20px;
}

.footer-link {
  display: block;
  margin-bottom: 0px;
  padding-top: 5px;
  padding-bottom: 5px;
  -webkit-transition: color 200ms ease-in-out;
  transition: color 200ms ease-in-out;
  color: #626a72;
  font-size: 15px;
  text-decoration: none;
  cursor: pointer;
}

.footer-link:hover {
  opacity: 1;
  color: #3878ff;
}

.footer-link.w--current {
  opacity: 1;
}

.footer-links-container {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.footer {
  position: relative;
  z-index: 0;
  padding-top: 48px;
  padding-bottom: 32px;
}

.icon {
  width: 40px;
  margin-bottom: 16px;
  opacity: 0.9;
  color: #fff;
  font-size: 24px;
}

.icon.tab {
  width: 32px;
  height: 32px;
  margin-right: 16px;
  margin-bottom: 8px;
}

.banner-section {
  padding-right: 50px;
  padding-left: 50px;
  background-color: #f2f5f7;
  color: #1f1f1f;
}

.banner-container {
  display: block;
  max-width: 1080px;
  margin-right: auto;
  margin-left: auto;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.banner {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-top: 12px;
  padding-bottom: 12px;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  font-size: 14px;
  line-height: 1.4em;
  text-align: center;
}

.tab-text-box {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 64px;
  margin-bottom: -1px;
  padding: 16px 0px 8px;
  border-top: 1px solid #c2cdd8;
  border-bottom: 1px solid #c2cdd8;
  background-color: hsla(0, 0%, 100%, 0);
  -webkit-transition-property: all;
  transition-property: all;
  color: rgba(51, 56, 63, 0.49);
  font-style: normal;
  text-align: left;
}

.tab-text-box:hover {
  color: #33383f;
}

.tab-text-box.w--current {
  height: auto;
  background-color: transparent;
  color: #1f1f1f;
}

.tabs-text-boxes {
  width: 41.66666666666667%;
}

.tabs-images {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 58.333333333333336%;
  height: 400px;
  padding-left: 60px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
  -ms-flex-align: end;
  align-items: flex-end;
  text-align: center;
}

.horizontal-tabs {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
  -ms-flex-align: end;
  align-items: flex-end;
  grid-auto-columns: 1fr;
  -ms-grid-columns: 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.nav-dropdown {
  position: relative;
  margin-right: 2px;
  margin-left: 6px;
  padding: 5px 22px 5px 8px;
  border-radius: 5px;
  -webkit-transition: color 200ms ease;
  transition: color 200ms ease;
  color: #33383f;
  line-height: 26px;
}

.nav-dropdown:hover {
  opacity: 1;
  color: #3878ff;
}

.nav-dropdown.w--current {
  -webkit-transition-property: none;
  transition-property: none;
  color: #096ad0;
  font-weight: 700;
}

.dropdown-list.w--open {
  padding: 8px;
  border-style: solid;
  border-width: 1px;
  border-color: #eaeef1;
  border-radius: 0px;
  background-color: #fff;
}

.paragraph {
  margin-bottom: 16px;
  color: '#373d40';
}

.paragraph.small {
  opacity: 0.75;
  font-size: 14px;
  line-height: 1.4em;
}

.paragraph.large {
  font-size: 20px;
  line-height: 1.5em;
}

.paragraph.medium {
  margin-bottom: 20px;
  font-size: 18px;
}

.paragraph.medium.no-margin {
  margin-bottom: 0px;
}

.paragraph.l {
  font-size: 19px;
  line-height: 1.4em;
}

.no-margin {
  margin-top: 0px;
  margin-bottom: 0px;
}

.nav-menu {
  padding-right: 12px;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  text-align: left;
}

.ds-menu-brand {
  width: 140px;
  margin-bottom: 50px;
  margin-left: 30px;
}

.ds-menu {
  width: 100%;
}

.ds-menu-link {
  width: 100%;
  padding-top: 7px;
  padding-bottom: 7px;
  padding-left: 27px;
  border-left: 4px solid transparent;
  -webkit-transition: background-color 200ms ease, color 200ms ease;
  transition: background-color 200ms ease, color 200ms ease;
  color: #f2f5f7;
}

.ds-menu-link:hover {
  color: #1b5ce5;
}

.ds-menu-link.w--current {
  padding-right: 0px;
  background-color: #1b5ce5;
  color: #f2f5f7;
  font-weight: 500;
}

.ds-nav {
  position: fixed;
  left: 0%;
  top: 0%;
  right: auto;
  bottom: 0%;
  overflow: auto;
  width: 240px;
  padding-top: 40px;
  background-color: #3878ff;
}

.form-radio-button {
  margin-bottom: 16px;
  padding-left: 24px;
}

.radio-button {
  width: 20px;
  height: 20px;
  margin-top: 0px;
  margin-right: 10px;
  margin-left: -24px;
}

.radio-button.w--redirected-checked {
  border-width: 6px;
  border-color: #3878ff;
}

.form-error {
  padding: 16px;
  font-size: 14px;
}

.form-wrapper {
  margin-bottom: 8px;
}

.ds-icon {
  display: inline-block;
  margin-right: 24px;
  margin-bottom: 4px;
}

.ds-icon-grid {
  margin-top: 24px;
}

.form-checkbox {
  margin-bottom: 16px;
  padding-left: 24px;
}

.check-box {
  width: 20px;
  height: 20px;
  margin-top: 0px;
  margin-right: 10px;
  margin-left: -24px;
  border-radius: 0px;
}

.check-box.w--redirected-checked {
  border-color: #3878ff;
  background-color: #3878ff;
  background-position: 50% 50%;
  background-size: 16px;
}

.ds-content {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-width: 100%;
  margin-left: 240px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.form-success {
  padding: 16px 24px;
  background-color: #f2f5f7;
  color: #33383f;
  font-size: 16px;
  line-height: 1.4em;
}

.ds-section {
  position: relative;
  z-index: 0;
  padding: 60px 80px 48px 60px;
  text-align: left;
}

.ds-section.header {
  background-color: #f2f5f7;
  color: #1f1f1f;
}

.ds-title {
  margin-bottom: 8px;
  color: #33383f;
}

.ds-section-header {
  margin-bottom: 50px;
}

.text-input {
  margin-bottom: 20px;
  padding: 21px 16px;
  border: 1px solid transparent;
  border-radius: 0px;
  background-color: #f2f5f7;
  -webkit-transition: border 200ms ease;
  transition: border 200ms ease;
  font-size: 15px;
  line-height: 1.4px;
}

.text-input:hover {
  border-color: #c2cdd8;
}

.text-input:focus {
  border-color: #3878ff;
}

.text-input::-webkit-input-placeholder {
  color: #c2cdd8;
}

.text-input:-ms-input-placeholder {
  color: #c2cdd8;
}

.text-input::-ms-input-placeholder {
  color: #c2cdd8;
}

.text-input::placeholder {
  color: #c2cdd8;
}

.text-input.no-margin {
  margin-bottom: 0px;
}

.text-input.white {
  background-color: #fff;
}

.card {
  display: block;
  overflow: hidden;
  width: 100%;
  height: 100%;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-transition: opacity 200ms ease, -webkit-transform 200ms ease;
  transition: opacity 200ms ease, -webkit-transform 200ms ease;
  transition: transform 200ms ease, opacity 200ms ease;
  transition: transform 200ms ease, opacity 200ms ease, -webkit-transform 200ms ease;
  color: #1f1f1f;
  text-align: left;
}

.card:hover {
  -webkit-transform: translate(0px, -3px);
  -ms-transform: translate(0px, -3px);
  transform: translate(0px, -3px);
}

.card-thumbnail {
  width: 100%;
  /*height: 18vw;*/
  -o-object-fit: cover;
  object-fit: cover;
}

.tag {
  display: inline-block;
  margin-bottom: 8px;
  padding: 3px 12px;
  background-color: gold;
  color: #1f1f1f;
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
  text-align: center;
}

.feature-horizontal {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 8px;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.feature-horizontal.no-margin {
  margin-bottom: 0px;
}

.side-icon {
  width: 24px;
  height: 24px;
  margin-top: 3px;
  margin-right: 8px;
}

.text-area {
  min-height: 120px;
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid transparent;
  border-radius: 0px;
  background-color: #f2f5f7;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.01);
  -webkit-transition: border 200ms ease;
  transition: border 200ms ease;
  font-size: 15px;
}

.text-area:hover {
  border-color: #c2cdd8;
}

.text-area:focus {
  border-color: #3878ff;
}

.text-area::-webkit-input-placeholder {
  color: #c2cdd8;
}

.text-area:-ms-input-placeholder {
  color: #c2cdd8;
}

.text-area::-ms-input-placeholder {
  color: #c2cdd8;
}

.text-area::placeholder {
  color: #c2cdd8;
}

.tabs-menu {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  margin-bottom: 48px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
}

.vertical-tab {
  width: 100%;
  margin-top: 24px;
  margin-bottom: 24px;
}

.tab-link {
  margin-right: 0px;
  margin-left: 0px;
  padding: 15px 20px;
  -webkit-box-flex: 0;
  -webkit-flex: 0 auto;
  -ms-flex: 0 auto;
  flex: 0 auto;
  border-bottom: 2px solid #eaeef1;
  background-color: transparent;
  opacity: 0.5;
  color: #626a72;
  font-size: 18px;
  text-align: center;
}

.tab-link:hover {
  border-bottom-color: #c2cdd8;
  color: #626a72;
}

.tab-link.w--current {
  border-bottom-color: #3878ff;
  background-color: transparent;
  opacity: 1;
  color: #3878ff;
  font-weight: 500;
}

.question {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
}

.answer {
  display: block;
  overflow: hidden;
  max-width: 650px;
}

.question-arrow-icon {
  width: 16px;
  margin-top: 11px;
  margin-right: 0px;
  margin-left: 24px;
}

.ds-description {
  max-width: 400px;
  color: #626a72;
}

.tick-list li {
  margin-bottom: 12px;
  padding-left: 26px;
  background-image: url("https://assets.website-files.com/6398008a5a4ad6e37cf1cc10/63982e1716676245c4813659_Blue%20Tick%20SVG.svg");
  background-position: 0px 4px;
  background-size: 16px;
  background-repeat: no-repeat;
  line-height: 1.2em;
}

.tick-list ul {
  margin-bottom: 0px;
  padding-left: 0px;
  list-style-type: none;
}

.pricing-grid {
  margin-right: auto;
  margin-left: auto;
}

.small-button {
  margin-bottom: 16px;
  padding: 5px 16px;
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  background-color: #3878ff;
  -webkit-transition: border-color 200ms ease, color 200ms ease, box-shadow 200ms ease, background-color 200ms ease;
  transition: border-color 200ms ease, color 200ms ease, box-shadow 200ms ease, background-color 200ms ease;
  color: #fff;
  font-size: 15px;
  line-height: 1.5;
  font-weight: 500;
  text-align: center;
}

.small-button:hover {
  background-color: #1248bb;
  box-shadow: 0 7px 20px -10px rgba(0, 0, 0, 0.2);
  color: #fff;
}

.small-button:active {
  background-color: #41a319;
}

.small-button.light {
  margin-right: 0px;
  background-color: #f2f5f7;
  box-shadow: inset 0 0 0 1px #eaeef1;
  color: #626a72;
}

.small-button.light:hover {
  background-color: #c2cdd8;
}

.small-button.light:active {
  background-color: #1b5ce5;
  color: #fff;
}

.small-button.light.no-margin:hover {
  background-color: #eaeef1;
}

.small-button.light.no-margin:active {
  background-color: #c2cdd8;
  color: #33383f;
}

.small-button.outline {
  background-color: transparent;
  box-shadow: inset 0 0 0 1px #d6e7ff;
  color: #3878ff;
}

.small-button.outline:hover {
  background-color: #1b5ce5;
  box-shadow: inset 0 0 0 2px #1b5ce5;
  color: #fff;
}

.small-button.outline:active {
  background-color: #1248bb;
  box-shadow: inset 0 0 0 2px #1248bb;
}

.small-button.no-margin {
  margin-bottom: 0px;
}

.cta-box {
  display: -ms-grid;
  display: grid;
  overflow: hidden;
  padding: 64px 64px 48px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  grid-auto-columns: 1fr;
  grid-column-gap: 80px;
  grid-row-gap: 16px;
  -ms-grid-columns: 50% 50%;
  grid-template-columns: 50% 50%;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
  background-color: #3878ff;
  color: #fff;
}

.nav-container {
  left: 0px;
  top: 0px;
  right: 0px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  height: 75px;
  max-width: 1330px;
  margin-right: auto;
  margin-left: auto;
  padding: 12px 50px;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.error-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-right: -15px;
  margin-left: -15px;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  grid-auto-columns: 1fr;
  -ms-grid-columns: 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.logo {
  display: block;
}

.nav-link {
  margin-right: 4px;
  margin-left: 4px;
  padding: 6px 8px;
  -webkit-transition: background-color 200ms ease, color 200ms ease, -webkit-transform 200ms ease;
  transition: background-color 200ms ease, color 200ms ease, -webkit-transform 200ms ease;
  transition: background-color 200ms ease, transform 200ms ease, color 200ms ease;
  transition: background-color 200ms ease, transform 200ms ease, color 200ms ease, -webkit-transform 200ms ease;
  color: #33383f;
  line-height: 24px;
  font-weight: 400;
  cursor: pointer;
}

.nav-link:hover {
  color: #3878ff;
}

.nav-link:active {
  color: #1b5ce5;
}

.nav-link.w--current {
  color: #33383f;
}

.nav-link.primary {
  padding-right: 20px;
  padding-left: 20px;
  border-radius: 0px;
  background-color: #3878ff;
  -webkit-transition: box-shadow 200ms ease, background-color 200ms ease, color 200ms ease, -webkit-transform 200ms ease;
  transition: box-shadow 200ms ease, background-color 200ms ease, color 200ms ease, -webkit-transform 200ms ease;
  transition: box-shadow 200ms ease, background-color 200ms ease, transform 200ms ease, color 200ms ease;
  transition: box-shadow 200ms ease, background-color 200ms ease, transform 200ms ease, color 200ms ease, -webkit-transform 200ms ease;
  color: #fff;
}

.nav-link.primary:hover {
  background-color: #1b5ce5;
}

.nav-link.primary:active {
  background-color: #1248bb;
  box-shadow: 0 0 0 0 #eaeef1;
  -webkit-transform: translate(0px, 2px);
  -ms-transform: translate(0px, 2px);
  transform: translate(0px, 2px);
}

.form-dropdown {
  position: relative;
  width: 100%;
  height: 48px;
  margin-bottom: 16px;
  padding: 8px 16px;
  border: 1px solid transparent;
  border-radius: 0px;
  background-color: #f2f5f7;
  -webkit-transition: border 200ms ease;
  transition: border 200ms ease;
  color: #99a4af;
  font-size: 15px;
}

.form-dropdown:hover {
  border-color: #c2cdd8;
}

.form-dropdown:focus {
  border-color: #3878ff;
  color: #33383f;
}

.form-dropdown::-webkit-input-placeholder {
  color: #999;
  font-size: 15px;
}

.form-dropdown:-ms-input-placeholder {
  color: #999;
  font-size: 15px;
}

.form-dropdown::-ms-input-placeholder {
  color: #999;
  font-size: 15px;
}

.form-dropdown::placeholder {
  color: #999;
  font-size: 15px;
}

.field-block {
  z-index: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-height: 20px;
  min-width: 50px;
  margin-bottom: 8px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  border: 1px solid transparent;
}

.error {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-right: -15px;
  margin-left: -15px;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  grid-auto-columns: 1fr;
  -ms-grid-columns: 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.banner-link {
  display: inline;
  margin-right: 0px;
  margin-left: 0px;
  padding-right: 0px;
  padding-left: 0px;
  color: #3878ff;
  line-height: 1.3em;
  font-weight: 500;
  text-decoration: none;
}

.banner-link:hover {
  color: #b6d1ff;
}

.footer-header {
  color: #1f1f1f;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0px;
}

.center-card {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  max-width: 450px;
  padding: 40px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  border-style: solid;
  border-width: 1px;
  border-color: #eaeef1;
  background-color: #fff;
}

.form {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  text-align: left;
}

.footer-container {
  display: block;
  width: 100%;
  max-width: 1330px;
  margin-right: auto;
  margin-left: auto;
  padding-right: 50px;
  padding-left: 50px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.ds-colour-block {
  display: inline-block;
  width: 12.5%;
  padding-right: 16px;
}

.ds-colour-grid {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-top: 24px;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.ds-module {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-height: 100px;
  min-width: 100px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  border-radius: 2px;
  background-color: #eaeef1;
}

._1-2-grid {
  position: relative;
  display: -ms-grid;
  display: grid;
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  grid-auto-columns: 1fr;
  grid-column-gap: 32px;
  grid-row-gap: 32px;
  -ms-grid-columns: 1fr 2fr;
  grid-template-columns: 1fr 2fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

._2-1-grid {
  position: relative;
  display: -ms-grid;
  display: grid;
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  grid-auto-columns: 1fr;
  grid-column-gap: 32px;
  grid-row-gap: 32px;
  -ms-grid-columns: 2fr 1fr;
  grid-template-columns: 2fr 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.heading {
  display: block;
  font-weight: 500;
}

.heading.h2 {
  margin-bottom: 18px;
  font-size: 42px;
  line-height: 1.2em;
}

.heading.h2.no-margin {
  margin-bottom: 0px;
}

.heading.h3 {
  margin-bottom: 16px;
  font-size: 32px;
  line-height: 1.3em;
}

.heading.h4 {
  margin-bottom: 12px;
  font-size: 24px;
  line-height: 1.25em;
}

.heading.h5 {
  margin-top: 4px;
  margin-bottom: 16px;
  font-size: 20px;
  line-height: 1.2em;
}

.heading.h5.no-margin {
  margin-bottom: 0px;
}

.heading.h6 {
  margin-bottom: 12px;
  color: #33383f;
  font-size: 18px;
  line-height: 1.2em;
  font-weight: 500;
}

.heading.large-h1 {
  margin-bottom: 18px;
  font-size: 58px;
  line-height: 1.1em;
}

.heading.large-h1.no-margin {
  margin-bottom: 8px;
}

.heading.h1 {
  margin-bottom: 22px;
  font-size: 50px;
  line-height: 1.15em;
}

.heading.h4 {
  margin-bottom: 12px;
  font-size: 25px;
  line-height: 1.25em;
}

.heading.h5 {
  margin-top: 2px;
  margin-bottom: 16px;
  font-size: 21px;
  line-height: 1.2em;
}

.heading.h5.no-margin {
  margin-bottom: 0px;
}

.dropdown-arrow {
  margin-right: 8px;
  margin-bottom: 11px;
  opacity: 0.3;
  font-size: 12px;
}

.error2 {
  position: relative;
  left: 0px;
  top: 0px;
  right: 0px;
  display: block;
  width: 100%;
  max-width: 1230px;
  margin-right: auto;
  margin-left: auto;
  padding-right: 40px;
  padding-left: 40px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.licenses-grid {
  position: relative;
  display: -ms-grid;
  display: grid;
  margin-right: auto;
  margin-bottom: 24px;
  margin-left: auto;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  grid-auto-columns: 1fr;
  grid-column-gap: 96px;
  grid-row-gap: 24px;
  -ms-grid-columns: 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.utility-page-wrap {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100vw;
  height: 80vh;
  max-height: 100%;
  max-width: 100%;
  padding: 20px;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #f2f5f7;
}

.utility-page-content {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 340px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: center;
}

.utility-page-form {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  max-width: 400px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.visible-spacer {
  display: inline-block;
  margin-bottom: 24px;
  background-color: #eaeef1;
}

.email-form {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.field-split {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.field-spacer {
  width: 24px;
  height: 10px;
}

.nav-spacer {
  height: 0px;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.sign-in-div {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: right;
}

.sign-up-nav {
  position: absolute;
  top: 0px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  height: auto;
  padding-top: 30px;
  padding-right: 40px;
  padding-left: 40px;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.blog-image {
  position: relative;
  display: block;
  margin-right: auto;
  margin-bottom: 16px;
  margin-left: auto;
  border-radius: 2px;
}

.questions-section {
  display: -ms-grid;
  display: grid;
  grid-auto-columns: 1fr;
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  -ms-grid-columns: 0.6fr 1fr;
  grid-template-columns: 0.6fr 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.tab-dropdown-icon {
  position: absolute;
  left: auto;
  top: 18px;
  right: 0px;
  bottom: auto;
  width: 18px;
  margin-top: 8px;
  margin-right: 0px;
  margin-left: 24px;
}

.full-page-wrapper {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #f2f5f7;
}

._3-grid {
  display: -ms-grid;
  display: grid;
  width: 100%;
  grid-auto-columns: 1fr;
  grid-column-gap: 32px;
  grid-row-gap: 32px;
  -ms-grid-columns: 1fr 1fr 1fr;
  grid-template-columns: 1fr 1fr 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

._4-grid {
  display: -ms-grid;
  display: grid;
  grid-auto-columns: 1fr;
  grid-column-gap: 64px;
  grid-row-gap: 32px;
  -ms-grid-columns: 1fr 1fr 1fr 1fr;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.email-subscribe {
  position: relative;
  width: 100%;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
}

.social-link {
  opacity: 0.5;
  -webkit-transition: opacity 200ms ease, color 200ms ease;
  transition: opacity 200ms ease, color 200ms ease;
}

.social-link:hover {
  opacity: 0.75;
}

.social-link-icon {
  width: 22px;
  height: 22px;
}

.social-grid {
  display: -ms-grid;
  display: grid;
  margin-bottom: -2px;
  margin-left: 16px;
  grid-auto-columns: 1fr;
  grid-column-gap: 20px;
  grid-row-gap: 16px;
  -ms-grid-columns: 1fr 1fr 1fr;
  grid-template-columns: 1fr 1fr 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.footer-social-section {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.search {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-width: 400px;
  margin-bottom: 0px;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
}

.overflow-card {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin-top: -180px;
  margin-right: auto;
  margin-left: auto;
  padding: 72px 64px 48px;
  border-style: solid;
  border-width: 1px;
  border-color: #eaeef1;
  background-color: #fff;
}

._2-grid {
  display: -ms-grid;
  display: grid;
  width: 100%;
  grid-auto-columns: 1fr;
  grid-column-gap: 32px;
  grid-row-gap: 32px;
  -ms-grid-columns: 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.error-5 {
  position: relative;
  z-index: 2;
  display: block;
  width: 100%;
  max-width: 1280px;
  min-height: 50px;
  margin-right: auto;
  margin-left: auto;
  padding-right: 40px;
  padding-left: 40px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.customer-logo-image {
  margin-right: auto;
  margin-left: auto;
}

.customer-logos-row {
  left: 0px;
  top: 0px;
  right: 0px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-justify-content: space-around;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.logos-row-block {
  position: relative;
  z-index: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 2400px;
  margin-right: auto;
  margin-left: auto;
}

.customer-logo-container {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-width: 160px;
  margin-right: 23px;
  margin-left: 23px;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.customer-logo-container.larger {
  max-width: 165px;
}

.logos-row-wrapper {
  position: relative;
  overflow: hidden;
  margin-top: 8px;
}

.hero-grid {
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  grid-column-gap: 80px;
  grid-row-gap: 32px;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.right-tab-image {
  width: 100%;
  height: 100%;
  margin-left: auto;
  -o-object-fit: cover;
  object-fit: cover;
}

.hero-illustration {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 120%;
  height: 140%;
  max-width: 120%;
  margin-right: -50px;
  margin-bottom: -120px;
  margin-left: auto;
  -o-object-fit: cover;
  object-fit: cover;
}

.cta-image {
  position: relative;
  width: 100%;
  height: 130%;
  max-width: none;
  margin-top: -53px;
  margin-right: -64px;
  -o-object-fit: cover;
  object-fit: cover;
}

.small-tick-text {
  margin-top: 8px;
  margin-bottom: 8px;
  padding-left: 20px;
  background-image: url("https://assets.website-files.com/6398008a5a4ad6e37cf1cc10/6399431b0e057251c7c5944c_Light%20Blue%20Tick.svg");
  background-position: 0% 50%;
  background-size: 12px;
  background-repeat: no-repeat;
  color: #d6e7ff;
  font-size: 14px;
}

.lower-footer-grid {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.footer-grid {
  -ms-grid-columns: 2fr 1fr 1fr 1fr;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.footer-logo-box {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
}

.blog-card-text-box {
  padding-top: 24px;
  padding-bottom: 12px;
}

.footer-email-input {
  height: 44px;
  padding-right: 0px;
  padding-left: 0px;
  border-style: none none solid;
  border-width: 1px;
  border-color: #000 #000 #c2cdd8;
  -webkit-transition: border-color 200ms ease;
  transition: border-color 200ms ease;
}

.footer-email-input:hover {
  border-bottom-color: #99a4af;
}

.footer-email-input:focus {
  border-bottom-color: #3878ff;
}

.footer-email-input::-webkit-input-placeholder {
  color: #99a4af;
}

.footer-email-input:-ms-input-placeholder {
  color: #99a4af;
}

.footer-email-input::-ms-input-placeholder {
  color: #99a4af;
}

.footer-email-input::placeholder {
  color: #99a4af;
}

.footer-email-button {
  position: absolute;
  left: auto;
  top: 0%;
  right: -10px;
  bottom: auto;
  margin-bottom: 8px;
  padding: 8px 11px;
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  background-color: transparent;
  background-image: url("https://assets.website-files.com/6398008a5a4ad6e37cf1cc10/639945f00123ca0c1e801861_Go%20Blue%20Arrow.svg");
  background-position: 50% 50%;
  background-size: 8px;
  background-repeat: no-repeat;
  -webkit-transition: box-shadow 200ms ease, border-color 200ms ease, color 200ms ease, background-color 200ms ease, -webkit-transform 200ms ease;
  transition: box-shadow 200ms ease, border-color 200ms ease, color 200ms ease, background-color 200ms ease, -webkit-transform 200ms ease;
  transition: box-shadow 200ms ease, transform 200ms ease, border-color 200ms ease, color 200ms ease, background-color 200ms ease;
  transition: box-shadow 200ms ease, transform 200ms ease, border-color 200ms ease, color 200ms ease, background-color 200ms ease, -webkit-transform 200ms ease;
  color: transparent;
  font-size: 18px;
  line-height: 1.5;
  font-weight: 500;
  text-align: center;
}

.footer-email-button:active {
  background-color: #1248bb;
  box-shadow: 0 0 0 0 rgba(32, 32, 32, 0);
  -webkit-transform: translate(0px, 3px);
  -ms-transform: translate(0px, 3px);
  transform: translate(0px, 3px);
}

.error-6 {
  position: relative;
  display: block;
  width: 100%;
  max-width: 1330px;
  min-height: 30px;
  margin-right: auto;
  margin-left: auto;
  padding-right: 40px;
  padding-left: 40px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.regular {
  font-weight: 400;
}

.error-7 {
  position: relative;
  display: block;
  width: 100%;
  max-width: 1230px;
  min-height: 50px;
  margin-right: auto;
  margin-left: auto;
  padding-right: 50px;
  padding-left: 50px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.pricing-features-title-block {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
  -ms-flex-align: end;
  align-items: flex-end;
  text-align: left;
}

.pricing-grid-cross {
  width: 18px;
  margin: auto;
}

.pricing-row-title-block {
  max-width: 350px;
  text-align: left;
}

.plan-header {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  padding: 12px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.pricing-grid-tick {
  width: 24px;
  margin: auto;
}

.pricing-feature-description-text {
  margin-bottom: 2px;
  color: #777370;
  font-size: 15px;
}

.pricing-grid-row {
  display: -ms-grid;
  display: grid;
  min-height: 100px;
  padding-top: 20px;
  padding-bottom: 20px;
  padding-left: 20px;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  grid-auto-columns: 1fr;
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  grid-template-areas: ".";
  -ms-grid-columns: 2.8fr 0px 1fr 0px 1fr 0px 1fr;
  grid-template-columns: 2.8fr 1fr 1fr 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
  border-top: 1px solid #f0edeb;
  text-align: center;
}

.pricing-grid-row.top {
  position: -webkit-sticky;
  position: sticky;
  top: 75px;
  z-index: 2;
  min-height: 40px;
  margin-bottom: -22px;
  padding-top: 0px;
  padding-bottom: 0px;
  border-bottom: 1px solid #f4f4f4;
  border-top-style: none;
  background-color: #fff;
}

.pricing-grid-section {
  margin-top: 20px;
  margin-bottom: 40px;
}

.question-block {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  width: 100%;
  margin-bottom: -1px;
  padding: 32px 0px 16px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  border-bottom: 1px solid #eaeef1;
  text-align: left;
  text-decoration: none;
  cursor: pointer;
}

.questions-wrapper {
  width: 100%;
  margin-top: -24px;
  margin-bottom: 48px;
}

.pricing-card {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
  padding: 32px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  background-color: #fff;
  color: #1f1f1f;
  text-align: left;
}

.pricing-card.popular-outline {
  box-shadow: inset 0 0 0 1px #dbfa6b;
}

.pricing-reccomended-tag {
  position: absolute;
  left: 0%;
  top: -33px;
  right: auto;
  bottom: auto;
  width: 100%;
  padding: 7px 16px 6px;
  background-color: #dbfa6b;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.feature-grid {
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  grid-column-gap: 80px;
  grid-row-gap: 32px;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.get-started-grid {
  -webkit-box-align: start;
  -webkit-align-items: start;
  -ms-flex-align: start;
  align-items: start;
  grid-column-gap: 80px;
  grid-row-gap: 32px;
  -ms-grid-columns: 1fr 480px;
  grid-template-columns: 1fr 480px;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.form-card {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  max-width: 700px;
  padding: 48px 48px 44px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  border-style: solid;
  border-width: 1px;
  border-color: #eaeef1;
  background-color: #fff;
}

.text-link {
  display: block;
  border-bottom: 1px solid #b6d1ff;
}

.link-grid {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: auto;
  grid-column-gap: 32px;
  -ms-grid-columns: auto;
  grid-template-columns: auto;
  -ms-grid-rows: auto auto;
  grid-template-rows: auto auto;
}

.logo-grid {
  justify-items: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  grid-column-gap: 32px;
  grid-row-gap: 24px;
  -ms-grid-columns: 1fr 1fr 1fr;
  grid-template-columns: 1fr 1fr 1fr;
}

.light-tick-list li {
  margin-bottom: 12px;
  padding-left: 26px;
  background-image: url("https://assets.website-files.com/6398008a5a4ad6e37cf1cc10/63982e1716676245c4813659_Blue%20Tick%20SVG.svg");
  background-position: 0px 2px;
  background-size: 18px;
  background-repeat: no-repeat;
  line-height: 1.2em;
}

.light-tick-list ul {
  margin-bottom: 0px;
  padding-left: 0px;
  list-style-type: none;
}

.ds-illustration-grid {
  display: -ms-grid;
  display: grid;
  width: 100%;
  grid-auto-columns: 1fr;
  grid-column-gap: 32px;
  grid-row-gap: 32px;
  -ms-grid-columns: 1fr 1fr 1fr;
  grid-template-columns: 1fr 1fr 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.cta-tick-text-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 8px;
  grid-auto-columns: 1fr;
  grid-column-gap: 32px;
  grid-row-gap: 16px;
  -ms-grid-columns: 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-grid-rows: auto auto;
  grid-template-rows: auto auto;
}

.blog-header-grid {
  display: -ms-grid;
  display: grid;
  width: 100%;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  grid-auto-columns: 1fr;
  grid-column-gap: 80px;
  grid-row-gap: 80px;
  -ms-grid-columns: 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.full-height-text-box {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
}

.top-height-title {
  margin-bottom: 20px;
}

.full-height-image {
  position: relative;
  display: block;
  width: 100%;
  height: 70vh;
  margin-right: auto;
  margin-bottom: 16px;
  margin-left: auto;
  -o-object-fit: cover;
  object-fit: cover;
}

.card-icon {
  width: 40px;
  margin-bottom: 48px;
  opacity: 0.9;
  color: #fff;
  font-size: 24px;
}

.card-icon.black {
  -webkit-filter: brightness(0%) grayscale(100%);
  filter: brightness(0%) grayscale(100%);
}

.hero-illustration-box {
  position: relative;
  width: 100%;
  height: 100%;
  margin-left: auto;
  -o-object-fit: cover;
  object-fit: cover;
}

.tab-pane {
  height: 100%;
}

.small-dot {
  position: relative;
  top: -2px;
  display: inline-block;
  padding-right: 2px;
  padding-left: 2px;
  font-size: 8px;
}

.outline-text-card {
  padding: 32px 32px 24px;
  background-color: #dbfa6b;
}

.contact-grid {
  display: block;
  grid-column-gap: 48px;
  -ms-grid-columns: 1fr;
  grid-template-columns: 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.lime-content-box {
  padding: 48px;
  background-color: #dbfa6b;
}

.full-height-card {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  height: 70vh;
  margin-bottom: 16px;
  padding: 48px 48px 24px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
  background-color: #f2f5f7;
}

.tight-2-grid {
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  grid-column-gap: 48px;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.white-text-card {
  padding: 32px 32px 24px;
  background-color: #fff;
}

._4-feature-grid {
  display: -ms-grid;
  display: grid;
  grid-auto-columns: 1fr;
  grid-column-gap: 64px;
  grid-row-gap: 32px;
  -ms-grid-columns: 1fr 1fr 1fr 1fr;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

@media screen and (min-width: 1280px) {
  .section.hero-blue {
    padding-top: 120px;
    padding-bottom: 120px;
  }

  .banner-container {
    max-width: 1140px;
  }

  .card-thumbnail {
    height: 741px;
  }

  .questions-section {
    grid-auto-columns: 1fr;
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    -ms-grid-columns: 0.6fr 1fr;
    grid-template-columns: 0.6fr 1fr;
    -ms-grid-rows: auto auto;
    grid-template-rows: auto auto;
  }

  .feature-grid {
    grid-column-gap: 96px;
  }

  .contact-grid {
    display: -ms-grid;
    display: grid;
    grid-auto-columns: 1fr;
    grid-column-gap: 48px;
    grid-row-gap: 16px;
    -ms-grid-columns: 0.8fr 1fr;
    grid-template-columns: 0.8fr 1fr;
    -ms-grid-rows: auto;
    grid-template-rows: auto;
  }

  .tight-2-grid {
    grid-auto-columns: 1fr;
  }
}

@media screen and (max-width: 991px) {
  .section.hero-blue {
    padding-top: 80px;
    padding-bottom: 80px;
  }

  .button {
    position: relative;
  }

  ._12-columns {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }

  .container {
    padding-right: 30px;
    padding-left: 30px;
  }

  .column {
    width: 50%;
  }

  .column.desk-3 {
    width: 33.33%;
    -webkit-flex-wrap: nowrap;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }

  .ds-block {
    margin-bottom: 40px;
  }

  .menu-icon {
    color: #096ad0;
  }

  .nav-content {
    margin-left: 16px;
  }

  .menu-button.w--open {
    background-color: transparent;
    color: #096ad0;
  }

  .logo-div {
    -webkit-box-flex: 0;
    -webkit-flex: 0 auto;
    -ms-flex: 0 auto;
    flex: 0 auto;
  }

  .nav-logo {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
  }

  .footer-links-container {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
  }

  .banner-section {
    padding-right: 60px;
    padding-left: 60px;
  }

  .banner-container {
    text-align: center;
  }

  .tab-text-box {
    width: 100%;
  }

  .tabs-text-boxes {
    width: 100%;
  }

  .tabs-images {
    width: 100%;
    height: auto;
    max-height: none;
    margin-top: 0px;
    margin-bottom: 32px;
    padding-left: 0px;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .horizontal-tabs {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
    -webkit-flex-direction: column-reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse;
  }

  .nav-dropdown {
    display: block;
    margin-right: 0px;
    margin-left: 0px;
    padding-right: 8px;
    padding-left: 8px;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    font-size: 15px;
    text-align: center;
  }

  .ds-menu-brand {
    margin-bottom: 20px;
  }

  .ds-menu {
    position: absolute;
    left: 0%;
    top: 80px;
    right: auto;
    bottom: auto;
    z-index: 500;
    display: block;
    padding-top: 17px;
    padding-bottom: 17px;
    border-bottom: 1px solid #eaeef1;
    background-color: #fff;
  }

  .ds-nav {
    position: -webkit-sticky;
    position: sticky;
    left: auto;
    bottom: auto;
    z-index: 10;
    overflow: visible;
    width: 100%;
    padding-top: 20px;
  }

  .ds-content {
    position: static;
    margin-left: 0px;
  }

  .card-thumbnail {
    height: 28vw;
  }

  .feature-horizontal {
    margin-bottom: 20px;
  }

  .small-button {
    position: relative;
  }

  .cta-box {
    padding: 48px 0px 40px 40px;
    grid-column-gap: 24px;
    -ms-grid-columns: 1.5fr 1fr;
    grid-template-columns: 1.5fr 1fr;
  }

  .nav-container {
    padding-right: 30px;
    padding-left: 30px;
  }

  .nav-link {
    margin-right: 2px;
    margin-left: 2px;
    font-size: 15px;
  }

  .nav-link.primary {
    margin-left: 8px;
    padding-right: 16px;
    padding-left: 16px;
  }

  .footer-container {
    padding-right: 30px;
    padding-left: 30px;
  }

  ._1-2-grid {
    margin-left: 0px;
  }

  ._2-1-grid {
    margin-left: 0px;
  }

  .heading.large-h1 {
    font-size: 52px;
  }

  .heading.h1 {
    font-size: 46px;
  }

  .dropdown-arrow {
    display: none;
  }

  .error2 {
    padding-right: 30px;
    padding-left: 30px;
  }

  .licenses-grid {
    margin-left: 0px;
    grid-column-gap: 40px;
  }

  .ds-body {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .ds-menu-button {
    top: -10px;
    margin-right: 12px;
  }

  .ds-menu-button.w--open {
    border-radius: 40px;
    background-color: #1b5ce5;
  }

  ._3-grid {
    -ms-grid-columns: 1fr 1fr;
    grid-template-columns: 1fr 1fr;
  }

  ._4-grid {
    -ms-grid-columns: 1fr 1fr;
    grid-template-columns: 1fr 1fr;
  }

  .overflow-card {
    padding-top: 64px;
  }

  .error-5 {
    padding-right: 30px;
    padding-left: 30px;
  }

  .customer-logo-container {
    width: 33%;
    margin-right: 0px;
    margin-left: 0px;
    padding-right: 20px;
    padding-left: 20px;
  }

  .cta-image {
    right: -20px;
    margin-bottom: -50px;
  }

  .error-6 {
    padding-right: 30px;
    padding-left: 30px;
  }

  .error-7 {
    padding-right: 30px;
    padding-left: 30px;
  }

  .pricing-grid-row {
    -ms-grid-columns: 2.5fr 1fr 1fr 1fr;
    grid-template-columns: 2.5fr 1fr 1fr 1fr;
  }

  .feature-grid {
    grid-column-gap: 64px;
  }

  .get-started-grid {
    grid-column-gap: 48px;
    -ms-grid-columns: 1fr 1fr;
    grid-template-columns: 1fr 1fr;
  }

  .form-card {
    padding: 32px;
  }

  .ds-illustration-grid {
    -ms-grid-columns: 1fr 1fr;
    grid-template-columns: 1fr 1fr;
  }

  .cta-tick-text-row {
    display: none;
  }

  .blog-header-grid {
    grid-column-gap: 48px;
    grid-row-gap: 48px;
  }

  .full-height-card {
    padding: 32px 32px 16px;
  }

  .tight-2-grid {
    grid-column-gap: 32px;
  }

  ._4-feature-grid {
    -ms-grid-columns: 1fr 1fr;
    grid-template-columns: 1fr 1fr;
  }
}

@media screen and (max-width: 767px) {
  h1 {
    font-size: 36px;
  }

  h2 {
    font-size: 32px;
  }

  h3 {
    font-size: 22px;
  }

  .section {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .section.hero-blue {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .spacer {
    height: 32px;
  }

  .spacer._64 {
    height: 48px;
  }

  .spacer._32.mobile-hidden {
    display: none;
  }

  .spacer._80 {
    width: 64px;
    height: 64px;
  }

  .spacer._96 {
    width: 64px;
    height: 64px;
  }

  .spacer._16.mobile-hidden {
    display: none;
  }

  .text-box.mobile-center {
    margin-right: auto;
    margin-left: auto;
    text-align: center;
  }

  ._12-columns {
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }

  ._12-columns.flex-horizontal {
    margin-bottom: 0px;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }

  .column.desk-6 {
    width: 100%;
  }

  .column.desk-5 {
    width: 100%;
  }

  .column.desk-4 {
    width: 100%;
  }

  .column.desk-9 {
    width: 100%;
  }

  .column.desk-7 {
    width: 100%;
  }

  .column.desk-8 {
    width: 100%;
  }

  .nav-content {
    position: absolute;
    overflow: auto;
    height: 100vh;
    margin-left: 0px;
    padding-top: 20px;
    padding-bottom: 100px;
    border-top: 1px solid #eaeef1;
    background-color: #fff;
  }

  .nav-cta-button-container {
    margin-top: 20px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: stretch;
    -webkit-align-items: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
  }

  .menu-button {
    width: 56px;
    height: 56px;
    margin-right: -12px;
    padding: 16px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .nav-bar {
    border: 1px none #000;
  }

  .logo-div {
    margin-right: auto;
  }

  .footer-logo {
    padding-left: 0px;
  }

  .footer-links-container {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
  }

  .banner-section {
    padding-right: 30px;
    padding-left: 30px;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    text-align: left;
  }

  .banner-container {
    text-align: left;
  }

  .banner {
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    text-align: left;
  }

  .tab-text-box {
    height: 62px;
  }

  .horizontal-tabs {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
    -webkit-flex-direction: column-reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse;
  }

  .nav-dropdown {
    padding-top: 13px;
    padding-bottom: 13px;
    font-size: 16px;
  }

  .nav-dropdown:hover {
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }

  .dropdown-list {
    padding-top: 8px;
    padding-bottom: 8px;
    background-color: #f2f5f7;
    text-align: center;
  }

  .dropdown-list.w--open {
    position: relative;
    display: none;
    border-style: none;
    box-shadow: none;
    text-align: center;
  }

  .paragraph.large {
    font-size: 18px;
  }

  .nav-menu {
    padding-right: 0px;
    text-align: center;
  }

  .ds-menu-brand.w--current {
    margin-top: 14px;
  }

  .ds-menu {
    padding: 16px 24px 24px;
    background-color: #fff;
  }

  .ds-content {
    margin-left: 0px;
  }

  .ds-section {
    padding-right: 30px;
    padding-left: 30px;
  }

  .card-thumbnail {
    height: 56vw;
  }

  .tabs-menu {
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }

  .tab-link {
    width: auto;
  }

  .tick-list ul {
    width: auto;
  }

  .cta-box {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    overflow: hidden;
    padding-right: 40px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: center;
  }

  .nav-container {
    padding-top: 8px;
    padding-bottom: 8px;
  }

  .logo {
    max-width: 90%;
  }

  .nav-link {
    margin-right: 25px;
    margin-left: 25px;
    padding-top: 12px;
    padding-bottom: 12px;
    font-size: 16px;
    text-align: center;
  }

  .nav-link:hover {
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }

  .field-block {
    min-height: auto;
  }

  .dropdown {
    display: block;
  }

  .form {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .footer-container {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .ds-colour-block {
    width: 25%;
  }

  ._1-2-grid {
    margin-left: 0px;
    -ms-grid-columns: 2fr;
    grid-template-columns: 2fr;
  }

  ._2-1-grid {
    margin-left: 0px;
    -ms-grid-columns: 2fr;
    grid-template-columns: 2fr;
  }

  .heading.h2 {
    font-size: 28px;
  }

  .heading.h3 {
    margin-bottom: 16px;
    font-size: 24px;
  }

  .heading.h4 {
    font-size: 20px;
  }

  .heading.h5 {
    font-size: 18px;
  }

  .heading.large-h1 {
    font-size: 35px;
  }

  .heading.h1 {
    font-size: 40px;
  }

  .heading.h4 {
    font-size: 20px;
  }

  .heading.h5 {
    font-size: 18px;
  }

  .dropdown-arrow {
    position: relative;
    display: inline-block;
    margin-right: 0px;
    margin-left: 6px;
  }

  .licenses-grid {
    margin-left: 0px;
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
  }

  .dropdown-toggle-text {
    display: inline-block;
    margin-left: 18px;
  }

  .questions-section {
    display: block;
  }

  .full-page-wrapper {
    padding: 24px;
  }

  ._3-grid {
    grid-row-gap: 24px;
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
  }

  .footer-social-section {
    margin-top: 8px;
  }

  .overflow-card {
    padding-top: 48px;
    padding-right: 48px;
    padding-left: 48px;
  }

  ._2-grid {
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
  }

  .error-5 {
    padding-right: 20px;
    padding-left: 20px;
  }

  .customer-logo-image {
    -webkit-box-flex: 0;
    -webkit-flex: 0 auto;
    -ms-flex: 0 auto;
    flex: 0 auto;
  }

  .customer-logo-container {
    width: 50%;
  }

  .hero-grid {
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
  }

  .right-tab-image {
    margin-right: auto;
    margin-left: auto;
  }

  .hero-illustration {
    left: 20px;
    margin-right: auto;
    margin-left: auto;
  }

  .cta-image {
    right: 0px;
    width: 105%;
    margin-top: 24px;
  }

  .cta-text-box {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .footer-grid {
    -ms-grid-columns: 1fr 1fr 1fr;
    grid-template-columns: 1fr 1fr 1fr;
  }

  .pricing-row-title-block {
    margin-bottom: 8px;
  }

  .plan-header {
    padding-bottom: 0px;
  }

  .pricing-grid-tick {
    margin-top: 24px;
    margin-bottom: 24px;
  }

  .pricing-grid-row {
    min-height: auto;
    padding-bottom: 10px;
    padding-left: 4px;
    -ms-grid-columns: 1fr 1fr 1fr;
    grid-template-columns: 1fr 1fr 1fr;
    -ms-grid-rows: auto auto;
    grid-template-rows: auto auto;
  }

  .pricing-grid-row.top {
    position: -webkit-sticky;
    position: sticky;
    top: 80px;
    margin-bottom: -25px;
    padding-bottom: 20px;
    background-color: #fff;
  }

  .questions-wrapper {
    margin-top: 0px;
  }

  .pricing-reccomended-tag {
    left: auto;
    top: 8px;
    right: 8px;
    bottom: auto;
    width: auto;
    padding: 4px 12px 3px;
    font-size: 12px;
    letter-spacing: 0px;
  }

  .feature-grid {
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
  }

  .get-started-grid {
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
  }

  .get-started-logos {
    display: none;
  }

  .light-tick-list ul {
    width: auto;
  }

  .blog-header-grid {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
  }

  .full-height-image {
    height: 52vw;
    margin-bottom: 0px;
  }

  .hero-illustration-box {
    height: 200px;
    margin-right: auto;
    margin-left: auto;
  }

  .full-height-card {
    height: auto;
  }

  .tight-2-grid {
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
  }
}

@media screen and (max-width: 479px) {
  body {
    font-size: 14px;
  }

  h1 {
    font-size: 32px;
  }

  h2 {
    font-size: 28px;
  }

  .button {
    width: 100%;
    font-size: 16px;
  }

  ._12-columns.flex-horizontal {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
  }

  .container {
    padding-right: 20px;
    padding-left: 20px;
  }

  .color-block {
    margin-bottom: 20px;
  }

  .column {
    margin-bottom: 0px;
  }

  .column.desk-6 {
    width: 100%;
  }

  .column.desk-5 {
    width: 100%;
  }

  .column.desk-3 {
    width: 100%;
  }

  .menu-button {
    -webkit-box-flex: 0;
    -webkit-flex: 0 auto;
    -ms-flex: 0 auto;
    flex: 0 auto;
  }

  .banner-section {
    margin-right: 0px;
    margin-left: 0px;
    padding-right: 20px;
    padding-left: 20px;
  }

  .banner {
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    text-align: left;
  }

  .tabs-text-boxes {
    max-width: 100%;
    text-align: center;
  }

  .dropdown-list.w--open {
    background-color: #f4f8fa;
  }

  .paragraph.medium {
    font-size: 16px;
  }

  .ds-section {
    padding-right: 16px;
    padding-left: 16px;
  }

  .text-input {
    width: 100%;
  }

  .feature-horizontal {
    width: 100%;
  }

  .text-area {
    width: 100%;
  }

  .answer {
    padding-left: 0px;
  }

  .question-arrow-icon {
    margin-left: 16px;
  }

  .small-button {
    padding-right: 10px;
    padding-left: 10px;
    font-size: 14px;
  }

  .cta-box {
    padding: 40px 32px;
  }

  .nav-container {
    height: 64px;
    padding: 2px 20px;
  }

  .banner-link {
    width: 100%;
    -webkit-box-flex: 0;
    -webkit-flex: 0 auto;
    -ms-flex: 0 auto;
    flex: 0 auto;
  }

  .center-card {
    padding: 0px;
    background-color: transparent;
    box-shadow: none;
  }

  .footer-container {
    padding-right: 20px;
    padding-left: 20px;
  }

  .heading.h2 {
    font-size: 30px;
  }

  .error2 {
    padding-right: 20px;
    padding-left: 20px;
  }

  .email-form {
    width: 100%;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .field-split {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-box-align: stretch;
    -webkit-align-items: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
  }

  .field-spacer {
    display: none;
  }

  .sign-in-div {
    display: none;
  }

  .sign-up-nav {
    padding-right: 24px;
    padding-left: 24px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .tab-dropdown-icon {
    margin-left: 16px;
  }

  .full-page-wrapper {
    padding-top: 100px;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
    background-color: #fff;
  }

  ._4-grid {
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
  }

  .email-subscribe {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .social-grid {
    margin-left: 0px;
  }

  .search {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .overflow-card {
    padding-top: 24px;
    padding-right: 24px;
    padding-left: 24px;
  }

  .error-5 {
    padding-right: 20px;
    padding-left: 20px;
  }

  .customer-logo-image {
    max-height: 35px;
  }

  .logos-row-block {
    width: 1400px;
  }

  .hero-illustration {
    left: 40px;
  }

  .cta-image {
    width: 110%;
  }

  .lower-footer-grid {
    display: -ms-grid;
    display: grid;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
    grid-auto-columns: 1fr;
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
    -ms-grid-rows: auto auto;
    grid-template-rows: auto auto;
  }

  .footer-grid {
    -ms-grid-columns: 1fr 1fr;
    grid-template-columns: 1fr 1fr;
  }

  .footer-email-button {
    font-size: 16px;
  }

  .error-6 {
    padding-right: 20px;
    padding-left: 20px;
  }

  .error-7 {
    padding-right: 20px;
    padding-left: 20px;
  }

  .plan-header {
    padding: 7px 7px 0px;
  }

  .pricing-grid-row {
    padding-left: 8px;
    grid-column-gap: 0px;
  }

  .pricing-grid-row.top {
    top: 62px;
    padding-top: 20px;
    padding-bottom: 0px;
    padding-left: 0px;
  }

  .form-card {
    padding: 24px;
    box-shadow: none;
  }

  .card-icon {
    margin-bottom: 24px;
  }

  .lime-content-box {
    padding: 32px 32px 16px;
  }

  ._4-feature-grid {
    grid-row-gap: 12px;
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
  }
}

#w-node-fdba7448-e060-f785-c6a5-96d8248d753d-87f1cc13 {
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
}

#w-node-df25bafc-bbda-a924-0cad-0deb1912000c-19120008 {
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
}

#w-node-accda97e-abab-b1f7-2fa6-390537acd3c4-19120008 {
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
}

#w-node-_132c6dc5-023f-03c8-d768-4797e3bfb80a-dcf1cc18 {
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
}

#w-node-_132c6dc5-023f-03c8-d768-4797e3bfb811-dcf1cc18 {
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
}

#w-node-_58ce4b51-7247-b12f-c4b9-56d48181257b-dcf1cc18 {
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
}

#w-node-_24932ba0-9bae-5337-205e-6fe4a67a318f-dcf1cc18 {
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
}

#w-node-_9dd05a3e-9b37-6af4-bd91-9ff6f14b230b-3cf1cc25 {
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
}

#w-node-_9dd05a3e-9b37-6af4-bd91-9ff6f14b230c-3cf1cc25 {
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
}

#w-node-_4c2df533-e40e-5e1e-9bb8-5a240b94938c-3cf1cc25 {
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
}

#w-node-_92df9a99-8ad4-27c8-ba74-86202fedd6a1-3cf1cc25 {
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
}

#w-node-_2861ff52-193a-1926-dd7f-a678d7829d98-3cf1cc25 {
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
}

#w-node-efd6e232-f83d-0636-982d-82e139521e18-3cf1cc25 {
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
}

#w-node-_3010b57d-ae86-61d8-e6e0-48d7bf6c90c9-45f1cc28 {
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
}

#w-node-_3010b57d-ae86-61d8-e6e0-48d7bf6c90d3-45f1cc28 {
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
}

#w-node-_3010b57d-ae86-61d8-e6e0-48d7bf6c90dd-45f1cc28 {
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
}

#w-node-_3010b57d-ae86-61d8-e6e0-48d7bf6c9105-45f1cc28 {
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
}

#w-node-_3010b57d-ae86-61d8-e6e0-48d7bf6c910f-45f1cc28 {
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
}

#w-node-_3010b57d-ae86-61d8-e6e0-48d7bf6c9119-45f1cc28 {
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
}

#w-node-_3010b57d-ae86-61d8-e6e0-48d7bf6c9123-45f1cc28 {
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
}

#w-node-_3010b57d-ae86-61d8-e6e0-48d7bf6c912d-45f1cc28 {
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
}

#w-node-e589e84a-0ba8-c850-6ac1-a4c50ec4c3b3-8df1cc2a {
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
}

#w-node-e589e84a-0ba8-c850-6ac1-a4c50ec4c3b3-bdf1cc2e {
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
  -ms-grid-column: span 1;
  grid-column-start: span 1;
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
}

@media screen and (max-width: 767px) {
  #w-node-_762eb1d7-aff2-0755-88a3-ba24a681999b-87f1cc13 {
    -webkit-box-ordinal-group: -9998;
    -webkit-order: -9999;
    -ms-flex-order: -9999;
    order: -9999;
  }

  #w-node-b66374b8-a69d-2cb6-3980-23c2cf3fcb8a-cf3fcb86 {
    -ms-grid-row: span 1;
    grid-row-start: span 1;
    -ms-grid-row-span: 1;
    grid-row-end: span 1;
    -ms-grid-column: span 3;
    grid-column-start: span 3;
    -ms-grid-column-span: 3;
    grid-column-end: span 3;
  }

  #w-node-_3010b57d-ae86-61d8-e6e0-48d7bf6c90b4-45f1cc28 {
    -ms-grid-row: span 1;
    grid-row-start: span 1;
    -ms-grid-row-span: 1;
    grid-row-end: span 1;
    -ms-grid-column: span 3;
    grid-column-start: span 3;
    -ms-grid-column-span: 3;
    grid-column-end: span 3;
  }

  #w-node-_3010b57d-ae86-61d8-e6e0-48d7bf6c90c9-45f1cc28 {
    -ms-grid-column: span 3;
    grid-column-start: span 3;
    -ms-grid-column-span: 3;
    grid-column-end: span 3;
    -ms-grid-row: span 1;
    grid-row-start: span 1;
    -ms-grid-row-span: 1;
    grid-row-end: span 1;
  }

  #w-node-_3010b57d-ae86-61d8-e6e0-48d7bf6c90d3-45f1cc28 {
    -ms-grid-column: span 3;
    grid-column-start: span 3;
    -ms-grid-column-span: 3;
    grid-column-end: span 3;
    -ms-grid-row: span 1;
    grid-row-start: span 1;
    -ms-grid-row-span: 1;
    grid-row-end: span 1;
  }

  #w-node-_3010b57d-ae86-61d8-e6e0-48d7bf6c90dd-45f1cc28 {
    -ms-grid-column: span 3;
    grid-column-start: span 3;
    -ms-grid-column-span: 3;
    grid-column-end: span 3;
    -ms-grid-row: span 1;
    grid-row-start: span 1;
    -ms-grid-row-span: 1;
    grid-row-end: span 1;
  }

  #w-node-_3010b57d-ae86-61d8-e6e0-48d7bf6c9105-45f1cc28 {
    -ms-grid-column: span 3;
    grid-column-start: span 3;
    -ms-grid-column-span: 3;
    grid-column-end: span 3;
    -ms-grid-row: span 1;
    grid-row-start: span 1;
    -ms-grid-row-span: 1;
    grid-row-end: span 1;
  }

  #w-node-_3010b57d-ae86-61d8-e6e0-48d7bf6c910f-45f1cc28 {
    -ms-grid-column: span 3;
    grid-column-start: span 3;
    -ms-grid-column-span: 3;
    grid-column-end: span 3;
    -ms-grid-row: span 1;
    grid-row-start: span 1;
    -ms-grid-row-span: 1;
    grid-row-end: span 1;
  }

  #w-node-_3010b57d-ae86-61d8-e6e0-48d7bf6c9119-45f1cc28 {
    -ms-grid-column: span 3;
    grid-column-start: span 3;
    -ms-grid-column-span: 3;
    grid-column-end: span 3;
    -ms-grid-row: span 1;
    grid-row-start: span 1;
    -ms-grid-row-span: 1;
    grid-row-end: span 1;
  }

  #w-node-_3010b57d-ae86-61d8-e6e0-48d7bf6c9123-45f1cc28 {
    -ms-grid-column: span 3;
    grid-column-start: span 3;
    -ms-grid-column-span: 3;
    grid-column-end: span 3;
    -ms-grid-row: span 1;
    grid-row-start: span 1;
    -ms-grid-row-span: 1;
    grid-row-end: span 1;
  }

  #w-node-_3010b57d-ae86-61d8-e6e0-48d7bf6c912d-45f1cc28 {
    -ms-grid-column: span 3;
    grid-column-start: span 3;
    -ms-grid-column-span: 3;
    grid-column-end: span 3;
    -ms-grid-row: span 1;
    grid-row-start: span 1;
    -ms-grid-row-span: 1;
    grid-row-end: span 1;
  }

  #w-node-_4f98b35c-f7a5-1402-4d0b-6bff679ae01b-e8fd62a5 {
    -webkit-box-ordinal-group: -9998;
    -webkit-order: -9999;
    -ms-flex-order: -9999;
    order: -9999;
  }
}

@media screen and (max-width: 479px) {
  #w-node-b66374b8-a69d-2cb6-3980-23c2cf3fcb8a-cf3fcb86 {
    -ms-grid-row: span 1;
    grid-row-start: span 1;
    -ms-grid-row-span: 1;
    grid-row-end: span 1;
    -ms-grid-column: span 2;
    grid-column-start: span 2;
    -ms-grid-column-span: 2;
    grid-column-end: span 2;
  }

  #w-node-_32f30c02-3be8-5b74-37a8-a671b034e6d6-cf3fcb86 {
    -ms-grid-row: span 1;
    grid-row-start: span 1;
    -ms-grid-row-span: 1;
    grid-row-end: span 1;
    -ms-grid-column: span 2;
    grid-column-start: span 2;
    -ms-grid-column-span: 2;
    grid-column-end: span 2;
  }

  #w-node-_37d13e64-de06-edf8-ab96-0ddb1cc90034-cf3fcb86 {
    -webkit-box-ordinal-group: -9998;
    -webkit-order: -9999;
    -ms-flex-order: -9999;
    order: -9999;
  }

  #w-node-_4f98b35c-f7a5-1402-4d0b-6bff679adfd0-e8fd62a5 {
    -ms-grid-row: span 1;
    grid-row-start: span 1;
    -ms-grid-row-span: 1;
    grid-row-end: span 1;
    -ms-grid-column: span 1;
    grid-column-start: span 1;
    -ms-grid-column-span: 1;
    grid-column-end: span 1;
  }
}