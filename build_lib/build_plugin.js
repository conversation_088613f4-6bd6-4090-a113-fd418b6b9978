
const fs = require('fs');
const upath = require('path');

// 获取文件名
async function getFileName(originDir, pattern) {
  // console.log("getFileName:",originDir,pattern);
  return new Promise((resolve, reject) => {
    fs.readdir(originDir, function (err, paths) {
      if (err) {
        console.log(err);
        throw err;
      }
      paths.forEach((itemPath) => {
        let pathFileName = upath.join(originDir, itemPath);
        fs.stat(pathFileName, (errStat, stats) => {
          if (errStat) {
            console.log(errStat);
            return errStat;
          }
          // console.log('文件:', stats.isFile(), pathFileName, itemPath);
          // console.log("是否是文件:", stats.isFile(),pattern.test(itemPath));
          // 如果是文件
          if (stats.isFile() && pattern.test(itemPath) && itemPath.indexOf('.map') < 0) {
            resolve(itemPath);
          }
        });
      });
    });
  });
}

async function createCndResource() {
  const client = upath.join(
    __dirname.replace('build_lib', ''),
    'dist/'
  );
  const iniFile = upath.join(client, 'cdnResource.ini');
  const jsonFile = upath.join(client, 'cdnResource.json');
  const jsPattern = /(index.)\w*(.js)/g;
  const csssPattern = /(index.)\w*(.css)/g;
  const jsFileName = await getFileName(client + "js/", jsPattern);
  const cssFileName = await getFileName(client + "css/", csssPattern);
  let cssKey = 'index.css';
  let jsKey = 'index.js';
  let iniData =
    `${cssKey}=/${cssFileName}\n` + `${jsKey}=/${jsFileName}`;
  let jsonData = {}
  jsonData[cssKey] = `css/${cssFileName}`
  jsonData[jsKey] = `js/${jsFileName}`
  fs.writeFileSync(iniFile, iniData, (errWrite) => {
    if (errWrite) {
      console.log('写入文件失败：', iniFile, errWrite);
      throw errWrite;
    }
    console.log('文件成功：', iniFile);
  });

  fs.writeFile(jsonFile, JSON.stringify(jsonData), (errWrite) => {
    if (errWrite) {
      console.log('写入文件失败：', jsonFile, errWrite);
      throw errWrite;
    }
    console.log('文件成功：', jsonFile);
    process.exit(0)
  });
}
async function pluginEntry() {
  
  //生成文件cdnResource.ini  cdnResource.json
  await createCndResource();
  
}

// 暴露接口
module.exports = {
  pluginEntry,
};
