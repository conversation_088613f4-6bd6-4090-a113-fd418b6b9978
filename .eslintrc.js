const _mode = process.env.NODE_ENV || "production";

module.exports = {
  env: {
    browser: true,
    es6: true
  },
  globals: {
    $: true,
    process: true,
    dirname: true,
    API_CONFIG: true,
    $ljBridge: true,
    API_CONFIG: true,
    IS_DEV: true
  },
  parser: "@typescript-eslint/parser",
  plugins: ["@typescript-eslint"],
  extends: [
    "alloy",
    "alloy/react",
    "alloy/typescript",
    "plugin:@typescript-eslint/recommended"
  ],
  settings: {
    react: {
      version: "detect"
    }
  },
  rules: {
    // 关闭的规则们
    "no-console": "off",
    "no-debugger": _mode === "development" ? 0 : 2,
    "no-alert": _mode === "development" ? 0 : 2,
    "no-underscore-dangle": 0, // 允许_开头定义
    "eol-last": 0, // 最后是否需要空一行
    "comma-dangle": 0,
    "arrow-parens": 0, // 注意这条规则，箭头函数参数只有一个时，内部代码复杂的话参数需要加括号包裹（太多了被我关了）
    "import/prefer-default-export": 0,
    "consistent-return": 0, // 可以 return 不同类型的值
    "import/no-unresolved": 0, // TODO 似乎现在不认识 @/xxx 的路径 忘了咋配了
    camelcase: 0, // 常量定义，我们是规定的_链接单词，启用则不需要下划线
    "class-methods-use-this": 0, // 没敢乱改，没使用 this 的方法需要写成静态方法
    "prefer-promise-reject-errors": 0, // 关掉 reject 一定要传 error
    "no-prototype-builtins": 0, // 允许调用 Object.prototype 对象实例上的方法
    "no-restricted-syntax": 0, // 允许使用一些新语法 比如 for in 准备剔除
    "guard-for-in": 0, // TODO 剔除 for in，因为 for in 会遍历原型上的属性，可能会导致一些问题
    "no-restricted-properties": 0, // 允许使用一些库函数
    "arrow-body-style": 0, // 箭头函数写法，感觉可以见仁见智
    "max-classes-per-file": 0, // TODO 拆分一个文件有两个 class 的情况，应该一个文件就一个
    "no-return-assign": 0, // 关闭箭头函数一定要返回值
    "object-curly-newline": 0, // 不规定解构或者定义对象时是否一定要换行，方便即可
    "import/no-cycle": 0, // 不检查循环依赖(计算时间长且似乎没必要)
    "no-await-in-loop": 0, // 可以在循环里写 await 但是最好不要
    "semi":["error","always"],

    // 开启的规则们
    "no-empty": ["error", { allowEmptyCatch: true }], // 不允许空的代码块除了 catch
    quotes: [2, "double"], // 字符串使用双引号
    "max-len": [1, 120], // 每行最大长度120
    "operator-linebreak": [2, "before"], // 换行时操作符放新一行 例如 && TODO待讨论
    eqeqeq: 1, // 严格等，不敢直接改，怕之前写了些兼容性的情况，所以搞成 warning
    "no-param-reassign": 1, // 不允许直接修改函数参数 (我给关了，太多)
    "no-plusplus": 2, // 不允许使用一元运算符，用 += 1 代替

    "@typescript-eslint/no-inferrable-types": 0, // 自动类型推断 后面有问题就关掉
    "@typescript-eslint/no-explicit-any": 0, // 允许使用 any
    "@typescript-eslint/no-empty-interface": 0, // 允许空的 interface
  }
};
