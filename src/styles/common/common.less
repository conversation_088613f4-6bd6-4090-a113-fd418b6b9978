// 通用全局样式在这
@import "../variable.less";

* {  
  -webkit-touch-callout:none;  /*系统默认菜单被禁用*/  
  -webkit-user-select:none; /*webkit浏览器*/  
  -khtml-user-select:none; /*早期浏览器*/  
  -moz-user-select:none;/*火狐*/  
  -ms-user-select:none; /*IE10*/  
  user-select:none;  
}

input,
textarea {
  -webkit-touch-callout:default;
  -webkit-user-select:auto; /*webkit浏览器*/  
  -khtml-user-select:auto; /*早期浏览器*/  
  -moz-user-select:auto;/*火狐*/  
  -ms-user-select:auto; /*IE10*/  
  user-select:auto;  
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: "PingFang SC", "SimSun", "SimHei", Arial, Tahoma, Helvetica;
  font-size: 14px;
  color: @font-base;
  background: @bg-base;
}

ul,
ol,
p {
  margin: 0;
  padding: 0;
}

p {
  word-break: break-all;
}

button {
  box-sizing: border-box;
  display: block;
  border: none;
  outline: none;
  background: #fff;
}

pre {
  margin: 0;
  padding: 0;
  font-family: "PingFang SC", "SimSun", "SimHei", Arial, Tahoma, Helvetica;
  font-size: 15px;
  white-space: pre-wrap; /* css3.0 */ 
  white-space: -moz-pre-wrap; /* Firefox */ 
  white-space: -pre-wrap; /* Opera 4-6 */ 
  white-space: -o-pre-wrap; /* Opera 7 */ 
  word-wrap: break-word; /* Internet Explorer 5.5+ */ 
}
