@import "./reset/index.less";   // 重写样式
@import "./common/common.less"; // 全局通用样式

// 一些通用的样式封装
.msg-card-base {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  max-width: 65%;
  background: #fff;
  border-radius: 4px;
  font-size: 15px;
  line-height: 20px;
}

.client-msg {
  .msg-card-base {
    background: #cde5ff;
    float: right;
  }
}

.overflow-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.btn-primary {
  padding: 8px 12px;
  background: @font-blue;
  color: #fff;
  border-radius: 4px;
}

.disable-touch-select {
  // 阻止长按选中文字
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}