import React from "react";
import {
  BrowserRouter as Router,
  Route,
  Switch,
  Redirect,
} from "react-router-dom";
import { Provider } from "react-redux";
import Store from "@/common/store";

import routes from "@/common/routers";

const renderRoutes = (routes: any) => {
  if (!Array.isArray(routes)) return null;

  return (
    <Provider store={Store}>
      <Switch>
        {routes.map((route, index) => {
          if (route.redirect) {
            return (
              <Redirect
                key={route.path || index}
                exact={route.exact}
                strict={route.strict}
                from={route.path}
                to={route.redirect}
              />
            );
          }
          return (
            <Route
              key={route.path || index}
              path={route.path}
              exact={route.exact}
              strict={route.strict}
              render={() => {
                const renderChildRoutes = renderRoutes(route.childRoutes);
                return route.component ? (
                  <route.component route={route}>
                    {renderChildRoutes}
                  </route.component>
                ) : renderChildRoutes;
              }}
            />
          );
        })}
      </Switch>
    </Provider>
  );
};
const AppRouter = (): JSX.Element => <Router>{renderRoutes(routes)}</Router>;
export default AppRouter;
