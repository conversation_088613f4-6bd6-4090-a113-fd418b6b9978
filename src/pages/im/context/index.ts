import React from "react";

import { genUrlParams } from "@/common/utils/url";

interface ImContextProps {
  imConfig: any,
  urlParams: any,
  initStatus: number,
  isPc: boolean,
  toolBox: any;
  isChatGPT?: boolean,
  disableInput?: boolean
  setDisableInput?: any
}

export const ImContext = React.createContext<ImContextProps>({
  imConfig: {}, // im 初始化配置，在 store 里也有一份用于不在 context 中时使用
  toolBox: {},
  urlParams: genUrlParams() || {}, // url 上的参数
  initStatus: 0, // 用户初始化时状态
  isPc: false, // 是否是 pc 端
  isChatGPT: false, // 是否使用chatGPT
  disableInput: false // 是否禁用输入框
});

interface MsgListContextProps {
  addNewMsgs?: Function,
  sendMsg?: Function,
  setMsgList?: Function,
  addSingleMsg?: Function,
  showSkillModal?: Function,
  isHistory?: boolean
}

export const MsgListContext = React.createContext<MsgListContextProps>({
  addNewMsgs: () => {}, // 用于一次性添加多个消息及添加比较特殊的消息类型
  addSingleMsg: () => {}, // 用于一次添加单个消息，只支持配置部分字段
  setMsgList: () => {}, // 用于直接设置或需要设置其中某条消息的状态时使用
  sendMsg: () => {}, // 发送消息
  showSkillModal: () => {}, // 展示技能组 modal
  isHistory: false // 判断是否是历史记录的 context
});