/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-06-03 11:08:14
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-21 11:50:33
 */

import React, { useState, useContext, useEffect } from "react";

import <PERSON>aHelper from "@/common/utils/ua";

import HistorySction from "./components/history-section";
import FunctionSection from "./components/function-section";
import InputSection from "./components/input-section";
import SceneModal from "./components/scene-modal";
import MsgSection from "./components/msg-section";
import WelcomeSection from "./components/welcome-section";
import PcToolSection from "./components/pc-tool-section";
import QuestionnaireTool from "./components/questionnaire-tool";
import { ImContext } from "./context/index";
import compatibleMap from "./constant/compatible-css-map";
import GptMsgSection from "./chatgpt/msg-section"

import "./pc.less";

const NEW_TOOL_TYPE = "1";

function Pc(): JSX.Element {
  const { imConfig, initStatus, urlParams, isChatGPT, toolBox, isPc } = useContext(ImContext);
  const [hasHistory, setHasHistory] = useState(false);
  const [showHistory, setShowHistory] = useState(initStatus !== 0);
  const [loadCount, setLoadCount] = useState(initStatus !== 0 ? 1 : 0);
  const [clickHistory, setClickHistory] = useState(false);
  const [sceneModalVisible, setSceneModalVisible] = useState(false);
  const [historyDone, setHistoryDone] = useState(false);
  const [activeTab, setActiveTab] = useState({tabKey: "", time: Date.now()});

  const { extRegionInfos = [], baseInfo: { background, name } } = imConfig;
  const [sceneCard] = (extRegionInfos || []).filter((info: any) => info.key === "scene_card");
  const channel = UaHelper.getAppChannel();
  const needCompatible = compatibleMap.indexOf(channel) !== -1;

  useEffect(() => {
    const config = sessionStorage.getItem('tabConfig');
    try {
      if (!config) {
        setSceneModalVisible(true);
      } else {
        setSceneModalVisible(false);
        setActiveTab({ tabKey: JSON.parse(config).toolBizCode, time: Date.now() });
      }
    } catch (error) {
      console.log(error);
    }
  }, []);
  
  const { showToolType, toolBox: toolBoxData = [] } = toolBox || {};
  const isNewToolType = showToolType === NEW_TOOL_TYPE;
  const realList = isNewToolType ? toolBoxData : (toolBoxData[0]?.data?.list || []);
  const toolInfo = { key: isNewToolType ? "new_tool_box" :"tool_box", data: { list: realList } };

  const hasTool = !!realList.length;
  
  const loadHistory = () => {
    setClickHistory(true);
    if (hasHistory) {
      setShowHistory(true);
      setLoadCount((loadCount) => loadCount + 1);
    }
  };

  const setTabConfig = config => {
    if (!config) return;
    // 点击小工具回来之后 定位在之前的tab 且不再弹窗
    sessionStorage.removeItem('tabConfig');
    sessionStorage.setItem('tabConfig', JSON.stringify(config));

    setActiveTab({ tabKey: config.toolBizCode, time: Date.now() });
    setSceneModalVisible(false);
  }

  return (
    <div className={`im-wrapper pc ${(needCompatible ? "pc-wx-work" : "")} ${(hasTool ? "pc-tool-box" : "")}`}>
      <div
        className={`pc-header`}
        style={{ background: background || "#108ee9" }}
      >
         {name || "我的客服"}
      </div>
      <div className="pc-wrapper">
        <div className="main-wrapper">
          <div className="pc-body">
            { !isChatGPT && hasHistory && <div className="show-history"><span onClick={loadHistory}>查看历史记录</span></div> }
            { !hasHistory && clickHistory && <div className="show-history"><span>--已经到顶了--</span></div> }
            <HistorySction visible={showHistory} setHasHistory={setHasHistory} loadCount={loadCount} setHistoryDone={setHistoryDone} />
            {initStatus === 0 && (
              <>
                <WelcomeSection imConfig={imConfig} />
                <FunctionSection extRegionInfos={extRegionInfos} isPc={true} />
              </>
            )}
            { isChatGPT ? <GptMsgSection /> : <MsgSection historyDone={historyDone} /> }
            <div id="bottomPlaceholder" />
          </div>
          <QuestionnaireTool extRegionInfos={imConfig.extRegionInfos || []} spm={urlParams.spm} />
          <InputSection onClickSceneModal={() => { setSceneModalVisible(true) }} />
          <SceneModal
            isPc={isPc}
            data={sceneCard}
            visible={sceneModalVisible && initStatus !== 2 && initStatus !== 1}
            onChange={config => { setTabConfig(config) }}
            onClose={() => { setSceneModalVisible(false) }}
          />
        </div>
        {hasTool && <PcToolSection 
          toolInfo={toolInfo} 
          isNewType={showToolType === NEW_TOOL_TYPE}
          moduleType={showToolType === NEW_TOOL_TYPE ? "new_tool_box" : "tool_box"}
          tab={activeTab.tabKey}
          key={activeTab.time}
        />}
      </div>
  </div>
  )
}

export default Pc;