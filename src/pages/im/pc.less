// pc 端样式覆盖文件，如果组件只需要样式修改则都在这里面统一覆盖
// 因为感觉在各个组件里做兼容有点蛋疼
// 注意如果是需求修改样式请去对应组件修改，这里只是 pc 端覆盖

* {  // pc 端允许选中了
  -webkit-touch-callout:auto !important;  /*系统默认菜单被禁用*/  
  -webkit-user-select:auto !important; /*webkit浏览器*/  
  -khtml-user-select:auto !important; /*早期浏览器*/  
  -moz-user-select:auto !important;/*火狐*/  
  -ms-user-select:auto !important; /*IE10*/  
  user-select:auto !important;

  &::-webkit-scrollbar {
    display: none;
  }
}

.im-wrapper {
  &.pc {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    overflow-y: scroll;
    scrollbar-width: none;
    padding: 0 10px;
    margin: 0 auto;
    width: 80vw;
    &.pc-tool-box{
      width: calc(50vw + 410px);
    }
  }

  .pc-header {
    height: 45px;
    line-height: 45px;
    color: #fff;
    font-size: 15px;
    text-align: center;
    width: 100%;
  }

  .pc-wrapper {
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    box-shadow: 1px 1px 6px rgba(0, 0, 0, .1);
  }

  .main-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    flex:1;
    min-width: 380px;
    height: 90vh;
    overflow: hidden;

    .pc-body {
      padding-bottom: 10px;
      overflow: scroll;
      scrollbar-width: none;

      ::-webkit-scrollbar {
        display: none;
      }

      .show-history {
        display: flex;
        justify-content: center;
        margin: 15px 0 -15px;

        span {
          padding: 5px 15px;
          background: #ddd;
          font-size: 12px;
          color: #666;
          border-radius: 13px;
          text-align: center;
          cursor: pointer;
        }
      }

      // 推荐区限制宽度
      .recommend-wrapper {
        width: 55%;
      }
      
      // 工单卡片全展开且换行
      .card-list-wrapper {
        flex-wrap: wrap;
        overflow: hidden;

        .card-wrapper {
          margin-bottom: 10px;
        }
        
        .card-more {
          height: 87px;
        }
      }

      // 基础卡片宽度调整
      .msg-card-base {
        max-width: 65%;
      }

      // 富文本中图片宽度
      .rich-text-wrapper {
        .rich-text {
          img {
            width: 80%;
            display: block;
          }
        }
      }

      // 进线提醒卡片
      .online-wrapper {
        justify-content: center;
        margin: 0 auto;
        width: 260px;
      }
    }
  }

  // notice!! 企业微信PC\A+PC 单独兼容
  &.pc-wx-work {
    .main-wrapper {
      .pc-body {
        padding-bottom: 148px;
      }

      .input-section-pc-wrapper {
        position: absolute;
        min-height: 138px; // 企业微信兼容尝试
        bottom: 0;
      }
    }
  }

  &.gpt-container {
    background-color: white;
    width: 99.8vw;
    padding: 0px 0px;
    display: block;

    .ai-header {
      height: 32px;
      width: 98%;
      background-image: url("https://file.ljcdn.com/psd-sinan-file/preview/CE9F8F31D9F049C08BF43622AA03989B/bgheader.png");
      display: flex;
      -webkit-box-orient: horizontal;
      -webkit-box-direction: normal;
      -webkit-flex-direction: row;
      -ms-flex-direction: row;
      flex-direction: row;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      padding: 15px 12px 12px;
      background-repeat: no-repeat;
      background-size: cover;
      border-radius: 5px;
    }

    .ai-title {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
      -webkit-align-items: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-box-pack: start;
      -webkit-justify-content: flex-start;
      -ms-flex-pack: start;
      justify-content: flex-start;
      height: 24px;
    }
  
    .ai-title img {
      width: 18px;
      height: 18px;
    }
  
    .ai-title .ai-main-title {
      font-size: 17px;
      font-weight: 500;
      color: darkslategray;
      margin-left: 6px;
    }

    .msg-card-base {
      max-width: 65%;
      display: flex;
      align-items: center;
      padding: 8px 12px;
      max-width: 65%;
      background: aliceblue;
      border-radius: 4px;
      font-size: 15px;
      line-height: 20px;
    }

    .input-section-pc-wrapper {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      width: 100%;
      background-color: white;
      border-top: none;
    }

    .bubble-wrapper .bubble {
      flex-shrink: 0;
      margin-right: 12px;
      padding: 8px 14px;
      font-size: 12px;
      border-radius: 16px;
      background: rgba(23,26,29,.03);
      cursor: pointer;
    }

    .main-wrapper {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      position: relative;
      flex: 1;
      min-width: 380px;
      height: 93vh;
      overflow: hidden;
    }
  }

  
}