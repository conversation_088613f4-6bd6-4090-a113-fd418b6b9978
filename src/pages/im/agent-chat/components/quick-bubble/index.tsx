import React from 'react';

interface QuickBubbleProps {
  bubbles: object[];
  onClick: (item: object) => void;
}

// 气泡-快捷指令
const QuickBubbles: React.FC<QuickBubbleProps> = ({ bubbles, onClick }) => {
  return (
    <div className="flex flex-wrap gap-2 mb-2">
      {bubbles && bubbles.length > 0 && 
      bubbles.map((item, index) => (
        <button
          key={index}
          className="px-3 py-1 bg-slate-200 text-slate-500 rounded-full text-sm hover:bg-blue-200 transition-colors"
          onClick={() => onClick(item)}
        >
          {item['bubbleName']}
        </button>
      ))}
    </div>
  );
};

export default QuickBubbles;