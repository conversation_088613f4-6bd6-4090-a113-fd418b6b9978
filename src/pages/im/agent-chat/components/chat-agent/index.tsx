import React, { useEffect, useState, useCallback, useRef } from 'react';
import { genUrlParams } from "@/common/utils/url";
import MarkdownComponent from "../../../components/msg-item/components/markdown";
import MarkdownCanvas from '../markdown-canvas';
import ChatIndex from '../../index';
import {scrollToBottom } from "../utils/ai-chat-utils";
import { findAiAppInfo } from "../utils/agent-api-utils";

import './index.less';

const ChatAgent: React.FC = () => {
  const urlParams = genUrlParams() || {};
  const [curScene ] = useState(urlParams.scene || "ai_app");
  const [contextId, setContextId] = useState(urlParams.contextId || '');
  const [markdownContentChunk, setMarkdownContentChunk] = useState('');
  const [markdownContent, setMarkdownContent] = useState('');
  const [isResizing, setIsResizing] = useState(false);
  const [leftWidth, setLeftWidth] = useState(60);
  const [showCanvas, setShowCanvas] = useState(false);
  const [markDownContentDone, setMarkDownContentDone] = useState(false);
  const [showStartDesign, setShowStartDesign] = useState(false);
  const markdownContentRef = useRef<string>('');
  const isEnterDevRef = useRef(-1);

  // 关闭画布
  const onCloseCanvas = useCallback(() => {
    setShowCanvas(false);
    onToggleDevCanvas(isEnterDevRef.current == 1);
  }, []);

  // 开始设计
  const onStartDesign = useCallback(() => {
    ImBus.emit("NEW_SSE_MSG_SEND", 
      { 
        // sse url替换为这个
        sseUrlReplace: "/lsagent/design",
        msgPayload: markdownContentRef.current, 
        scene: curScene,
      }
    );
    setShowCanvas(false);
    setShowStartDesign(false);
  }, []);

  // Markdown内容编辑回调
  const handleMarkdownChange = useCallback((markdown: string) => {
    markdownContentRef.current = markdown;
  }, []);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsResizing(true);
    e.preventDefault();
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing) return;
    
    const containerWidth = window.innerWidth;
    const newLeftWidth = (e.clientX / containerWidth) * 100;
    
    // 限制最小和最大宽度
    if (newLeftWidth >= 20 && newLeftWidth <= 80) {
      setLeftWidth(newLeftWidth);
    }
  }, [isResizing]);

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
  }, []);

  React.useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

   //查询项目信息
  const loadAppInfo = async () => {
    if (curScene === 'ai_agent_app') {
      const app = await findAiAppInfo(contextId);
      const isShowStartDesign = !app || app?.status === 'requirement';
      setShowStartDesign(isShowStartDesign);
      isEnterDevRef.current = isShowStartDesign ? 0 : 1;
    } 
  }

  useEffect(() => {
    loadAppInfo();
  }, []);

  useEffect(() => {
    // 展示画布
    ImBus.on("SHOW_CANVAS", (data: boolean) => {
      setShowCanvas(data);
    });

    // 流式数据
    ImBus.on("CANVAS_CHUNK", (data: string) => {
      setMarkDownContentDone(false);
      setMarkdownContentChunk(data);
      scrollToBottom();
    });

    // 完整内容
    ImBus.on("FULL_CANVAS_CONTENT", (data: string) => {
      loadAppInfo();
      setMarkdownContent(data);
      markdownContentRef.current = data;
      setShowCanvas(true);
      setMarkDownContentDone(true);
      onToggleDevCanvas(false);
    });

  }, []);

  const onToggleDevCanvas = (data: boolean) => {
    // 发送事件：隐藏上层父窗口画布
    window.parent.postMessage({ type: "toggleCanvas", data }, "*");
  } ;


  return (
    <div className="canvas-container">
      {/* 左侧 Canvas 区域 */}
      {
        showCanvas &&
        <div 
        className="canvas-left" 
        style={{ width: `${leftWidth}%` }}
        >
          
          <div className="canvas-content">
            {
              markDownContentDone &&
              <MarkdownCanvas
                initialMarkdown={markdownContent}
                onChange={handleMarkdownChange}
                onClose={onCloseCanvas}
                onStartDesign={onStartDesign}
                showStartDesign={showStartDesign}
              />
            }

            {
              !markDownContentDone &&
              <div className="canvas-markdown">
                <MarkdownComponent content={markdownContentChunk} />
              </div>
            }
          </div>
        </div>
      }
      <div id="bottomPlaceholder" />
      {/* 分割线 */}
      {
        showCanvas &&
        <div 
        className="canvas-divider"
        onMouseDown={handleMouseDown}
        >
          <div className="divider-handle" />
        </div>
      }
      

      {/* 右侧 Chat 区域 */}
      <div 
        className={`canvas-right ${showCanvas ? '' : 'show-full'}`}
        style={{ width: `${showCanvas ? 100 - leftWidth : (showStartDesign ? 70 : 99)}%` }}
      >
        <div className="chat-content">
          <ChatIndex />
        </div>
      </div>
    </div>
  );
};

export default ChatAgent;