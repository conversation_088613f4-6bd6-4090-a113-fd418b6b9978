.canvas-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  // background: #f5f5f5;
  background: #fff;
  
  .canvas-left {
    display: flex;
    flex-direction: column;
    margin-top: 2rem;
    
    .canvas-header {
      padding: 16px 20px;
      border-bottom: 1px solid #e0e0e0;
      background: #fafafa;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }
    
    .canvas-content {
      flex: 1;
      padding: 4px;
      overflow-y: scroll;
    }

    .canvas-markdown {
      background-color: #ffffff;
    }
  }
  
  .canvas-divider {
    width: 1px;
    background: #e0e0e0;
    cursor: col-resize;
    position: relative;
    transition: background-color 0.2s;
    
    &:hover {
      background: #1890ff;
    }
    
    .divider-handle {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 20px;
      height: 40px;
      background: #1890ff;
      border-radius: 4px;
      opacity: 0;
      transition: opacity 0.2s;
    }
    
    &:hover .divider-handle {
      opacity: 1;
    }
  }
  
  .canvas-right {
    display: flex;
    flex-direction: column;
    background: white;
    
    .chat-header {
      padding: 16px 20px;
      border-bottom: 1px solid #e0e0e0;
      background: #fafafa;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }
    
    .chat-content {
      flex: 1;
      overflow: hidden;
      
      // 确保聊天组件填满容器
      > div {
        height: 100%;
      }
    }
  }

  .show-full {
    margin: 0 auto;
  }

}

// 响应式设计
// @media (max-width: 768px) {
//   .canvas-container {
//     flex-direction: column;
    
//     .canvas-left,
//     .canvas-right {
//       width: 100% !important;
//       height: 50%;
//     }
    
//     .canvas-divider {
//       width: 100%;
//       height: 4px;
//       cursor: row-resize;
//     }
//   }
// }