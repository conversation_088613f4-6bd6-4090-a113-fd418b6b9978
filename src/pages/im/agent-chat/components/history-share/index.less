
.pilot-share {
  min-height: 900px;

  .msg-wrapper {
    margin: 16px 0px 0 0px;
  }
  .client-msg {
    .avatar {
      display: none;
    }
    .msg-card-base {
      background: #eff6ff;
      padding: 10px 12px;
      border-radius: 6px;
    }
  }

  .service-msg {
    .avatar {
      display: none;
    }
    .msg-card-base {
      background: #fafaff;
      max-width: 100%;
      padding: 0;
      align-items: inherit;
      border-radius: 6px;

      .markdown {
        h1,h2,h3,h4 {
          color: #222222;
        }
        ol li strong {
          color: #222222;
        }
        ul li strong {
          color: #222222;
        }
        ul li em {
          color: #222222;
        }
      }
    }
  }

  .avatar {
      display: none;
  }
  
  .ai-action-op-container {
    display: none !important;
  }

  .use-tool-container {
    border: solid 1px;
    padding: 13px;
    width: 196px;
    border-radius: 6px;
    border-color: lightgray;
    display: flex;
    align-items: center;
    cursor: pointer;

    .use-tool-tip {
      margin-left: 14px;
      color: darkblue;
    }

    
  }

  .like-btn {
    cursor: pointer;
    font-size: 15px;
  }
  .dislike-btn {
    margin-left: 178px;
    cursor: pointer;
    font-size: 15px;
  }

}

