/*
 * @Author: liuzhenkun002
 * @Date: 2021-04-01 17:24:09
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-07 12:00:34
 */

import React, { useContext, useEffect, useState, useRef } from "react";
import { connect } from "react-redux";

import request from "@/common/request";
import User<PERSON>pi from "@/common/service/user";
import ImApi from "@/common/service/im";
import {
  SAVE_USER_INFO,
  SET_IM_CONFIG,
} from "@/common/store";
import { ImContext, MsgListContext } from "../../../context";
import MsgItem from "../../../components/msg-item"

import "./index.less";


const mapStateToProps = (state: any) => ({
  userInfo: state.user.userInfo
});

const mapDispatchToProps = (dispatch: any) => ({
  saveUserInfo: (data: any) => dispatch({ type: SAVE_USER_INFO, payload: data }),
  saveImConfig: (config: any) => dispatch({ type: SET_IM_CONFIG, payload: config }),
})


function ConversationShare(props: any): JSX.Element {
  const { urlParams } = useContext(ImContext);
  const [imConfig, setImConfig] = useState(null);
  const historyRef = useRef();
  const [historyList, setHistoryList] = useState([]);

  const initData = async() => {
    const user: any = await UserApi.getUserInfo();
    props.saveUserInfo(user);


    const configData = {
      ucid: user.ucId,
      spm: urlParams.spm,
      register: urlParams.register,
      accessSource: "pc",
      channelType: "Web"
    };

    Promise.all([
      ImApi.getImConfig(configData),
    ]).then(([imConfig]: [any]) => {
      setImConfig(imConfig);
      props.saveImConfig(imConfig);

      getMsgHistory(user);
    }).catch(x => x);
  }
 
  const getMsgHistory = async(user) => {
    if (urlParams.convId) {
        const data = {
            sessionId: "ai" + urlParams.convId,
            customerId: urlParams.customerId || user.ucId,
            limit: 100,
          };
        ImApi.getConversationMessages(data)
            .then((data: any) => {
                if (!data || !data.length) return;
                setHistoryList(data);
            })
            .catch((x) => x);
    }
  };
  
  useEffect(() => {
    document.title = "会话详情";
    initData();
  }, []);


  return (
    <ImContext.Provider value={{ imConfig, urlParams }}>
      <MsgListContext.Provider value={{ isHistory: true }}>
        <div className="pilot-share flex-1 bg-white flex flex-col" id="historyList" ref={historyRef}>
          {historyList.map((msg, index) => (<MsgItem key={`${msg.id}${index}`} {...msg} msgId={msg.msgIdSequence}  />))}
        </div>
      </MsgListContext.Provider>
    </ImContext.Provider>
  );
}

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(ConversationShare);