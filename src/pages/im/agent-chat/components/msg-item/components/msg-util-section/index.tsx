
import React, { useState } from "react";
import AiFeedback from "../../../feedback";

function MsgUtilSection(props): JSX.Element {
    const [modalShow, setModalShow] = useState(false);
    const [feedback, setFeedback] = useState("");

    const {  data, content } = props.props;

    const msg_content = data || content;


    return (
      <>
        <AiFeedback modalShow={modalShow} setModalShow={setModalShow} feedback={feedback} setFeedback={setFeedback} props={props.props} msg_content={msg_content} />
      </>
    )
}

export default MsgUtilSection