import React, { useState, useEffect } from "react";

import MarkdownComponent from "../../../../../components/msg-item/components/markdown";

import "./index.less";

function ToolUse(props): JSX.Element {
    const [isCollapse, setIsCollapse] = useState(true);

    const { 
        data,
    } = props;

    let mkdata = "```json\n" + data + "\n```";

    return (
      <>
         <div className="msg-card-base relative tool-use-info-container" >
            <div className="flex items-center title" 
            onClick={() => setIsCollapse(!isCollapse)}
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="#666666" d="M16.14 2.25a5.61 5.61 0 0 0-5.327 7.376L2.77 17.671a1.774 1.774 0 0 0 0 2.508l1.052 1.052a1.773 1.773 0 0 0 2.509 0l8.044-8.045a5.61 5.61 0 0 0 7.19-6.765c-.266-1.004-1.442-1.104-2.032-.514L17.81 7.629a1.017 1.017 0 1 1-1.438-1.438l1.722-1.723c.59-.59.49-1.766-.515-2.032a5.6 5.6 0 0 0-1.438-.186"/></svg>
                <span className="ml-2">工具调用结果</span>

                <span className="ml-4">
                 {
                      isCollapse ? 
                      <span className="collapse-icon">
                      展开 <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 48 48"><path fill="none" stroke="#cccccc" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M36 18L24 30L12 18"/></svg>
                      </span> : 
                      <span className="collapse-icon">
                      收起 <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 48 48"><path fill="none" stroke="#cccccc" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="m13 30l12-12l12 12"/></svg>
                      </span>
                 }
                </span>
            </div>
            {
                !isCollapse &&
                <MarkdownComponent content={mkdata} />
            }
         </div> 
      </>
    )
}

export default ToolUse