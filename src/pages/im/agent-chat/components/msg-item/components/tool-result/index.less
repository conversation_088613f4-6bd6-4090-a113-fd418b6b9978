.use-tool-tip {
    position: relative;
    display: flex;
    align-items: center;
}

.pilot-container {
    .use-tool-container {
        padding: 13px;
        width: 279px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        cursor: pointer;
        margin-top: 5px;
        border: 1px solid lightgray;
    }
    .form-ai-result:hover {
        width: 279px !important;
    }
    .design2app {
        width: 330px !important;
    }
    .ai-gene-form {
        width: 226px !important;
    }
    .btn-width {
        width: 279px !important;
    }

    .preview-pubapp {
        padding: 13px;
        width: 240px;
        border-radius: 6px;
        border-color: lightgray;
        display: flex;
        align-items: center;
    }
    .new-version {
        padding: 13px;
        width: 130px;
        border-radius: 6px;
        border-color: lightgray;
        display: flex;
        align-items: center;
    }
    .ai-edit-error {
        padding: 13px;
        width: 290px;
        border-radius: 6px;
        border-color: lightgray;
        display: flex;
        align-items: center;
    }
    .preview-publish {
        padding: 13px;
        width: 389px;
        border-radius: 6px;
        border-color: lightgray;
        display: grid;

        .accept-btn-container {
            margin: 0 auto;
            margin-top: 1rem;

            .accept-btn {
                background-color: #1f49cd;
                color: white !important;
                width: 17rem;
                height: 2rem;
                border-radius: 11px;
                transition: transform 0.2s ease;
                transform: scale(1);
                
                &:hover {
                    transform: scale(1.1);
                }
                
                &.accepted {
                    background: #d9d9d9;
                    cursor: not-allowed;
                }
            }
        }
    
    }
    .cavas-card {
        background: linear-gradient(to top right, #d6defc, #fff);
    }
}


.form-ai-result {
    .result-tip {
        width: 80px !important;
    }
    .hover-buttons {
        display: flex;
        float: right;
        margin-left: 11px;
        gap: 8px;
    }
}


.design2app .hover-buttons {
    display: flex;
    float: right;
    margin-left: 11px;
    gap: 8px;
}
.ai-gene-form .hover-buttons {
    display: flex;
    float: right;
    margin-left: 11px;
    gap: 8px;
}

.accept-btn, .reject-btn {
    border-radius: 4px;
    cursor: pointer;
    padding: 4px !important;
    color: white;
    border: none;
    float: right;
}

.accept-btn {
    background-color: #52c41a;
}

.reject-btn {
    background-color: #ff4d4f;
}

.diff-btn {
    background-color: #b1c323;
}
