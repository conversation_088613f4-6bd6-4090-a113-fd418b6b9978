import React, { useState, useEffect } from "react";
import {
    GlobalOutlined,
    AppstoreAddOutlined,
} from '@ant-design/icons';
import { parseArtifactResult, scrollToBottom} from "../../../utils/ai-chat-utils";
import Switch from '../../../switch';

import './index.less';

function ToolResult(props): JSX.Element {
    const [hideButtons, setHideButtons] = useState(false);
    const [showToolResult, setShowToolResult] = useState(true);

    const { 
        data, 
        content, 
        last_schema, 
        scene_id, 
        cur_func, 
        action_code, 
        sessionId,
        timestamp,
        foreignId
    } = props;

    const { artifactType, artifactContent, version } = parseArtifactResult(data || content)
    // console.log("artifactType:", artifactType, ", content:", artifactContent, ", version:", version, ", props:", props);


    // 获取载体中应用名字
    const getAppName = () => {
        let applicationName = '';
        if (artifactType) {
            if (artifactType.includes("linkUrl")) {
                const { appName } = JSON.parse(artifactContent);
                applicationName = appName;
            } else if (artifactType === 'TAB_LIST') {
                const { data: { appName } } = JSON.parse(artifactContent);
                applicationName = appName;
            }
        }
        return applicationName;
    }

    // 是否需要给载体发送消息
    const needSendMsgToParentWindow = (artifactType) => {
        // !foreignId表示不是历史会话
        return !foreignId && (artifactType === 'kemis' || artifactType === 'TAB_LIST');
    }
    // 预览
    const openPreview = () => {
        if (needSendMsgToParentWindow(artifactType)) {
            // 发送AI预览消息（设计即实现）
            const type = artifactType === 'TAB_LIST' ? 'aiWorkbenchPreviewResult' : 'aiPreviewResult';
            window.parent.postMessage({ type, metadata: artifactContent, scene_id, cur_func, action_code }, "*");
            
            if (artifactType === 'TAB_LIST') {
                // 收到AI设计分析结果（贝创）
                window.parent.postMessage({ type: 'aiDesignAnalyze', metadata: artifactContent, scene_id, cur_func, action_code }, "*");
                
                // 隐藏其他预览内容（设计即实现）
                window.parent.postMessage({ "type": "hideAiPreviewResult" }, "*");
            } else {
                // 隐藏其他预览内容（设计即实现）
                window.parent.postMessage({ "type": "hideAiWorkbenchPreviewResult" }, "*");
            }
        }
    }

     // toggle ai form 结果
     const toggleAiGeneFormResult = (status) => {
        if (status) {
            window.parent.postMessage({ "type": "aiPreviewResult", "metadata": artifactContent }, "*");
        } else {
            window.parent.postMessage({ "type": "hideAiPreviewResult" }, "*");
        }
    }

    // 拒绝采纳结果
    const rejectResult = (e) => {
        // 阻止事件冒泡
        e.stopPropagation();
        
        if (needSendMsgToParentWindow(artifactType) && last_schema) {
            // 使用上一版的结果
            window.parent.postMessage({ "type": "aiPreviewResult", "metadata": last_schema }, "*");
        }
    }

    // 展示差异结果
    const showDiffResult = (e) => {
        // 阻止事件冒泡
        e.stopPropagation();
        window.parent.postMessage({ "type": "diffResult", "metadata": artifactContent, oldJson: last_schema }, "*");
    }

    // 监听新消息事件
    const listenNewMsg = () => {
        ImBus.on("LAST_SEND_MSG", (data: any) => {
            // 如果当前消息不是最新消息，则不展示行为按钮
            setShowToolResult(timestamp === data.timestamp);
        });
    }

    // 展示画布
    const showCanvas = () => {
       ImBus.emit("FULL_CANVAS_CONTENT", artifactContent);
    }

    useEffect(() => {
        scrollToBottom();
        listenNewMsg();
        openPreview(); 

        const timer = setTimeout(() => {
            setHideButtons(true);
        }, 10000);
        
        return () => clearTimeout(timer);
      }, []);

    return (
      <>
         <div className="msg-card-base relative">
            {
               artifactType === 'kemis' && scene_id === 'ai_form' &&
                <div className={`ai-action-op-container use-tool-container form-ai-result ${hideButtons ? '' : 'btn-width'}`}
                    onClick={openPreview}
                    onMouseEnter={() => setHideButtons(false)}
                    onMouseLeave={() => setHideButtons(true)}
                >
                    <GlobalOutlined />
                    <span className="ai-action-op-container use-tool-tip">
                        <span className="result-tip">结果已生成</span>
                        <div className={`hover-buttons ${hideButtons ? 'hidden' : ''}`}>
                            <button className={`accept-btn `}>使用本次</button>
                            <button className="reject-btn" onClick={(e) => rejectResult(e)}>不使用本次</button>
                            <button className="diff-btn" onClick={(e) => showDiffResult(e)}>对比差异</button>
                        </div>
                    </span>
                </div>
            }

            {
               artifactType === 'kemis' && scene_id !== 'ai_form' &&
                <div className={`ai-action-op-container use-tool-container ai-gene-form`}>
                    <div className="flex" onClick={openPreview}>
                        <GlobalOutlined />
                        <span className="use-tool-tip">
                            结果已生成
                        </span>
                    </div>
                    <div className="hover-buttons">
                        <Switch enabledText={'预览'} disabledText={'预览'} toggleSwitch={toggleAiGeneFormResult}  />
                    </div>
                </div>
            }

            {
                showToolResult && artifactType === 'applinkUrl' && scene_id !== 'ai_agent_app' &&
                <div className={`ai-action-op-container use-tool-container design2app`}>
                    <div className="flex" onClick={openPreview}>
                        <AppstoreAddOutlined />
                        <span className="use-tool-tip">应用[{getAppName()}]已生成，点击查看</span>
                    </div>
                </div>
            }

            {
                artifactType === 'TAB_LIST' && scene_id === 'ai_agent_app' &&
                <div className={`ai-action-op-container preview-pubapp `}>
                    <div className="flex gap-4 items-center cursor-pointer" onClick={openPreview}>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="#4f46e5" d="M2.5 7a4.5 4.5 0 1 0 9 0a4.5 4.5 0 0 0-9 0m0 10a4.5 4.5 0 1 0 9 0a4.5 4.5 0 0 0-9 0m10 0a4.5 4.5 0 1 0 9 0a4.5 4.5 0 0 0-9 0m5.025-5.845l.278-.636a4.9 4.9 0 0 1 2.496-2.533l.854-.38c.463-.205.463-.878 0-1.083l-.806-.359a4.9 4.9 0 0 1-2.533-2.617l-.285-.688a.57.57 0 0 0-1.058 0l-.285.688a4.9 4.9 0 0 1-2.533 2.617l-.806.359c-.463.205-.463.878 0 1.083l.854.38a4.9 4.9 0 0 1 2.496 2.533l.278.636a.57.57 0 0 0 1.05 0"/></svg>
                        <span>请稍等，应用即将呈现！</span>
                    </div>
                </div>
            }

            {
                showToolResult && artifactType === 'generatePreviewDone'  && 
                <div className={`ai-action-op-container preview-publish `}>
                    <div className="flex gap-4 items-center mt-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="none" stroke="#4f46e5" stroke-width="2" d="M23 1s-6.528-.458-9 2c-.023.037-4 4-4 4L5 8l-3 2l8 4l4 8l2-3l1-5s3.963-3.977 4-4c2.458-2.472 2-9 2-9Zm-6 7a1 1 0 1 1 0-2a1 1 0 0 1 0 2ZM7 17c-1-1-3-1-4 0s-1 5-1 5s4 0 5-1s1-3 0-4Z"/></svg>
                        <span>设计完成，您可以继续对话调整应用，或发布并体验应用</span>
                    </div>
                </div>
            }
            {
                artifactType !== 'AI_EDIT_ERROR' && version && version !== 'undefined' &&
                <div className={`ai-action-op-container new-version `}>
                    <div className="flex gap-4 items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="#4f46e5" d="M2.5 7a4.5 4.5 0 1 0 9 0a4.5 4.5 0 0 0-9 0m0 10a4.5 4.5 0 1 0 9 0a4.5 4.5 0 0 0-9 0m10 0a4.5 4.5 0 1 0 9 0a4.5 4.5 0 0 0-9 0m5.025-5.845l.278-.636a4.9 4.9 0 0 1 2.496-2.533l.854-.38c.463-.205.463-.878 0-1.083l-.806-.359a4.9 4.9 0 0 1-2.533-2.617l-.285-.688a.57.57 0 0 0-1.058 0l-.285.688a4.9 4.9 0 0 1-2.533 2.617l-.806.359c-.463.205-.463.878 0 1.083l.854.38a4.9 4.9 0 0 1 2.496 2.533l.278.636a.57.57 0 0 0 1.05 0"/></svg>
                        <span>{version} 已生成</span>
                    </div>
                </div>
            }

            {
                artifactType === 'AI_EDIT_ERROR' &&
                <>
                <div className={`ai-action-op-container ai-edit-error`}>
                    <div className="flex gap-4 items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 48 48"><g fill="none"><path fill="url(#IconifyId1963d2fe11d3342dd4)" d="M24 4c11.046 0 20 8.954 20 20s-8.954 20-20 20S4 35.046 4 24S12.954 4 24 4"/><path fill="url(#IconifyId1963d2fe11d3342dd5)" d="M24 13c.69 0 1.25.56 1.25 1.25v12.5a1.25 1.25 0 1 1-2.5 0v-12.5c0-.69.56-1.25 1.25-1.25m0 21a2 2 0 1 0 0-4a2 2 0 0 0 0 4"/><defs><linearGradient id="IconifyId1963d2fe11d3342dd4" x1="10.25" x2="36.5" y1="-2.25" y2="47.75" gradientUnits="userSpaceOnUse"><stop stop-color="#FFCD0F"/><stop offset="1" stop-color="#FE8401"/></linearGradient><linearGradient id="IconifyId1963d2fe11d3342dd5" x1="18.667" x2="29.067" y1="13" y2="34.131" gradientUnits="userSpaceOnUse"><stop stop-color="#4A4A4A"/><stop offset="1" stop-color="#212121"/></linearGradient></defs></g></svg>
                        <span>{artifactContent}</span>
                    </div>
                </div>
                </>
            }
            
            {
                artifactType === 'canvasCardForBRD' &&
                <div className="pb-3" onClick={showCanvas}>
                    <button className="cavas-card flex rounded-xl mb-1 -mx-0.5 overflow-hidden border transition w-full rounded-lg" aria-label="Preview contents">
                        <div className="flex flex-1 align-start justify-between w-full pl-0.5 py-0.5">
                            <div className="flex flex-col gap-1 py-4 px-4">
                                <div className="text-sm">产品方案</div>
                                <div className="text-sm text-text-300 opacity-100 transition-opacity duration-200">{new Date().toLocaleString()}&ensp;∙&ensp;(点击可查看和编辑)&nbsp;</div>
                            </div>
                        </div>
                    </button>
                </div>
            }
         </div> 
      </>
    )
}

export default ToolResult