import React, { useState } from "react";

import MarkdownComponent from "../../../../../components/msg-item/components/markdown";
import MsgExtRegion from "../../../msg-ext-region";
import MsgUtilSection from "../msg-util-section";
import { parseArtifactResult } from "../../../utils/ai-chat-utils"

import './index.less'

function ChatPMPilot(props): JSX.Element {
    let { data, extRegionInfos, knowledgeId, done, scene_id, type, reasoningContent, collapseMarkdown, foreignId } = props;
    
    const [isExpanded, setIsExpanded] = useState(!collapseMarkdown ? false : true);
    const [isExpandedMdData, setIsExpandedMdData] = useState(false);

    if (type === -225 || type === '-225') {
      const ar = parseArtifactResult(data);
      if (ar) {
        const { artifactType, extInfo, artifactContent } = ar;
        if (artifactType === 'extRegionInfos' && extInfo) {
          extRegionInfos = JSON.parse(extInfo);
          data = artifactContent;
        }
      }
    }

    return (
      <>
         <div className="msg-card-base relative">
            {
              knowledgeId && 
              <div className="knowledge-detail-container">
                <iframe className="iframe-page" frameBorder="0" src={`https://xiaobei.ke.com/#/detail/${knowledgeId}?pure=true`} height={"500px"} width={"100%"} />
              </div>
            }

            {
              reasoningContent && reasoningContent.length > 0 && (
                <div>
                  <div 
                    className="reasoning-title" 
                    onClick={() => setIsExpanded(!isExpanded)}
                  >
                    <span className="flex items-center mr-2">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="#666666" d="M3.299 17.596c.432 1.332 1.745 2.182 3.146 2.182H6.5A2.78 2.78 0 0 0 9.223 22c.457 0 .884-.115 1.262-.313a.99.99 0 0 0 .515-.882V3.027a1 1 0 0 0-.785-.983a2.32 2.32 0 0 0-1.479.201c-.744.356-1.18 1.151-1.18 1.978v.055a2.778 2.778 0 0 0-2.744 4.433A3.33 3.33 0 0 0 2 12c0 1.178.611 2.211 1.533 2.812c-.43.771-.571 1.746-.234 2.784m15.889-8.885a2.778 2.778 0 0 0-2.744-4.433v-.055c0-.826-.437-1.622-1.181-1.978a2.32 2.32 0 0 0-1.478-.201a1 1 0 0 0-.785.983v17.777c0 .365.192.712.516.882c.378.199.804.314 1.261.314a2.78 2.78 0 0 0 2.723-2.223h.056c1.4 0 2.714-.85 3.146-2.182c.337-1.038.196-2.013-.234-2.784A3.35 3.35 0 0 0 22 12a3.33 3.33 0 0 0-2.812-3.289"/></svg>
                      <span className="ml-2">思考过程</span>

                      <span className="ml-4">
                      {
                      isExpanded ? 
                      <span className="reasoning-collapse-icon">
                      收起 <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 48 48"><path fill="none" stroke="#cccccc" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="m13 30l12-12l12 12"/></svg>
                      </span> : 
                      <span className="reasoning-collapse-icon">
                      展开 <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 48 48"><path fill="none" stroke="#cccccc" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M36 18L24 30L12 18"/></svg>
                      </span>
                      }
                      </span>
                      
                    </span>
                  </div>
                  {isExpanded && <MarkdownComponent content={reasoningContent} />}
                </div>
              )
            }
            
            {
              data && data.length > 0 &&
              (
              !collapseMarkdown ? 
              <MarkdownComponent content={data} /> :
              <div>
                <div 
                  className="cursor-pointer" 
                  onClick={() => setIsExpandedMdData(!isExpandedMdData)}
                >
                  <span style={{ marginRight: '4px' }}>
                    结果说明:
                    <span className="ml-4">
                    {
                    isExpandedMdData ? 
                    <span className="reasoning-collapse-icon">
                    收起 <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 48 48"><path fill="none" stroke="#cccccc" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="m13 30l12-12l12 12"/></svg>
                    </span> : 
                    <span className="reasoning-collapse-icon">
                    展开 <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 48 48"><path fill="none" stroke="#cccccc" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M36 18L24 30L12 18"/></svg>
                    </span>
                    }
                    </span>
                  </span>
                </div>
                {isExpandedMdData && <MarkdownComponent content={data} />}
              </div>
              )
            }
            
            { extRegionInfos && 
              extRegionInfos.map((item, index) => <MsgExtRegion key={index} toolName={item.toolName} data={item.data} scene_id={scene_id} /> )
            }

            {
              (done || foreignId) && <MsgUtilSection props={props} />
            }
         </div> 
      </>
    )
}

export default ChatPMPilot