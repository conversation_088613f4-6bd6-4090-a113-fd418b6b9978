import React, { useState } from 'react';
import ChatPMPilot from "../chat-pm-pilot";

import './index.less'

const GroovyCards = ({ cards }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const scrollToNextCard = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % cards.length);
  };

  const scrollToPrevCard = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1) % cards.length);
  };

  return (
    <div className="">
        <div className="msg-wrapper service-msg">
            <div className="msg-card-base">
                <div className="msg-content-title">
                    <h3>共找到{cards.length}段代码,当前是第{currentIndex + 1}段</h3>
                </div>
                <div className="msg-content-body">
                    {
                    cards.map((item, index) => (
                        <div key={index} className={`groovy-item ${index === currentIndex ? 'block' : 'hidden'}`}>
                        <div className="groovy-item-title">
                            <span>作者:{item.groovyAuthor}</span>
                            <span className={`float-right ${item.score < 0.5 ? 'block color-red' : 'hidden'}`}>(较弱)</span>
                            <span className='float-right'>相关性:{item.score.toFixed(2)}</span>
                        </div>
                        <div className="groovy-item-content">
                            <ChatPMPilot data={item.groovyCode} />
                        </div>
                        </div>
                    ))
                    }
                </div>
                <div >
                    {
                        currentIndex > 0 &&
                        <button className='prev-groovy' onClick={scrollToPrevCard}>上一段代码</button>
                    }
                    {
                        currentIndex < cards.length - 1 &&
                        <button className='next-groovy' onClick={scrollToNextCard}>下一段代码</button>
                    }
                </div>
            </div>
        </div>
    </div>
  );
};

function GroovyList(props: any): JSX.Element {
    const { groovyList } = props;

    return (
        <div className='ai-groovy-list'>
            {
                groovyList && groovyList.length > 0 &&
                <GroovyCards cards={groovyList} />
            }
        </div>
      );
}

export default GroovyList;