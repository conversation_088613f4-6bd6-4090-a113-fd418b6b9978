
import React, { useEffect, useState, useRef,useLayoutEffect } from "react";
import { connect } from "react-redux";

import ImApi from "@/common/service/im";
import { genUrlParams } from "@/common/utils/url";
import MsgItem from "../../../components/msg-item"
import { mapStateToProps } from "../utils/redux"
import { scrollToBottom } from "../utils/ai-chat-utils"

function HistorySection(props: any): JSX.Element {
  const urlParams = genUrlParams() || {};
  const [historyList, setHistoryList] = useState([]);
  const historyListRef = useRef<HTMLDivElement>(null);

 
  const getMsgHistory = async() => {
    if (urlParams.contextId) {
        const data = {
            sessionId: "ai" + urlParams.contextId, 
            limit: 100,
        };
        ImApi.getConversationMessages(data)
            .then((list: any) => {
                if (!list || !list.length) return;
                const listWithLastMsg = list.map((item, idx) => ({
                  ...item,
                  lastMsg: idx === list.length - 1 ? "true" : "false"
                }));
                setHistoryList(listWithLastMsg);
            })
            .catch((x) => x);
    }
  };
  
  useEffect(() => {
    getMsgHistory();
  }, []);

  useLayoutEffect(() => {
    if (historyList.length > 0) {
      // 使用 setTimeout 来确保在所有 MsgItem 渲染完成后再滚动
      setTimeout(scrollToBottom, 100);
    }
  }, [historyList]);


  return (
    <div className="ai-history-section " id="historyList" ref={historyListRef}>
        {historyList.map((msg, index) => (<MsgItem key={`${msg.id}${index}`} {...msg} msgId={msg.msgIdSequence}  />))}
    </div>
  );
}

export default connect(
  mapStateToProps
)(HistorySection);