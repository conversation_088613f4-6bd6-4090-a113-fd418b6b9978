import React, { useEffect, useState, useRef } from 'react'
import { Toast } from 'antd-mobile';
import Switch from './switch';
import axios from 'axios';
import { Tooltip } from 'react-tooltip';
import { DIG_TYPE, EVT_ID, report_dig, UI_CODE } from './utils/aigc_dig';
import { findAiAppInfo } from './utils/agent-api-utils'

function ChatInput(props: any): JSX.Element {
    
    const [value, setValue] = useState("")
    const [reasonThinkEnabled, setReasonThinkEnabled] = useState(false);
    const { functions, changeFunction, isShowStopBtn,changeStop,curFunction,toggleSwitch,curScene,handleSendMsg,inputPlaceholder,setInputPlaceholder,contextId } = props
    const [attachments, setAttachments] = useState<{ url: string; name: string }[]>([]);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const [mastergoEnabled, setMastergoEnabled] = useState(false);
    const [aiAppInfo, setAiAppInfo] = useState({'contextId': contextId, 'status': 'requirement'})

    // 修改自动调整高度的函数
    const adjustTextareaHeight = () => {
        const textarea = textareaRef.current;
        if (!textarea) return;

        const currentSelectionStart = textarea.selectionStart;
        const isAtBottom = currentSelectionStart === textarea.value.length;
        
        // 保存当前内容和滚动位置
        const text = textarea.value;
        const currentScrollPos = textarea.scrollTop;
        
        // 创建一个隐藏的克隆元素来测量实际高度
        const clone = textarea.cloneNode() as HTMLTextAreaElement;
        clone.style.position = 'absolute';
        clone.style.visibility = 'hidden';
        clone.style.height = '96px'; // 设置默认高度
        clone.value = text;
        document.body.appendChild(clone);
        
        // 获取实际需要的高度
        const scrollHeight = clone.scrollHeight;
        document.body.removeChild(clone);
        
        // 根据实际内容高度设置textarea的高度
        if (scrollHeight <= 96) {
            textarea.style.height = '96px';
        } else {
            textarea.style.height = `${Math.min(scrollHeight, 272)}px`;
        }
        
        // 只在光标在末尾时滚动到底部
        if (isAtBottom) {
            scrollToBottom();
        } else {
            textarea.scrollTop = currentScrollPos;
        }
    };

    const scrollToBottom = () => {
      var targetElement = document.getElementById('input-bottom');
      if (!targetElement) return;
      // 滚动到指定元素的位置
      targetElement.scrollIntoView({ behavior: 'smooth' });
    };

    //查询项目信息
    const loadAppInfo = async () => {
        const app = await findAiAppInfo(contextId);
        if(app) {
            const appInfo = {...aiAppInfo, ...app}
            setAiAppInfo(appInfo)
        }
    }

    // 在 value 改变时调整高度
    useEffect(() => {
        adjustTextareaHeight();
    }, [value]);

    //加载组件初始化
    useEffect(() => {
        if(curScene == 'ai_agent_app') {
            loadAppInfo()
        }
    }, [])

    // 发送消息
    const handleSubmit=()=>{
      if (mastergoEnabled) {
        const mastergoRegex = /^https:\/\/(mastergo\.com|bkmuc\.ke\.com)\/file\/\d+\?(?=.*page_id=[^&]+)(?=.*layer_id=\d+%3A\d+)/;
        if (!mastergoRegex.test(value)) {
            Toast.fail('请输入正确的Mastergo文件地址，格式如：https://xxx.com/file/159588388892349?page_id=M&layer_id=1%3A03');
            return;
        }
      }

      let fileUrl = "";
      if (attachments && attachments.length > 0) {
        fileUrl = attachments.map(attachment => attachment.url).join(',');
      }
      report_dig(curScene, DIG_TYPE.CUSTOM_EVENT, {'str1': aiAppInfo.contextId, 'str2': aiAppInfo.status, 'str3': value}, EVT_ID.USER_SEND_CONTENT, UI_CODE.REQUIRE_DESIGN_CODE)
      handleSendMsg(value, { fileUrl, reasonThinkEnabled, mastergoEnabled }, () => {
        setValue("");
        setAttachments([]);
      });
    }

    const handleOptionChange = (event) => {
      changeFunction(event.target.value);
    };

    // 停止执行，不显示停止按钮
    const handleStop = () => {
      changeStop(false);
    };

    // 是否展示图片上传按钮
    const showImgUploadBtn = () => {
        return curScene.includes('ai_form') || (curScene.includes('ai_workbench') && (curFunction==='ai_form' || curFunction === 'requirement2PageCode')) || (curScene.includes('ai_home') && curFunction==='ai_form') || curScene.includes('ai_agent_app');
    }

    // 是否展示Mastergo输入按钮
    const showMastergoBtn = () => {
        return (curScene.includes('ai_workbench') && curFunction==='ai_form') || (curScene.includes('ai_home') && curFunction==='ai_form');
    }

    // 是否展示深度思考按钮
    const showReasonBtn = () => {
        return (curScene.includes('ai_workbench') && curFunction==='ai_form') || (curScene.includes('ai_home') && curFunction==='ai_form');
    }

    // 上传文件
    const uploadImage = async (file: File) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('appCode', 'LrbpEpqneJzgaMnvBSljDlrWMxlMlkIR');
        formData.append('isPublic', 'true');

        try {
            const response = await axios.post('https://luoshu.ke.com/web/file/uploadNew', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                withCredentials: true
            });
            return response?.data?.data?.url;
        } catch (error) {
            console.error('Error uploading image:', error);
            Toast.fail('图片上传失败');
            return null;
        }
    };

    // 处理文件上传
    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(event.target.files || []);
        for (const file of files) {
            const imageUrl = await uploadImage(file);
            if (imageUrl) {
                setAttachments(prev => [...prev, { url: imageUrl, name: file.name }]);
            }
        }
        // 重置 input 的 value，确保相同文件可以再次上传
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    // 删除附件
    const removeAttachment = (index: number) => {
        setAttachments(prev => {
            const newAttachments = [...prev];
            newAttachments.splice(index, 1);
            return newAttachments;
        });
    };

    // 点击上传附件
    const handleAttachmentClick = () => {
        if (fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    // 选择Mastergo输入
    const handleMastergoClick = () => {
        const msgoEnable = !mastergoEnabled;
        setMastergoEnabled(msgoEnable);
        if (msgoEnable) {
            setInputPlaceholder('输入mastergo地址，必须包含layer_id，比如：https://mastergo.com/file/159588388892349?page_id=M&layer_id=1%3A03');
        } else {
            setInputPlaceholder('请输入您的需求');
        }
    };

    // 新增粘贴处理函数
    const handlePaste = async (event: React.ClipboardEvent) => {
        const items = event.clipboardData.items;
        console.log('items:', items);
        for (const item of items) {
            if (item.type.indexOf('image') === 0) {
                event.preventDefault();
                const file = item.getAsFile();
                if (file) {
                    const imageUrl = await uploadImage(file);
                    if (imageUrl) {
                        setAttachments(prev => [...prev, { url: imageUrl, name: '粘贴图片' }]);
                    }
                }
            }
        }
    };

    return (
        <div 
            className={`w-full bg-opacity-10 max-h-68 rounded-lg py-4 px-4 overflow-auto relative border-slate-300 border mb-2  bg-white `}
            onPaste={handlePaste}  // 添加粘贴事件监听
        >
            {
                functions && functions.filter(func => curScene === "ai_agent" || func.functionShow === true).length > 0 && 
                <div>
                    <span className="mr-2 text-neutral-700">功能:</span>
                    <select
                    value={curFunction}
                    onChange={handleOptionChange}
                    className="p-2 border-0 bg-transparent outline-none text-neutral-700 font-serif sky-800 "
                  >
                    {
                        functions.map((scene, index) => (
                            <option key={index} value={scene.key} >
                                {scene.value}
                            </option>
                        ))
                    }
                  </select>

                  {
                    (curFunction === "" || curFunction === 'generate_groovy') &&
                    <div className="flex items-center justify-center h-screen input-switch">
                        <div className="w-full max-w-md">
                            <Switch enabledText={'使用出入参'} disabledText={'不使用出入参'} toggleSwitch={toggleSwitch} />
                        </div>
                    </div>
                  }
                  
                </div>
            }

            <div className="flex flex-wrap mb-2">
                {attachments.map((attachment, index) => (
                    <div key={index} className="relative mr-2 mb-2">
                        <img src={attachment.url} alt={attachment.name} className="w-16 h-16 object-cover rounded" />
                        <button
                            onClick={() => removeAttachment(index)}
                            className="absolute top-0 right-0 bg-red-500 rounded-full w-5 h-5 flex items-center justify-center"
                        >
                            ×
                        </button>
                    </div>
                ))}
            </div>

            <div className="flex flex-col">
                <textarea 
                    ref={textareaRef}
                    onKeyDown={(e)=>{
                        if (e.key === 'Enter') {
                            if (!e.shiftKey && !e.ctrlKey) {
                                // 普通回车：提交
                                e.preventDefault();
                                handleSubmit();
                            } else if (e.ctrlKey) {
                                // Shift+Enter 或 Ctrl+Enter：换行
                                e.preventDefault();
                                // 在光标位置插入换行符
                                const { selectionStart, selectionEnd } = e.currentTarget;
                                const newValue = value.substring(0, selectionStart) + '\n' + value.substring(selectionEnd);
                                setValue(newValue);
                                // 下一个事件循环中设置光标位置
                                setTimeout(() => {
                                    e.target.selectionStart = e.target.selectionEnd = selectionStart + 1;
                                }, 0);
                            }
                        }
                    }}
                    value={value}
                    placeholder={inputPlaceholder}
                    onChange={(e)=>{
                        setValue(e.target.value);
                        adjustTextareaHeight();
                    }}
                    rows={1}
                    style={{ height: '6rem', minHeight: '6rem' }}  // 添加最小高度
                    className={`border-0 bg-transparent outline-none w-full text-neutral-700 font-sans p-0.5 overflow-y-auto max-h-64`}
                />
                
                <div className="flex items-center mt-2">
                    
                    {
                        showReasonBtn() &&
                        <div className="flex items-center">
                            <button
                                id='reason-btn-switch'
                                onClick={() => setReasonThinkEnabled(!reasonThinkEnabled)}
                                // disabled={attachments.length > 0}
                                className={`px-3 py-1 rounded-md text-sm mr-2 ${
                                    reasonThinkEnabled
                                    ? 'bg-blue-100 text-blue-600' 
                                    : 'bg-gray-100 text-gray-600'
                                }`}
                            >
                                深度思考
                            </button>

                            <Tooltip
                            anchorSelect="#reason-btn-switch"
                            content="回复前先思考，更精准但会慢些"
                            />
                        </div>
                    }
                    
                    
                    <div className="flex flex-col items-center ml-2">
                        <input
                            type="file"
                            ref={fileInputRef}
                            onChange={handleFileUpload}
                            accept="image/*"
                            className="hidden"
                            multiple={false}
                        />
                        {
                            showImgUploadBtn() &&
                            <>
                            <div 
                                id="upload-img-icon"
                                onClick={handleAttachmentClick}
                                className='absolute bottom-4 right-16 cursor-pointer ease-in duration-100 hover:scale-125 dynamic-btn'   
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><g fill="none"><path stroke="#666666" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 2v3m0 3V5m0 0h3m-3 0h-3"/><path fill="#666666" fill-rule="evenodd" d="M13 2H5a3 3 0 0 0-3 3v10.5q0 .13.032.25A1 1 0 0 0 2 16v3a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3v-7a1 1 0 0 0-.032-.25A1 1 0 0 0 22 11.5V11h-2v.016c-4.297.139-7.4 1.174-9.58 2.623c.826.293 1.75.71 2.656 1.256c1.399.84 2.821 2.02 3.778 3.583a1 1 0 1 1-1.706 1.044c-.736-1.203-1.878-2.178-3.102-2.913c-1.222-.734-2.465-1.192-3.327-1.392a15.5 15.5 0 0 0-3.703-.386h-.022q-.522.008-.994.045V5a1 1 0 0 1 1-1h8zM8.5 6a2.7 2.7 0 0 0-1.522.488C6.408 6.898 6 7.574 6 8.5s.408 1.601.978 2.011A2.67 2.67 0 0 0 8.5 11c.41 0 1.003-.115 1.522-.489c.57-.41.978-1.085.978-2.011s-.408-1.601-.978-2.012A2.67 2.67 0 0 0 8.5 6" clip-rule="evenodd"/></g></svg>
                            </div>
                            <Tooltip
                                anchorSelect="#upload-img-icon"
                                content="上传图片"
                            />
                            </>
                        }

                        {
                            showMastergoBtn() && 
                            <>
                                <div 
                                    id="input-mastergo-icon"
                                    onClick={handleMastergoClick}
                                    className={`absolute bottom-4 right-24 cursor-pointer ease-in duration-100 hover:scale-125 dynamic-btn`}
                                    >
                                        {
                                            mastergoEnabled ? 
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 15 15"><path fill="#c026d3" fill-rule="evenodd" d="M7.26 3.167L4.37 5.333V1zM1 8.222l2.889-2.166L1 3.889zM1 14l2.889-2.167L1 9.667zm6.74-5.778l2.89-2.166l-2.89-2.167zM14 3.167l-2.889 2.166V1zm-2.889 7.944L14 8.944l-2.889-2.166zm-7.222 0L1 8.944l2.889-2.166zm.481-5.055l2.89 2.166V3.89zm-.481-.723L1 3.167L3.889 1zM7.74 3.167l2.889 2.166V1zM14 8.222l-2.889-2.166L14 3.889zm-2.889 3.611L14 14V9.667z" clip-rule="evenodd"/></svg>
                                            :
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 15 15"><path fill="#666666" fill-rule="evenodd" d="M7.26 3.167L4.37 5.333V1zM1 8.222l2.889-2.166L1 3.889zM1 14l2.889-2.167L1 9.667zm6.74-5.778l2.89-2.166l-2.89-2.167zM14 3.167l-2.889 2.166V1zm-2.889 7.944L14 8.944l-2.889-2.166zm-7.222 0L1 8.944l2.889-2.166zm.481-5.055l2.89 2.166V3.89zm-.481-.723L1 3.167L3.889 1zM7.74 3.167l2.889 2.166V1zM14 8.222l-2.889-2.166L14 3.889zm-2.889 3.611L14 14V9.667z" clip-rule="evenodd"/></svg>
                                        }
                                </div>
                                <Tooltip
                                    anchorSelect="#input-mastergo-icon"
                                    content="输入Mastergo文件地址"
                                />
                            </>
                        }

                        <div 
                            id="sendmsg"
                            onClick={handleSubmit}
                            className='absolute bottom-4 right-3 cursor-pointer ease-in duration-100 hover:scale-125 dynamic-btn'   
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="23" height="23" viewBox="0 0 14 14"><path fill="#4f46e5" fill-rule="evenodd" d="M13.854.146a.5.5 0 0 1 .113.534l-5 13a.5.5 0 0 1-.922.027l-2.091-4.6L9.03 6.03a.75.75 0 0 0-1.06-1.06L4.893 8.046l-4.6-2.09a.5.5 0 0 1 .028-.923l13-5a.5.5 0 0 1 .533.113" clip-rule="evenodd"/></svg>
                        </div>

                        {
                            isShowStopBtn &&
                            <div 
                                onClick={handleStop}
                                className='absolute top-4
                                right-3 cursor-pointer ease-in duration-100 hover:scale-125 '   
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 24 24"><path fill="#474865" d="M9 16h6q.425 0 .713-.288T16 15V9q0-.425-.288-.712T15 8H9q-.425 0-.712.288T8 9v6q0 .425.288.713T9 16m3 6q-2.075 0-3.9-.788t-3.175-2.137T2.788 15.9T2 12t.788-3.9t2.137-3.175T8.1 2.788T12 2t3.9.788t3.175 2.137T21.213 8.1T22 12t-.788 3.9t-2.137 3.175t-3.175 2.138T12 22"/></svg>
                            </div>
                        } 

                    </div>
                </div>
            </div>
            <div id="input-bottom"></div>
        </div>
    )
}

export default ChatInput;