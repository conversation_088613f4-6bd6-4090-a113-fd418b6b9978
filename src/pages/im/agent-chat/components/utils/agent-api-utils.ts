import request from "@/common/request";
import axios from "axios";
import { gatewayHost } from "@/common/utils/urlFig";
  

// 获取智能体配置
export const getAgentConfigByAgentId = (agentId) => {
    const ackUrl = `${gatewayHost}/web/fe/sinanai/aiAgentServiceImpl/getCommonAgentConfig?agentId=${agentId}`;
    return request.get(ackUrl, {withCredentials: true});
}

// 获取AI工作台配置
export const getAiWorkbenchConfig = async (scene) => {
    const response = await axios.post(`${gatewayHost}/web/v1/ls/dPZiZnkXLcrRxksHDIlGGrNtSJAbvisk/convertAIProjectToAgentFunction`, 
        {
            scene
        }, 
        {
        headers: {
            'Content-Type': 'application/json',
        },
        withCredentials: true
    });
    return response?.data?.data;
}


// 获取AI项目状态
export const findAiProjectStatus = (contextId) => {
    const url = `${gatewayHost}/web/fe/sinanai/workbenchServiceImpl/findAiProjectStatus?contextId=${contextId}`
    return request.get(url, {withCredentials: true});
}

// 获取AI应用信息
export const findAiAppInfo =  (contextId: string) => {
    return request.get(`${gatewayHost}/web/fe/sinanai/workbenchServiceImpl/findAiAppInfo?contextId=${contextId}`);
}