import Store from "@/common/store";
import {initUlog} from '@lianjia/diglog-js'

export const enum DIG_TYPE {

  MODULE_CLICK = 'Module_Click',
  PAGE_VIEW = 'Page_View',
  CUSTOM_EVENT = "Custom_Track"

}

export const enum EVT_ID {
  
  USER_SEND_CONTENT = '87323',
  RE_GENERATE_ONCLICK = '87327',
  BEGIN_DESIGN_ONCLICK = '87317',
  PAGE_VIEW = '1,3'
}

export const enum UI_CODE {
  AGENT_HOME_CODE = "ls_ai_agent_homepage",
  REQUIRE_DESIGN_CODE = "ls_ai_agent_requirementdesign",
}

const initAiUlog = () => {
  const { protocol, host } = location;
  let target = ''
  if (host.includes('test') || host.includes('dev')) {
    target = '//dig.lianjia.com/check.gif';
  } else {
    target = '//dig.lianjia.com/c.gif';
  }
  // 埋点上报通用字段配置
  const udlConfig = {
    pid: 'luoshu_pc'
  };

  // 埋点设置
  const ulogConfig = {
    target: `${protocol}${target}`
  };
  const ulog = initUlog(udlConfig, ulogConfig);
  window.$AIGC_ULOG = ulog

}

/**
 * 上报埋点
 * @param scence 
 * @param dig_type 
 * @param dig_action 
 * @param evt_id 
 * @param ui_code 
 * @returns 
 */
export const report_dig = async (scence: string, dig_type: DIG_TYPE, dig_action: {}, evt_id?: string, ui_code?: string) => {
  try {
    if(scence !== 'ai_agent_app') {
      return
    }
    const { user: { userInfo } } = Store.getState();
    const action = { ...dig_action, 'cityCode': userInfo?.cityCode, 'user_code': userInfo?.sysCode, 'user_name': userInfo?.name, 'click_time': Date.now() }
    const param = {'uicode': ui_code};
    trackAction(evt_id, param, action, dig_type);
  } catch (error) {
    console.log('【report_dig_err】' + error);
  }
  
} 

const getCookie = (cookiename: string) => {
  let arr,
  reg = new RegExp('(^| )' + cookiename + '=([^;]*)(;|$)')
  if (!document.cookie) return ''
  arr = document.cookie.match(reg)
  if (arr) {
    return decodeURIComponent(arr[2])
  } else {
    return ''
  }
}

function getParam(str: string) {
  const queries: Record<string, string> = {}
  if (!str) {
    return {}
  }
  str.split('&').forEach((c) => {
    if (c === '') {
      return
    }
    const [key, ...values] = c.split('=')
    let decodedKey
    let decodedValue
    try {
      decodedKey = decodeURIComponent(key)
    } catch (err) {
      decodedKey = key
    }
    try {
      decodedValue = decodeURIComponent(values.join('='))
    } catch (err) {
      decodedValue = values.join('=')
    }
    queries[decodedKey] = decodedValue || ''
  })
  return queries
}

const urlQuery = () => {
  const hash = window.location.href;
  const index = location.href.indexOf("?") + 1;
  const params = hash.substring(index);
  const mIndex = params.indexOf("#");
  return mIndex > -1 ? getParam(params.substring(0, mIndex)) : getParam(params)
}

function trackAction(evt, param = {}, action, event) {
  const token = getCookie('lianjia_token');
  let urlParams = urlQuery();
  const { user: { userInfo } } = Store.getState();
  let { ucId } = userInfo || {}; 
  const pid = 'luoshu_pc';
  if (urlParams.operator) {
    delete urlParams.operator;
  }

  param.action = { ...urlParams, ...action };
  if (!('$AIGC_ULOG' in window)) {
    initAiUlog()
  }
  const { href, protocol, host, pathname } = location;
  let key = href;
  if (href.length > 1000 || pathname.toLowerCase() === "/dynamicformrender") {
    key = `${protocol}${host}${pathname}`;
  }
  window.$AIGC_ULOG.send(
    evt,
    Object.assign({}, { token, ucid: ucId, pid, event, key }, param)
  );
}