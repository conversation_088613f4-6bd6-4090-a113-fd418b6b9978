import axios from "axios";
import { randomId } from "@/common/utils";
import { gatewayHost } from "@/common/utils/urlFig";
import { getAppCodeFromAppUrl } from "@/common/utils/regexp";
import { 
  getCommonAigcResult, 
  invokeToolForGenerateForm,
  getAigcResult
} from "./ai-chat-utils";
import ImApi from "@/common/service/im";
import { Toast } from 'antd-mobile';


export const parseAiResultEvent = (event, data) => {
  if (event === 'BEGIN_DESIGN') {
    // AI开始设计
    pubEvent({ type: 'aiDesgining'});
  } else if (event === 'DESIGNING') {
    // 设计中过程同步
    pubEvent({ type: 'aiDesigningProcessDesc', data });
  }
}

// BRD提示语
export const brdWelcomeMsg = "<p>您好，根据您的需求，左侧是我为您生成的产品方案，您可以通过与我对话进行进一步修改，也<span style='font-weight: 600;'>可以直接手动编辑</span>。修改完成后，点击下方开始设计即可生成应用。</p>";

// 解析流式数据
export const parseAigcChunkResult = (result: string) => {
  let intent = "Q&A";
  if (result.includes("<intent>design</intent>")) {
    // AI设计，打开画布
    intent = "design";
    ImBus.emit("SHOW_CANVAS", true);
    ImBus.emit("CANVAS_CHUNK", result);
  }
  return intent;
}

// 处理Ai生成的内容
export const dealAigcResult = (scene, contextId, addSingleMsg, result: string) => {
  const extractResult = extractIntentAndResultRobust(result);
  if (extractResult.intent === 'design') {
    // 发送AI设计内容，在画布上展示
    ImBus.emit("FULL_CANVAS_CONTENT", extractResult.result);
    // 生成消息卡片
    const msg_data = { id: randomId(), type: -227, direction: 2, query_content: '', scene_id: scene, sessionId: contextId };
    const msg_content = "<Artifact type=\"canvasCardForBRD\">\n" + extractResult.result + "\n</Artifact>";
    addSingleMsg(msg_data, msg_content);
  }
}

/**
 * 从字符串中提取attempt_completion标签中的intent和result内容
 * @param input 包含attempt_completion标签的字符串
 * @returns 包含intent和result的对象
 */
interface ExtractResult {
  intent: string | null;
  result: string | null;
}

export function extractIntentAndResultRobust(input: string): ExtractResult {
  const extractResult: ExtractResult = {
    intent: null,
    result: null
  };

  try {
    // 清理输入字符串，处理转义字符
    const cleanInput = input.replace(/\\n/g, '\n').replace(/\\\\/g, '\\');
    
    // 提取intent内容 - 支持多行和空白字符
    const intentRegex = /<intent>\s*([\s\S]*?)\s*<\/intent>/i;
    const intentMatch = cleanInput.match(intentRegex);
    if (intentMatch) {
      extractResult.intent = intentMatch[1].trim();
    }

    // 提取result内容 - 支持多行和空白字符
    const resultRegex = /<result>\s*([\s\S]*?)\s*<\/result>/i;
    const resultMatch = cleanInput.match(resultRegex);
    if (resultMatch) {
      extractResult.result = resultMatch[1].trim();
    }
  } catch (error) {
    console.error('Error extracting intent and result:', error);
  }

  return extractResult;
}

/**
 * 移除字符串中的thinking和use_mcp_tool标签及其内容
 * @param input 输入字符串
 * @returns 处理后的字符串
 */
export function removeTagsAndContent(scene: string, input: string): string {
  // 移除<thinking>和</thinking>之间的内容（包含标签本身）
  let result = input.replace(/<thinking>[\s\S]*?<\/thinking>/gi, '');
  
  // 移除<use_mcp_tool>和</use_mcp_tool>之间的内容（包含标签本身）
  result = result.replace(/<use_mcp_tool>[\s\S]*?<\/use_mcp_tool/gi, '');
  result = result.replace(/<use_mcp_tool>[\s\S]*?<\/use_mcp_tool>/gi, '');
  
  // 内容替换
  // if (scene === 'ai_groovy') {
  //   result = input.replace(/\\n/g, '\n\n');
  // }

  return result;
}


// 获取智能体对话接口
export const getChatUrl = (originUrl, scene, func, sseUrlReplace) => {
  
  if (sseUrlReplace) {
    return originUrl.replace("/sse/chat", sseUrlReplace);
  }

  return originUrl;
}

// 当前用户名+系统号
export const getUserDisplayName = (userInfo) => {
  return `${userInfo.name}（${userInfo.sysCode}）`;
}

// 解析工具结果
export const parsePriorityToolResult = (props, curScene, requestIdRef, contextId, appCodeRef, addSingleMsg, saveConversationForm, curFunc, setUseTool, schemaRef, setDisableInputBox, userInfo, msyPayloadRef, tool_result) => {
  const { ucId } = userInfo;
  const toolResult = JSON.parse(tool_result);
  const { eventType, requestId, } = toolResult;
  const msg_data = { id: randomId(), type: -227, direction: 2, query_content: '', scene_id: curScene, sessionId: contextId, cur_func: curFunc };
   
  if ("TAB_LIST" === eventType) {
    // AI识别到要生成的模块
    const msg_content = "<Artifact type=\"" + eventType + "\">\n" + tool_result + "\n</Artifact>";
    addSingleMsg(msg_data, msg_content);
  } else if ("AI_EDITING" === eventType) {
    // AI生成内容进行版本迭代
    requestIdRef.current = requestId;
    doAiEdit(curScene, requestIdRef, contextId, appCodeRef.current, addSingleMsg, curFunc, setUseTool, userInfo);
  } else if ("FETCH_MASTERGO_RESULT" === eventType || "GENERATE_PAGE_CODE" === eventType) {
    // 拉取结果
    setUseTool(true);
    getAigcResult(ucId, requestId, msyPayloadRef.current, curScene, contextId, appCodeRef, null, setDisableInputBox, schemaRef, addSingleMsg, null, setUseTool, saveConversationForm, props);
  }
}

// 执行AI编辑任务
const doAiEdit = (curScene, requestIdRef, contextId, appCode, addSingleMsg, curFunc, setUseTool, userInfo) => {
  const msg_data = { id: randomId(), type: -227, direction: 2, query_content: '', scene_id: curScene, sessionId: contextId, cur_func: curFunc };
  // 发送设计中事件
  pubEvent({ "type": 'aiDesgining'});
  setUseTool(true);
  // AI修改中，拉取结果
  getCommonAigcResult(requestIdRef.current, curScene, appCode, addSingleMsg, setUseTool, userInfo, 'AI_EDIT_COMPLETE', async (data) => {
    setUseTool(false);
    // 发送设计完成事件
    pubEvent({ "type": 'AI_EDIT_COMPLETE', data });
    // 产生新消息
    const msg_content = "<Artifact type=\"AI_EDITING\" version=\"" + data?.record?.version + "\">\n" + data?.record?.version + "\n</Artifact>";
    addSingleMsg(msg_data, msg_content);

    // 延迟发送事件
    await delay(1000);
    pubEvent({ "type": 'aiResultReload', });
  }, err => {
    console.log("AI拉取设计结果出现异常，", err);
    // 发布异常事件
    pubEvent({ "type": 'AI_EDIT_OCCUR_ERROR' });
    // 消息提示
    if (err.eventType === 'AI_EDIT_ERROR') {
      msg_data.type = -225;
      addSingleMsg(msg_data, '<div class="color-red">抱歉出错了，您可以点击对话框中气泡尝试重新生成</div>');
    }
  });

}


// 监听事件然后做响应处理
export const listenEventAndHandleMsg = (chat, userInfo, func, scene, contextId, appCodeRef, requestIdRef, initData, msyPayloadRef, addSingleMsg, setUseTool, event, actionCodeRef) => {
  const { type, data } = event.data;

  if (type.includes("linkUrl")) {
    // 链接消息
    const msg_data = { id: randomId(), type: -227, direction: 2, query_content: '', scene_id: scene, sessionId: contextId };
    const msg_content = "<Artifact type=\"" + type + "\">\n" + data + "\n</Artifact>";
    addSingleMsg(msg_data, msg_content);
  } else if (type === 'doAppPub') {
    // 发布应用
    const { url, requestId } = data;
    const appCode = getAppCodeFromAppUrl(url);
    appCodeRef.current = appCode;
    requestIdRef.current = requestId;
    const msg_data = { id: randomId(), type: 120, direction: 2, query_content: '', scene_id: scene, sessionId: contextId };
    const msg_content = "小洛正在为您生成应用并发布至测试环境，请您稍等片刻请勿关闭对话框！^_^";
    addSingleMsg(msg_data, msg_content);
    publishApp(userInfo, contextId, scene, addSingleMsg, setUseTool, appCodeRef, requestIdRef, initData, 'test');
  } else if (type === 'generatePreviewDone') {
    // AI生成预览内容加载完毕
    const msg_data = { id: randomId(), type: -227, direction: 2, query_content: '', scene_id: scene, sessionId: contextId };
    const msg_content = "<Artifact type=\"" + type + "\">\n" + data + "\n</Artifact>";
    addSingleMsg(msg_data, msg_content);
  } else if (type === 'generateAppAck') {
    // 开始生成应用
    setUseTool(true);
  } else if (type === 'doAppPubToProd') {
    // 应用发布到线上
    const { url, requestId } = data;
    const appCode = getAppCodeFromAppUrl(url);
    appCodeRef.current = appCode;
    if (requestId) {
      requestIdRef.current = requestId;
    }
    publishApp(userInfo, contextId, scene, addSingleMsg, setUseTool, appCodeRef, requestIdRef, initData, 'test', true);
  } else if (type === 'doAiDesignFromPreview') {
    // 去设计
    const data = {
      sessionId: "ai" + contextId, 
      limit: 100,
  };
    ImApi.getConversationMessages(data)
      .then((list: any) => {
          if (!list || !list.length) return;
          // 最后一条消息当作需求，去设计应用
          const requirment = list[list.length - 1]?.data;
          const data = { msg:'去设计', sendmsg:false, nextAction: 'generatePreviewApp' };
          handleBtnAction(userInfo, func, scene, contextId, undefined, appCodeRef, requestIdRef, '', undefined, addSingleMsg, setUseTool, chat, '', undefined, undefined, undefined, undefined, undefined, requirment, '', '', data);
      })
      .catch((x) => x);
  } else if (type === 'AI_DESIGN_COMPLETE_V1') {
    // 第一版内容已生成
    const msg_data = { id: randomId(), type: -227, direction: 2, query_content: '', scene_id: scene, sessionId: contextId };
    const msg_content = "<Artifact type=\"" + type + "\" version=\"V1\">\n" + data + "\n</Artifact>";
    addSingleMsg(msg_data, msg_content);
  } else if (type === 'AI_DESIGN_PREVIEW_APP') {
    // 生成预览应用
    chat({msgPayload: data, scene: scene, extParams: { nextAction: 'design2app' } });
  }
}
  
  
// 按钮行为处理
export const handleBtnAction = (
  userInfo, 
  func, 
  scene, 
  contextId, 
  urlParams, 
  appCodeRef, 
  requestIdRef, 
  actionCodeRef, 
  initData, 
  addSingleMsg, 
  setUseTool, 
  chat, 
  beforePageSchema, 
  setDisableInputBox, 
  schemaRef, 
  setShowInputBox,  
  saveConversationForm, 
  props,
  lastRequirement, 
  msyPayloadRef,
  fileUrlRef,
  data,
  reasonThinkEnabled = false
) => {
  const { msg, sendmsg, categroy, env, nextAction, immediatelyToProd, content } = data;
  const { ucId } = userInfo;
  
  if (sendmsg) {
    // 模拟用户发送消息
    const msg_data = { 
      id: randomId(), 
      type: 120, 
      direction: 1, 
      query_content: msg,
      scene_id: scene, 
      sessionId: contextId,
      cur_func: func,
      firstBizScene: urlParams?.firstScene,
      secondBizScene: urlParams?.secondScene
    }
    addSingleMsg(msg_data, msg);
  }
  
  const requirement = lastRequirement ? lastRequirement : content;

  if ("没问题" === msg || "前端选型评估" === msg) {
    // 去设计页面（开发者中心）
    setUseTool(true);
    invokeToolForGenerateForm(scene, ucId, contextId, appCodeRef, beforePageSchema, setDisableInputBox, schemaRef, addSingleMsg, setShowInputBox, setUseTool, saveConversationForm, props, fileUrlRef, requirement, reasonThinkEnabled, scene, func);
  } else if ("去设计" === msg) {
    if ('generatePreviewApp' === nextAction) {
      // AI开始执行
      pubEvent({ "type": 'aiDesgining'});
    }
    // 执行下一个行为
    chat({msgPayload: requirement, scene: scene, extParams: { nextAction, picUrl: fileUrlRef.current, }});
  } else if ("应用上线" === msg) {
    publishApp(userInfo, contextId, scene, addSingleMsg, setUseTool, appCodeRef, requestIdRef, initData, 'prod');
  } else if ("申请数据明细" === msg) {
    applyBigDataTable(userInfo, scene, contextId, appCodeRef, requestIdRef, addSingleMsg, setUseTool);
  } else if ("发布重试" === msg) {
    publishApp(userInfo, contextId, scene, addSingleMsg, setUseTool, appCodeRef, requestIdRef, initData, env, immediatelyToProd);
  } else if ("aiRegenerate" === nextAction) {
    if (!msyPayloadRef.current) {
      Toast.info("请输入您的需求描述！！！");
      return;
    }
    // 重新生成
    chat({msgPayload: msyPayloadRef.current, scene: scene, extParams: { picUrl: fileUrlRef.current, reasonThinkEnabled } });
  } 

}
  
// 模拟延迟函数
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 发布应用
const publishApp = async (userInfo, contextId, scene, addSingleMsg, setUseTool, appCodeRef, requestIdRef, initData, env, immediatelyToProd=false) => {
  setUseTool(true);
  doPublishApp(userInfo, appCodeRef.current, requestIdRef.current, env, initData?.roleNames, contextId);
  getCommonAigcResult(requestIdRef.current, scene, appCodeRef.current, addSingleMsg, setUseTool, userInfo, 'PUBLISH_RESULT', data => {
    setUseTool(false);
    // 发布完成，发送事件
    pubEvent({ "type": "appPubDone" });
    console.log("publishApp result: ", data);
    if (data.success && data.visitUrl) {
      // 发布成功
      const appUrl = data.visitUrl;
      if (env === 'test') {
        // 成功发布到测试环境
        if (immediatelyToProd) {
          // 直接发布到线上
          publishApp(userInfo, contextId, scene, addSingleMsg, setUseTool, appCodeRef, requestIdRef, initData, 'prod');
        } else {
          const msg_data = { id: randomId(), type: -225, direction: 2, query_content: '', scene_id: scene, sessionId: contextId, done: true };
          let msg_content = `已为您将应用发布至测试环境，您可以用您的系统号和下方测试密码访问以下链接进行测试，测试完成后，您可以点击"上线"按钮将应用发布上线：<br>[访问测试地址](${appUrl}), 用户名:系统号 密码:H0meL1nk`
          if (scene.includes('ai_home')) {
            msg_content = `已为您将应用发布至测试环境，您可以用您的系统号和下方测试密码访问以下链接进行测试：[访问测试地址](${appUrl})， 用户名:系统号 密码:H0meL1nk`;
          } else {
            msg_data['next_action'] = 'pubProd';
          }
          addSingleMsg(msg_data, msg_content);
        }
      } else if (env === 'prod') {
        // 成功发布到线上
        const msg_data = { id: randomId(), type: 120, direction: 2, query_content: '', scene_id: scene, sessionId: contextId  };
        const msg_content = `<div class="flex-col p-4 ">
                              <p class="-mt-12 -mb-8">已为您将应用发布至线上环境，你可以用您的系统号访问以下链接。</p>
                              <p class="flex justify-center text-2xs">
                                <a href="${appUrl}" target="_blank">访问线上地址</a>
                              </p>
                              <p class="-mt-4 -mb-24">如果您希望将线上系统的数据形成大数据表，请您与平台的管理员取得联系（联系方式参考帮助文档）。</p>
                             </div> 
                            `;
        addSingleMsg(msg_data, msg_content);
        // 发布事件
        pubEvent({ "type": 'appPublishSuccess'});
      }
    } else {
      // 发布失败，展示重试按钮
      const msg_data = { id: randomId(), type: -225, direction: 2, query_content: '', scene_id: scene, sessionId: contextId, done: true, next_action: 'retryPubApp', env, immediatelyToProd };
      addSingleMsg(msg_data, "抱歉发布出现错误，请您点击重试！");
      pubEvent({ "type": 'appPublishFailed'});
    }
  }, err => {
    console.log("发布失败，", err);
    pubEvent({ "type": 'appPublishFailed'});
  });
}

// 执行发布应用
const doPublishApp = (userInfo, appCode, requestId, env, roleNames, contextId) => {
  const url = `${gatewayHost}/web/fe/sinanai/workbenchPublishServiceImpl/publish`
  axios.post(
    url,
    {
      userName: getUserDisplayName(userInfo),
      appCode,
      requestId,
      pushEnv: env,
      roleNames,
      contextId
    },
    {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true
    })
    .then(res => {
      console.log(res);
    })
    .catch(err => {
      console.log(err);
    });
}

// 执行申请数据明细
const doApplyBigDataTable = (appCode) => {
  const url = `${gatewayHost}/web/fe/sinanai/workbenchPublishServiceImpl/applyDataDetail`
  axios.post(
    url,
    {
      appCode,
    },
    {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true
    })
    .catch(err => {
      console.log(err)
    });
}

// 申请大数据表明细
const applyBigDataTable = async (userInfo, scene, contextId, appCodeRef, requestIdRef, addSingleMsg, setUseTool) => {
  setUseTool(true);
  doApplyBigDataTable(appCodeRef.current);
  await delay(1000);
  const msg_data = { id: randomId(), type: -225, direction: 2, query_content: '', scene_id: scene, sessionId: contextId, done: true };
  const msg_content = "已为您申请数据明细表，请您稍后在邮箱中查收，感谢您对小洛的支持。"
  addSingleMsg(msg_data, msg_content);

  setUseTool(false);
}

// 发布事件
const pubEvent = (event) => {
  window.parent.postMessage(event, "*");
}