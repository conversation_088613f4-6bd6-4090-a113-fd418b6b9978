import { genUrlParams } from "@/common/utils/url";
import { getAgentConfigByAgentId, getAiWorkbenchConfig } from "./agent-api-utils";

// 场景对应的功能
const scene_functions = {
    // AI 生成/推荐groovy
    "ai_groovy": [
        {
            key: 'generate_groovy',
            value: '生成Groovy代码',
            functionShow: true
        },
        {
            key: 'retrieval_groovy_list',
            value: '推荐相关Groovy',
            functionShow: true
        }
    ],
    // 表单设计器中局部调整
    "ai_form": [
        {
            key: 'generate_form',
            value: '智能调整表单',
            functionShow: true
        },
        {
            key: 'kemis_qa',
            value: 'Kemis问题答疑',
            functionShow: true
        }
    ]
}

// 获取智能体配置
export const getAgentConfig = async (scene: string) => {
    const agentConfig = {
        agentAvatar: '',
        functions: scene_functions,
        bubbles: {},
        history: false
    };
    
    const urlParams = genUrlParams() || {};
    const { agentId, appCode } = urlParams;
    
    // 根据agentId获取智能体配置
    let config;
    if (scene === 'ai_agent') {
        // 通用智能体场景（比如：司南CRM智能体）
        if (!agentId) {
            agentConfig['history'] = true;
            return agentConfig;
        }
        config = await getAgentConfigByAgentId(agentId);
    } else if (scene.includes('ai_workbench') || scene.includes('ai_home')) {
        config = await getAiWorkbenchConfig(scene);
    } else if (scene === 'ai_agent_app') {
        // 需求即应用-贝创 场景
        config = await getAiWorkbenchConfig(scene);
        config['history'] = true;
    } else {
        return agentConfig;
    }
    
    // 合并 config 的 functions 到 scene_functions
    const mergedFunctions = { ...scene_functions };
    const configFunctions = config['functions'].map(func => ({
        key: func.functionsCode,
        value: func.name,
        inputPlaceholder: func.inputPlaceholder,
        functionDesc: func.functionDesc,
        functionShow: func.functionShow
    }));
    mergedFunctions[scene] = (mergedFunctions[scene] || []).concat(configFunctions);

    // 更新 agentConfig
    agentConfig.agentAvatar = config['avatar'];
    agentConfig.functions = mergedFunctions;
    agentConfig.history = config['history'];

    // 将 bubbles 转换为 map
    const bubblesMap = {};
    config['functions'].forEach(func => {
        if (func.bubbles && func.bubbles.length > 0) {
            bubblesMap[func.functionsCode] = func.bubbles.sort((a, b) => a.bubbleOrder - b.bubbleOrder);
        }
    });
    agentConfig.bubbles = bubblesMap;
    return agentConfig;
}

// 场景扩展配置
export const sceneExtConfig = {
    // “设计即实现
    "ai_workbench": {
        // 首屏欢迎语
        "welcomeTips": '~O(∩_∩)O~<br><br>在这里，你可以完成：<br>1) 自然语言描述生成表单<br>2) 上传图片生成表单<br>3) 发送设计文档wiki地址(<a href="https://wiki.lianjia.com/pages/viewpage.action?pageId=1457689165" target="_blank">设计模板</a>)，生成应用相关模块'
    }
}