import { Toast } from 'antd-mobile';
import axios from "axios";
import ImApi from "@/common/service/im";
import { track } from "@/common/tracks/actions/im";
import { randomId } from "@/common/utils"
import { aigcHost,gatewayHost,chatHost } from "@/common/utils/urlFig"


// 解析json schema
export const parseAndGetJsonSchema = (data) => {

    let jsonSchema = ""
    if (typeof data === 'string') {
      let jsonStr = data
      if (!isJson(jsonStr)) {
        const si = jsonStr.indexOf("{");
        const ei = jsonStr.lastIndexOf("}");
        if (si != -1 && ei != -1) {
          jsonStr = jsonStr.substring(si, ei+1);
        } 
      }
      jsonSchema = JSON.stringify(JSON.parse(jsonStr));
    } else {
      jsonSchema = JSON.stringify(data);
    }
    return jsonSchema;
}

const isJson = (str) => {
    let f = false;
    try {
      JSON.parse(str)
      f = true;
    } catch(e) {
      // ignore
    }
    return f;
}

 // 解析知识项
 export const parseKnowledgeResult = (msg_data, tool_result, ext_data) => {
    const toolResults = [];
    const data = JSON.parse(tool_result);
    Object.keys(data).forEach(key => {
      if (ext_data) {
        data[key] = { ...data[key], ...ext_data };
      }
      toolResults.push({"toolName": key, "data": data[key]});
      const { items } = data[key];
      if (items) {
        const itemIds = items.map(item => item.id).join(',');
        msg_data['knowledgeIds'] = itemIds;
      }
    });
    msg_data['extRegionInfos'] = toolResults;
}

// 保存会话消息
 export const doSaveConversationMsg = (msg: any, baseInfo, userInfo, contextId, scene) => {
    const { bizChannel, spm } = baseInfo;
    const { ucId } = userInfo;
    const { data, direction, query_content, type, timestamp } = msg;

    const reply_message = {
      convId: "ai"+contextId,
      channel: bizChannel,
      spm,
      fromUcid: ucId,
      msgType: type,
      direction: direction,
      msgId: randomId(),
      msgPayload: data,
      sendTime: timestamp,
      source: scene
    }

    if (data) {
      ImApi.saveConversationMessage(reply_message);
    } else {
      // 用户query
      const user_message = {
        ...reply_message,
        msgType: 120,
        direction: 1,
        msgId: randomId(),
        msgPayload: query_content,
      };

      ImApi.saveConversationMessage(user_message);
    }
  }

  // 保存会话表单数据
 export const doSaveConversationForm = (ucid, scene_id, contextId, spm, parentInitedData) => {
    const url = `${gatewayHost}/web/v1/ls/rBrdowJHJlzTVnTgCbMJXCKKsvxumVYe/updateSessionLogic`;
    const conversationLink = `${chatHost}/conversation/share?spm=${spm}&convId=${contextId}&customerId=${ucid}`;
    axios.post(
      url,
      {
        "businessKey": "AI_Conversation",
        "param": {
          "conversationTitle": "",
          "conversationId": contextId,
          "conversationLink": conversationLink,
          "conversationTime": new Date().getTime(),
          "ucid": ucid + "", 
          "scene": scene_id,
          "feedback": parentInitedData?.parentUrl
        },
        "source": "0"
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
        withCredentials: true
      })
      .then(res => {
        console.log(res);
      })
      .catch(err => {
        Toast.info("执行失败，请稍后重试！");
        console.log(err)
      });
  }

// AI对话响应结果埋点
export const onDigData = (user_id: string, scene_id: string, query: string, result: string, appCode: string, user_name: string) => {
    if (result.length > 700) {
      result = result.length > 700 ? result.substring(0, 700) : result;
    }
    if (query.length > 700) {
      query = query.length > 700 ? query.substring(0, 700) : query;
    }
    track(63969, {
      request_id: user_id,
      query_content: query,
      scene_id: scene_id,
      result: result,
      biz_source: appCode,
      user_name: user_name
    }, {
      event: 'Custom_Track',
      uicode: 'ai_pm_copilot'
    });
  }

  // AI Groovy响应结果埋点
  export const onAIGroovyDigData = (user_id: string, function_name: string,query: string, result: string, user_name: string) => {
    if (result.length > 700) {
      result = result.length > 700 ? result.substring(0, 700) : result;
    }
    if (query.length > 700) {
      query = query.length > 700 ? query.substring(0, 700) : query;
    }
    track(67193, {
      request_id: user_id,
      query_content: query,
      function_name: function_name,
      result: result,
      user_name: user_name
    }, {
      event: 'Custom_Track',
      uicode: 'ai_pm_copilot'
    });
  }

// 洛书AI 通用埋点
export const onLuoshuAIDigData = (user_id: string, function_name: string,query: string, result: string, user_name: string, scene: string) => {
  if (result.length > 700) {
    result = result.length > 700 ? result.substring(0, 700) : result;
  }
  if (query.length > 700) {
    query = query.length > 700 ? query.substring(0, 700) : query;
  }
  track(68474, {
    request_id: user_id,
    query_content: query,
    function_name: function_name,
    result: result,
    user_name: user_name,
    scene: scene
  }, {
    event: 'Custom_Track',
    uicode: 'ai_pm_copilot'
  });
}

// AI对话响应结果埋点, 触发时机：聊天框输入内容并发送/点击气泡
export const onDigChatAgentData = (user_id: string, scene_id: string, function_name:string, query: string, result: string, ext_data: object) => {
  if (result.length > 700) {
    result = result.length > 700 ? result.substring(0, 700) : result;
  }
  if (query.length > 700) {
    query = query.length > 700 ? query.substring(0, 700) : query;
  }
  track(69385, {
    scene_id: scene_id,
    operator_ucid: user_id,
    function_name,
    str1: query,
    str2: result,
    ctime: Date.now(),
    ... ext_data
  }, {
    event: 'Custom_Track',
    uicode: 'crm_aiSession'
  });
}

// AI工具条埋点
export const onDigAIToolData = (user_id: string, scene_id: string, function_name:string, ext_data: object) => {
  track(69391, {
    scene_id: scene_id,
    operator_ucid: user_id,
    function_name,
    ctime: Date.now(),
    ... ext_data
  }, {
    event: 'Custom_Track',
    uicode: 'crm_aiSession'
  });
}

  // 加载扩展参数
 export const reloadExtParams = (callback, contextId, extParams) => {
    const url = `${gatewayHost}/web/fe/sinanai/aigcServiceImpl/reloadExtParams`
    axios.post(
      url,
      {
        "extParamsDTO": {
          "contextId": contextId,
          "extParams": extParams
       }
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
        withCredentials: true
      })
      .then(res => {
        console.log(res);
        callback();
      })
      .catch(err => {
        console.log(err)
      });
  }


// 滚到底部
export const scrollToBottom = () => {
    var targetElement = document.getElementById('bottomPlaceholder');
    if (!targetElement) return;
    // 滚动到指定元素的位置
    targetElement.scrollIntoView({ behavior: 'smooth' });
    console.log("scrollToBottom")
};


 // 保存会话反馈数据
 export const doSaveConversationFeedbackForm = (user_name, contextId, action, toolResult, feedback, resultId, successCallback, errCallback) => {
  const url = `${gatewayHost}/web/v1/ls/rBrdowJHJlzTVnTgCbMJXCKKsvxumVYe/updateSessionFeedback`;
  axios.post(
    url,
    {
      "businessKey": "AI_Conversation_Feedback",
      "param": {
        "conversationId": contextId,
        "userName": user_name,
        "action": action,
        "toolResult": toolResult,
        "feedback": feedback, 
        "resultId": resultId
      },
      "source": "0"
    },
    {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true
    })
    .then(res => {
      console.log(res);
      successCallback();
    })
    .catch(err => {
      Toast.info("执行失败，请稍后重试！");
      console.log(err);
      errCallback(err);
    });
}

// 解析Artifact结果（工具执行结果）
export const parseArtifactResult = (msg_data) => {
  let match;
  let artifactAttributes = {};

  // Extract type, version and other attributes from the opening Artifact tag
  const attributesRegex = /<Artifact\s+(.*?)>/;
  const attributesMatch = msg_data.match(attributesRegex);
  
  if (attributesMatch && attributesMatch[1]) {
    // Extract all attributes as key-value pairs
    const attributesStr = attributesMatch[1];
    const attrMatches = attributesStr.matchAll(/(\w+)="([^"]+)"/g);
    
    for (const attrMatch of attrMatches) {
      artifactAttributes[attrMatch[1]] = attrMatch[2];
    }
  }

  // Then continue with existing logic for content extraction
  if (msg_data.includes('extInfo')) {
    const regex1 = /type="([^"]+)"(?:[^>]*extInfo="(\[.*?\])")?[^>]*>([^<]+)<\/Artifact>/;
    const regex2 = /type="([^"]+)"[^>]*extInfo="(\[.*?\])"[^>]*>([\s\S]*?)<\/Artifact>/;
    match = msg_data.match(regex1);
    if (!match) {
      match = msg_data.match(regex2);
    }
  } 

  if (!match) {
    const regex = /<Artifact type="([^"]+)">\n([\s\S]+?)\n<\/Artifact>/;
    match = msg_data.match(regex);
  }

  // If we still don't have a match, try a more general pattern
  if (!match) {
    const contentRegex = /<Artifact.*?>([\s\S]*?)<\/Artifact>/;
    const contentMatch = msg_data.match(contentRegex);
    
    if (contentMatch) {
      return {
        ...artifactAttributes,
        artifactType: artifactAttributes.type,
        artifactContent: contentMatch[1].trim()
      };
    }
  }

  if (match) {
    if (match.length == 3) {
      const artifactType = match[1]; 
      const artifactContent = match[2]; 
      return { ...artifactAttributes, artifactType, artifactContent };
    } else if (match.length == 4) {
      const artifactType = match[1]; 
      const extInfo = match[2]; 
      const artifactContent = match[3]; 
      return { ...artifactAttributes, artifactType, extInfo, artifactContent };
    }
  }
  
  return { ...artifactAttributes, artifactType: artifactAttributes.type || "kemis" };
}

 // 调用反馈接口
export const doFeedback = (name, sessionId, id, action, artifactContent, feedback, modalShow, setModalShow) => {
  if ("点踩" === action && !modalShow) {
      setModalShow(true)
  } else {
      doSaveConversationFeedbackForm(name, sessionId, action, artifactContent, feedback, id, () => {
          Toast.success("反馈成功，非常感谢", 1);
      }, err => {
          Toast.fail("反馈失败: " + err, 1);
      });
  }
}
// 提交反馈
export const submitFeedback = (name, sessionId, id, action, artifactContent, feedback, modalShow, setModalShow) => {
  doFeedback(name, sessionId, id, action, artifactContent, feedback, modalShow, setModalShow);
  setModalShow(false);
}

// 检测并调用工具
export const checkAndInvokeTool = ( ucid, 
                                    query,
                                    tool_name, 
                                    parameters, 
                                    curScene, 
                                    contextId, 
                                    appCodeRef, 
                                    beforePageSchemaRef, 
                                    setDisableInputBox, 
                                    schemaRef, 
                                    addSingleMsg, 
                                    setShowInputBox, 
                                    setUseTool, 
                                    saveConversationForm, 
                                    props, 
                                  ) => {
  console.log(`${tool_name}:${query}`);
  window.parent.postMessage({ "type": "invokingTool" }, "*");
  // 调用工具
  invokeTool(ucid, tool_name, parameters);
  // 获取结果
  getAigcResult(ucid, ucid, query, curScene, contextId, appCodeRef, beforePageSchemaRef, setDisableInputBox, schemaRef, addSingleMsg, setShowInputBox, setUseTool, saveConversationForm, props);
}

// 调用工具
export const invokeTool = (ucid, tool_name, parameters) => {
  const url = `${aigcHost}/tool/invoke`
  axios.post(
    url,
    {
      "userId": ucid,
      "toolName": tool_name,
      "parameters": parameters,
    },
    {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true
    })
    .then(res => {
      console.log(res);
    })
    .catch(err => {
      console.log(err)
    });
}


 // 获取AI生成结果（轮询获取）
 export const getAigcResult = (ucid, 
                               requestId, 
                               query, 
                               curScene, 
                               contextId, 
                               appCodeRef, 
                               beforePageSchemaRef,
                               setDisableInputBox,
                               schemaRef,
                               addSingleMsg,
                               setShowInputBox,
                               setUseTool,
                               saveConversationForm,
                               props,
                              ) => {
  setDisableInputBox(true);
  let lastChunkMsg = ""
  const startTime = performance.now();
  
  const resultUrl = `${aigcHost}/aigc/getAigcResult?requestId=${requestId}`
  const timer = setInterval(() => {
    axios.get(resultUrl, {withCredentials: true})
    .then(rs => {
      if (rs) {
        const result = rs.data.data;
        if (result.eventType && (result.eventType === 'PROGRESS' || result.eventType === 'TEMPLATE_RESULT' || result.eventType === 'TASK_ERROR_RETRY')) {
          // ignore
        } else if (result.eventType === 'TASK_ERROR') {
          clearInterval(timer);
          setDisableInputBox(false);
          setUseTool(false);

          const msg_data = { id: randomId(), type: 120, direction: 2, query_content: query, scene_id: curScene };
          addSingleMsg(msg_data, `<div class="inline-flex"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="#e11d48" d="M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2M12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8s8 3.58 8 8s-3.58 8-8 8"/></svg><span class="color-red">${result.errmsg}</span></div>`);
          ack(requestId);

          onDigData(ucid, curScene, query, "TASK_ERROR: " + result.errmsg, appCodeRef.current, props.userInfo?.name);
        } else if (result.errno == 0 && result.data && result.eventType && result.eventType === 'CHUNK_MESSAGE') {
          if (lastChunkMsg != result.data) {
            lastChunkMsg = result.data;
            // 部分结果
            console.log(`累计耗时: ${performance.now() - startTime} 毫秒`);
            console.log("AI结果：", result)
            const msg_data = { id: randomId(), type: 120, direction: 2, query_content: query, scene_id: curScene };
            addSingleMsg(msg_data, `<div class="inline-flex"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="#65a30d" d="m9.55 15.15l8.475-8.475q.3-.3.713-.3t.712.3q.3.3.3.713t-.3.712l-9.2 9.2q-.3.3-.7.3t-.7-.3L4.55 13q-.3-.3-.288-.712t.313-.713q.3-.3.713-.3t.712.3z"/></svg><span>${result.data}</span></div>`);
          }
        } else if (result.errno != -1) {
          const elapsedMilliseconds = performance.now() - startTime;
          console.log(`累计耗时: ${elapsedMilliseconds} 毫秒`);
          console.log("AI结果：", result)
          if (result.errno == 0 && result.data && result.eventType && result.eventType === 'TOTAL_MESSAGE') {
              // 生成完整结果
              setUseTool(false);
              parseResultAndGetSchema(ucid, query, curScene, result.data, contextId, appCodeRef, beforePageSchemaRef, setDisableInputBox, schemaRef, addSingleMsg, saveConversationForm, props);
          } else {
            Toast.info(result.errmsg);
          }
          // 清除定时器
          clearInterval(timer);
          // 结果确认
          ack(requestId);
          ack(requestId);
        }
      }
    });
  }, 2000);
}

// 获取AI生成结果（轮询获取）
 export const getCommonAigcResult = (
                                        requestId, 
                                        curScene, 
                                        appCode, 
                                        addSingleMsg,
                                        setUseTool,
                                        userInfo,
                                        expectEventType,
                                        successCallback?,
                                        errCallback?
                              ) => {
  const startTime = performance.now();
  const { ucId, name } = userInfo;
  // ack(requestId);
  const resultUrl = `${aigcHost}/aigc/getAigcResult?requestId=${requestId}`
  const timer = setInterval(() => {
    axios.get(resultUrl, {withCredentials: true})
    .then(rs => {
      if (rs) {
        const result = rs.data.data;
       
        if (result.eventType === 'TASK_ERROR' || result.eventType === 'AI_EDIT_ERROR') {
          clearInterval(timer);
          setUseTool(false);
          ack(requestId);
          ack(requestId);

          if (result.eventType === 'TASK_ERROR') {
            // 通用的错误消息
            const msg_data = { id: randomId(), type: 120, direction: 2, query_content: '', scene_id: curScene };
            addSingleMsg(msg_data, `<div class="inline-flex"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="#e11d48" d="M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2M12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8s8 3.58 8 8s-3.58 8-8 8"/></svg><span class="color-red">${result.errmsg}</span></div>`);
          }
         
          if (errCallback) {
            errCallback(result);
          }
          // 埋点
          onDigData(ucId, curScene, '', "TASK_ERROR: " + result.errmsg, appCode, name);
        } else if (result.eventType === expectEventType) {
          const elapsedMilliseconds = performance.now() - startTime;
          console.log(`累计耗时: ${elapsedMilliseconds} 毫秒`);
          console.log("AI结果：", result);
          if (successCallback) {
            successCallback(result.data);
          }
          // 清除定时器
          clearInterval(timer);
          // 结果确认
          ack(requestId);
          ack(requestId);
        }
      }
    });
  }, 2000);
}

// 生成页面
export const exePipeline = (ucid, 
                            requestId,
                            curScene, 
                            query, 
                            pipelineName, 
                            contextIdRef, 
                            appCodeRef, 
                            beforePageSchemaRef,
                            setDisableInputBox,
                            schemaRef,
                            addSingleMsg,
                            setShowInputBox,
                            setUseTool,
                            saveConversationForm,
                            props,
                            spm,
                            appCode,
                            formCode,
                            selectedJsonSchema,
                            extParams?
                          ) => {
  setUseTool(true);
  schemaRef.current = "";
  ack(ucid);
  const url = `${gatewayHost}/web/fe/sinanai/aigcServiceImpl/exePipeline`
  axios.post(
    url,
    {
      "request": {
        ...(extParams || {}),
        "ucId": ucid,
        "requestId": requestId,
        "resourceFile": "PAGE",
        "resourceName": "测试",
        "aigcDescription": query,
        "pipelineName": pipelineName,
        "enableTemplate": false,
        "beforeSchema": beforePageSchemaRef.current,
        "appCode": appCodeRef.current || appCode,
        "scenario": curScene,
        formCode,
        "selectedJson": selectedJsonSchema,
    }
    },
    {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true
    })
    .then(res => {
      if (res.status == 200 && res.data && res.data.code == 1) {
        getAigcResult(ucid, requestId, query, curScene, contextIdRef.current, appCodeRef, beforePageSchemaRef, setDisableInputBox, schemaRef, addSingleMsg, setShowInputBox, setUseTool, saveConversationForm, props);
      } else {
        Toast.info("执行失败，请稍后重试！");
        console.log(res);
      }
    })
    .catch(err => {
      Toast.info("执行失败，请稍后重试！");
      console.log(err)
    });
}


// 解析生成结果并获取Schema 
const parseResultAndGetSchema = (ucid, 
                                  query, 
                                  curScene, 
                                  data,
                                  contextId, 
                                  appCodeRef, 
                                  beforePageSchemaRef,
                                  setDisableInputBox,
                                  schemaRef,
                                  addSingleMsg,
                                  saveConversationForm,
                                  props,
                                      ) => {
  setDisableInputBox(false);
  const msg_data = { id: randomId(), type: 120, direction: 2, query_content: query, scene_id: curScene, sessionId: contextId };
  let result = "";
  

  if (curScene === 'ai_form') {
    const jsonSchema = parseAndGetJsonSchema(data);
    const newSchema = "```json\n" + jsonSchema;
    schemaRef.current = newSchema;

    msg_data['type'] = -227;
    msg_data['last_schema'] = beforePageSchemaRef.current;
    // kemis表单
    const msg_content = "<Artifact type=\"kemis\">\n" + jsonSchema + "\n</Artifact>";
    addSingleMsg(msg_data, msg_content);
    result = jsonSchema;
  } else {
    msg_data['type'] = -227;
    // kemis表单
    const jsonSchema = parseAndGetJsonSchema(data);
    schemaRef.current = jsonSchema;
    const msg_content = "<Artifact type=\"kemis\">\n" + jsonSchema + "\n</Artifact>";
    addSingleMsg(msg_data, msg_content);
  }

  onDigData(ucid, curScene, query, result, appCodeRef.current, props.userInfo?.name);
  // 更新会话表单
  saveConversationForm(ucid, curScene);
}

// 结果确认
export const ack = (requestId) => {
  const ackUrl = `${aigcHost}/aigc/ack?requestId=${requestId}`
  axios.get(ackUrl, {withCredentials: true});
}

// 推荐groovy代码
export const generateGroovyHandler = (ucid, curScene, msgPayload, saveConversationMsg, addSingleMsg, getfunction, userInfo, ) => {
  // 保存消息
  const query_data = { id: randomId(), type: 120, direction: 2, query_content: msgPayload, scene_id: curScene }
  saveConversationMsg(query_data);

  // groovy检索接口
  const url = `${gatewayHost}/web/fe/sinanai/groovySugServiceImpl/retrievalGroovy`
  axios.post(
    url,
    {
       "topN": 3,
       "query": msgPayload
    },
    {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true
    })
    .then(res => {
      if (res.status == 200 && res.data && res.data.code == 1) {
        res.data.data = res.data.data.filter(item => item["score"] > 0.3);

        if (res.data.data.length == 0) {
          const msg_data = { id: randomId(), type: 120, direction: 2, query_content: msgPayload, scene_id: curScene};
          addSingleMsg(msg_data, "无相关的Groovy代码！");
          // 埋点
          onAIGroovyDigData(ucid, getfunction(), msgPayload, "无相关的Groovy代码！", userInfo?.name);
        } else {
          let result = "";
          res.data?.data?.forEach(item => {
            result = result + item["resourceId"] + ",";
            item["groovyCode"] = "```groovy \n" + item["groovyCode"] + "\n```";
          });

          const msg_data = { id: randomId(), type: -226, direction: 2, query_content: msgPayload, scene_id: curScene, groovyList: res.data.data };
          addSingleMsg(msg_data, result);
          
          // 埋点
          onAIGroovyDigData(ucid, getfunction(), msgPayload, result, userInfo?.name);
        }
      } else {
        Toast.info("执行失败，请稍后重试！");
        console.log(res);
      }
    })
    .catch(err => {
      Toast.info("执行失败，请稍后重试！");
      console.log(err)
    });
}

// 记录表单最原始的元数据
export const recordOriginSchema = (curScene, contextId, jsonSchema, addSingleMsg) => {
  const msg_data = { id: randomId(), type: -227, direction: 2, query_content: '', scene_id: curScene, sessionId: contextId, originSchema: true };
  const msg_content = "<Artifact type=\"kemis\">\n" + jsonSchema + "\n</Artifact>";
  addSingleMsg(msg_data, msg_content);
}


// sse结束后的后置处理
export const postHandlerForSse = (
  curScene, 
  ucid, 
  msgPayload, 
  mkcontent, 
  sse_url, 
  sse_body, 
  msg_data, 
  props, 
  setMarkdownContent, 
  lastRequirement, 
  sseMsgHandler,
  func,
  urlParams,
  contextId,
  appCodeRef, 
  setUseTool, 
  beforePageSchema, 
  setDisableInputBox, 
  schemaRef, 
  setShowInputBox, 
  saveConversationForm,
  addSingleMsg,
  fileUrlRef
) => {
  if (props.changeStop) {
    // 隐藏停止按钮
    props.changeStop(false);
  }

    // 判断是否调用工具
  if (mkcontent.includes('use_tool:')) {
    setMarkdownContent('');
    
    // 解析工具
    const regex = /use_tool:(.+?)@(.+)/;
    const match = mkcontent.match(regex);

    if (match) {
      const toolId = match[1];
      const parameters = match[2];
      // 请求Chat接口，并传入工具和参数
      sse_body['extParams'] = {
        action: 'triggerTool',
        code: toolId,
        parameters
      }
      sseMsgHandler(ucid, curScene, sse_url, mkcontent, sse_body);
    }
  } else {
    // 是否点击气泡
    let is_bubble = false;
    if (sse_body['extParams'] && sse_body['extParams']['action'] && (sse_body['extParams']['action'] === 'triggerTool' || sse_body['extParams']['action'] === 'triggerWorkflow')) {
      is_bubble = true;
    }
    // 引用的知识id
    let ref_knowledges = msg_data['knowledgeIds'] || "";
    if (ref_knowledges.length > 700) {
      ref_knowledges = ref_knowledges.substring(0, 700);
    }
    // 发送行为的埋点
    onDigChatAgentData(ucid, curScene, func, msgPayload, mkcontent, {
      biz_scene1: urlParams?.firstScene,
      biz_scene2: urlParams?.secondScene,
      contextId: contextId,
      is_bubble,
      reference_id: ref_knowledges
    });

    setDisableInputBox(false);
  }

  if (curScene === 'ai_groovy' || curScene === 'ai_form') {
    // 埋点
    if (curScene === 'ai_groovy') {
      onAIGroovyDigData(ucid, func, msgPayload, mkcontent, props.userInfo?.name);
    }
    if (curScene === 'ai_form') {
      onLuoshuAIDigData(ucid, func, msgPayload, mkcontent, props.userInfo?.name, curScene);
    }
  }

    // 更新会话表单
    saveConversationForm(ucid, curScene);
}

// 调用工具 - 生成表单
export const invokeToolForGenerateForm = (curScene, ucId, contextId, appCodeRef, beforePageSchema, setDisableInputBox, schemaRef, addSingleMsg, setShowInputBox, setUseTool, saveConversationForm, props, fileUrlRef, lastRequirement, reasonThinkEnabled = false, scene, func) => {
  const query = getQueryFromReq(lastRequirement);
  // 调用工具(生成表单的工具)
  checkAndInvokeTool(ucId, query, "generate_form_meta_data", { "userId": ucId, "formQuery": query, fileUrl: fileUrlRef.current, reasonThinkEnabled, scenario: scene, functionCode: func }, curScene, contextId, appCodeRef, beforePageSchema, setDisableInputBox, schemaRef, addSingleMsg, setShowInputBox, setUseTool, saveConversationForm, props);
}

// 从需求中获取输入的query
export const getQueryFromReq = (requirment) => {
  let query = requirment;
  if (requirment.split('---').length == 3) {
    query = requirment.split('---')[1];
  }
  return query;
}