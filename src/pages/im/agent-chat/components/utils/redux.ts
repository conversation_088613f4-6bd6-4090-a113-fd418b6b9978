import {
    CHANGE_SERVICE_STATUS,
    REVERT_MSG,
    ADD_NEW_MSGS,
    ADD_BUBBLE,
    REMOVE_BUBBLE,
    CHANGE_INQUEUE_STATUS,
    UPDATE_MSG_MAP
  } from "@/common/store";
  
  export const mapStateToProps = (state: any) => ({
    msgList: state.im.msgList,
    userInfo: state.user.userInfo
  });
  
  export const mapDispatchToProps = (dispatch: any) => ({
    inServiceChange: (inService: boolean) => dispatch({ type: CHANGE_SERVICE_STATUS, payload: inService }),
    inQueueChange: (inQueue: boolean) => dispatch({ type: CHANGE_INQUEUE_STATUS, payload: inQueue }),
    revertMsg: (msg: any[]) => dispatch({ type: REVERT_MSG, payload: msg }),
    updateMsgMap: (list: any[]) => dispatch({type: UPDATE_MSG_MAP, payload: list}),
    addNewMsgs: (msgList: any[]) => dispatch({ type: ADD_NEW_MSGS, payload: msgList }),
    addBubble: (bubble: any) => dispatch({ type: ADD_BUBBLE, payload: bubble }),
    removeBubble: (bubble: any) => dispatch({ type: REMOVE_BUBBLE, payload: bubble })
  });