.pilot-container {
  .msg-wrapper {
    margin: 16px 0px 0 0px;
  }
  .client-msg {
    .avatar {
      display: none;
    }
    .msg-card-base {
      background: #eff6ff;
      padding: 10px 12px;
      border-radius: 6px;
    }
  }

  .service-msg {
    .avatar {
      display: none;
    }
    .msg-card-base {
      background: #fafaff;
      max-width: 100%;
      padding: 0;
      align-items: inherit;
      border-radius: 6px;

      .markdown {
        h1,h2,h3,h4,h5,h6 {
          color: #222222;
        }
        ol li strong {
          color: #222222;
        }
        ul li strong {
          color: #222222;
        }
        ul li em {
          color: #222222;
        }
      }
    }
  }

  .question-list-wrapper .question-item {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 4px;
    width: 100%;
    cursor: pointer;
    word-break: break-all;
    color: cornflowerblue;
    margin-left: 12px;
  }

  .generate-result-ack {
    padding: 4px 8px;
    font-size: 17px;
    line-height: 16px;
    color: #222222;
    display: inline-flex;
    place-items: center;
  }

  .knowledge-detail-container {
    max-width: 950px;
    width: 950px 
  }

  .use-tool-container {
    // border: solid 1px;
    padding: 13px;
    width: 167px;
    border-radius: 6px;
    border-color: lightgray;
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-top: 5px;

    .use-tool-tip {
      margin-left: 14px;
      color: darkblue;
    }

    
  }

  .like-btn {
    cursor: pointer;
    font-size: 15px;
  }
  .dislike-btn {
    margin-left: 268px;
    cursor: pointer;
    font-size: 15px;
  }

  .agent-chat-body {
    .client-msg {
      .avatar {
        display: block;
      }
    }
    .service-msg {
      .avatar {
        display: block;
      }
    }
  }

}
