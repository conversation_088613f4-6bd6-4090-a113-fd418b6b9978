import React, { useEffect, useState, useContext, useRef } from "react";
import { connect } from "react-redux";
import { randomId, replaceProtocol } from "@/common/utils"
import { ImContext } from "../../../context";
import FetchEventSource from "@/common/utils/FetchEventSource"
import MsgItem from "../../../components/msg-item"
import ChatPMPilot from "../msg-item/components/chat-pm-pilot";
import { mapStateToProps, mapDispatchToProps } from "../utils/redux";
import { 
  doSaveConversationMsg, 
  doSaveConversationForm, 
  reloadExtParams,
  scrollToBottom,
  ack,
  exePipeline,
  generateGroovyHandler,
  postHandlerForSse,
} from "../utils/ai-chat-utils";
import {
  parsePriorityToolResult,
  listenEventAndHandleMsg,
  handleBtnAction,
  getUserDisplayName,
  getChatUrl,
  removeTagsAndContent,
  extractIntentAndResultRobust,
  parseAigcChunkResult,
  dealAigcResult,
  brdWelcomeMsg,
  parseAiResultEvent
} from "../utils/ai-agent-utils";


import "./index.less";



function MsgSection(props: any): JSX.Element {
  const { imConfig, urlParams } = useContext(ImContext);
  const { sseChatUrl, baseInfo } = imConfig;
  const [markdownContent, setMarkdownContent] = useState("");
  const [reasoningContent, setReasoningContent] = useState("");
  const [hasContent, setHasContent] = useState(false);
  const [evtSourceClosed, setEvtSourceClosed] = useState(false);
  const [extRegionInfos, setExtRegionInfos] = useState([]);
  const schemaRef = useRef("");
  const beforePageSchema = useRef("");
  const selectedJsonSchema = useRef("");
  const sceneRef = useRef(props.curScene);
  const contextIdRef = useRef(props.contextId);
  const { setShowInputBox,setDisableInputBox,getfunction } = props;
  const appCodeRef = useRef("");
  const extParamsRef = useRef({});
  const luoshuExtParamsRef = useRef({});
  const sseClientRef = useRef(null);
  const msyPayloadRef = useRef("");
  const [useTool, setUseTool] = useState(false);
  const lastRequirementRef = useRef("");
  const initDataRef = useRef({});
  const enableInputOutputParamsRef = useRef(true);
  const formCodeRef = useRef("");
  const { userInfo:{ ucId }, bubbleCode } = props;
  const actionCodeRef = useRef(bubbleCode);
  const requestIdRef = useRef("");
  const collapseMarkdownRef = useRef(false);
  const currentAiRecordIdRef = useRef("");
  const userInfoRef = useRef(props.userInfo);
  const fileUrlRef = useRef("");
  const reasonThinkEnabledRef = useRef(false);

  // 保存会话消息
  const saveConversationMsg = (msg: any) => {
    doSaveConversationMsg(msg, baseInfo, userInfoRef.current, contextIdRef.current, sceneRef.current);
  }

  // 保存会话表单数据
  const saveConversationForm = (ucid, scene_id) => {
    doSaveConversationForm(ucid, scene_id, contextIdRef.current, urlParams.spm, initDataRef.current);
  }

  const addNewMsgs = (addList: any[]) => {
    props.addNewMsgs(addList);
    props.updateMsgMap(addList);

    scrollToBottom();
  };

  const addSingleMsg = (msgData: any, content: string) => {
    const { type, extRegionInfos } = msgData;

    if (type === -225 && extRegionInfos && !content.includes("<Artifact")) {
      // 消息内容扩展
      const extInfo = JSON.stringify(extRegionInfos);
      content = `<Artifact type="extRegionInfos" extInfo="${extInfo}" >\n${content}\n</Artifact>`;
    }

    const msg = {
      ... msgData,
      data: content,
      action_code: actionCodeRef.current,
      timestamp: Date.now()
    };
    // 展示消息
    addNewMsgs([msg]);
    // 保存消息
    saveConversationMsg(msg);
    scrollToBottom();
    // 发送信息事件
    ImBus.emit("LAST_SEND_MSG", msg);
  };
 

  /**
   *  SSE监听器
   */
  const sseListener = () => {
    if (sseChatUrl) {
        // 兜底，清除之前的结果
        ack(ucId);

        // 监听用户发送的信息
        ImBus.on("NEW_SSE_MSG_SEND", (data: any) => {
          inputHandle(sseChatUrl, data);
        });
    }
  }

  // chat
  const chat = (data) => {
    inputHandle(sseChatUrl, data);
  }

  // 输入处理
  const inputHandle = (sseChatUrl, data) => {
    sseChatUrl = 'http://im-saas-dev.ke.com:8080/sse/chat?fromUcid=1000000026015657&spm=c5f30a89299020b936db09ae2d2ace7';
    let { msgPayload, fileUrl, extParams, reasonThinkEnabled, scene, sseUrlReplace } = data;
    if (fileUrl) {
      // 富文本
      let content = "<img src='" + fileUrl + "' width='80' height='80' /><br>" + msgPayload;
      data['type'] = 120;
      addSingleMsg(data, content);
      fileUrlRef.current = fileUrl;
    } else {
      if (!data?.type) {
        data = { ...data , type: -225, id: randomId()};
      }
      addSingleMsg(data, msgPayload);
    }

    // 如果extParams为空，则使用extParamsRef的值。如果不为空，则更新extParamsRef的值
    if (extParams) {
      extParamsRef.current = extParams;
    } else {
      extParams = extParamsRef.current;
    }

    if (!fileUrl) {
      fileUrl = extParams?.picUrl || fileUrlRef.current;
    }

    reasonThinkEnabledRef.current = reasonThinkEnabled || extParams?.reasonThinkEnabled;
    const func = getfunction();
    const curScene = scene || sceneRef.current;
    const sseUrl = getChatUrl(sseChatUrl, curScene, func, sseUrlReplace);
    const sse_body = {
      userId: ucId,
      displayUserName: getUserDisplayName(userInfoRef.current),
      scenario: curScene,
      query: msgPayload,
      contextId: contextIdRef.current,
      appCode: appCodeRef.current,
      picUrl: fileUrl,
      extParams: {
        actionCode: actionCodeRef.current,
        ...(extParams || {}),
        firstScene: urlParams?.firstScene,
        secondScene: urlParams?.secondScene,
        reasonThinkEnabled: reasonThinkEnabled || extParams?.reasonThinkEnabled,
        currentAiRecordId: currentAiRecordIdRef.current,
        selectedJsonSchema: selectedJsonSchema.current,
        pageJsonSchema: beforePageSchema.current,
      },
      agentId: urlParams?.agentId,
      function: func,
      queryId: randomId()
    }
  
    switch (curScene) {
      case 'ai_form':
        handleAiForm(ucId, curScene, msgPayload, sse_body, sseUrl, func, extParams);
        break;
      case 'ai_groovy':
        handleAiGroovy(ucId, curScene, msgPayload, sse_body, sseUrl, func);
        break;
      default:
        sseMsgHandler(ucId, curScene, sseUrl, msgPayload, sse_body);
        break;
    }
  }

  // 智能表单
  const handleAiForm = (ucid, curScene, msgPayload, body, sseChatUrl, func, extParams) => {
    if (func === 'generate_form') {
      const requestId = randomId();
      // AI局部调整表单
      generateHandler(ucid, requestId, curScene, msgPayload, extParams);
      // AI生成过程内容
      let [baseUrl, queryParams] = sseChatUrl.split('?');
      let url = baseUrl.replace('/chat', '/subscribe');
      body['queryId'] = requestId;
      sseMsgHandler(ucid, curScene, url, msgPayload, body, true);
    } else if (func === 'kemis_qa') {
      // FCN助手
      let [baseUrl, queryParams] = sseChatUrl.split('?');
      let url = baseUrl.replace('/chat', '/askFcn') + (queryParams ? '?' + queryParams : '');
      sseMsgHandler(ucid, curScene, url, msgPayload, body);
    }
  }

  // 智能Groovy
  const handleAiGroovy = (ucid, curScene, msgPayload, body, sseChatUrl, func) => {
    if (func === 'retrieval_groovy_list') {
      // 推荐 groovy
      generateGroovyHandler(ucid, curScene, msgPayload, saveConversationMsg, addSingleMsg, getfunction, userInfoRef.current);
    } else {
      // 对话生成 groovy
      body['extParams'] = {
        enableInputOutputParams: enableInputOutputParamsRef.current
      }
      reloadExtParams(() => sseMsgHandler(ucid, curScene, sseChatUrl, msgPayload, body), contextIdRef.current, luoshuExtParamsRef.current);
    }
  }

  // AI对话，长连接消息
  const sseMsgHandler = (ucid, curScene, sseChatUrl, msgPayload, body, needCollapseMarkdown?) => {
      setHasContent(true);
      setEvtSourceClosed(false);
      setExtRegionInfos(null);
      scrollToBottom();
      setDisableInputBox(true);
      collapseMarkdownRef.current = needCollapseMarkdown ? needCollapseMarkdown : false;

      if (props.changeStop) {
        // 展示停止按钮
        props.changeStop(true);
      }
      let mkcontent = '';
      let reasoncontent = '';
      
      msyPayloadRef.current = msgPayload;
      const msg_data = { 
        id: randomId(), 
        type: -225, 
        direction: 2, 
        query_content: 
        msgPayload, 
        scene_id: curScene, 
        sessionId: contextIdRef.current,
        cur_func: getfunction() ,
        firstBizScene: urlParams?.firstScene,
        secondBizScene: urlParams?.secondScene
      }
      
      const sseClient = new FetchEventSource(sseChatUrl, e => {
          let { event, data } = e || {};

          if (event === 'thinking') {
            // AI思考内容
            const thinking_data = { 
              id: randomId(), 
              type: -225, 
              direction: 2, 
              scene_id: curScene, 
              sessionId: contextIdRef.current,
              cur_func: getfunction() ,
            }
            if (curScene === 'ai_form') {
               // 生成一条新的思考消息
              thinking_data['reasoningContent'] = data.replace('<thinking>', '').replace('</thinking>', '').replace('</thinking', '').replace('@reasoning@', '');
              addSingleMsg(thinking_data, '');
            }
          } else if (event === 'tool_use') {
            // 工具调用中
            const tool_data = { 
              id: randomId(), 
              type: -228, 
              direction: 2, 
              scene_id: curScene, 
              sessionId: contextIdRef.current,
              cur_func: getfunction() ,
            }
            if (curScene === 'ai_form') {
              // 生成一条新的消息
              addSingleMsg(tool_data, data);
            }
          }
          else if (event === 'message') {
            if (data.includes("@reasoning@")) {
              // TODO remove: 推理内容
              if (reasoncontent.length == 0) {
                reasoncontent += "> ";
              }
              reasoncontent += data.replace('@reasoning@', '').replace(/\\n\\n/g, '\n\n >');
              setReasoningContent(reasoncontent);
            } else {
              // 回复内容
              data = data.replace('close', '');
              // if (data.includes("```")) {
              //   data = data.replace(/\\n```\\n(\\n)+/g, '\n');
              // }
              mkcontent += data;
              // 移除指定标签内容
              mkcontent = removeTagsAndContent(curScene, mkcontent);

              // 处理流式数据
              const intent = parseAigcChunkResult(mkcontent);
              if (intent === 'design') {
                setMarkdownContent(brdWelcomeMsg);
              } else {
                if (mkcontent.includes('<TDD>')) { 
                  // TODO remove: 显示画布, 展示技术设计内容
                  window.parent.postMessage({ type: "showCanvas", data: mkcontent.replace(/\\n\\n/g, '\n\n ').replace(/\\n/g, '\n ') }, "*");
                  setMarkdownContent('正在生成...');
                } else {
                  setMarkdownContent(mkcontent);
                }
              }
            }
          } else if (event === 'error') {
            addSingleMsg(msg_data, data);
          } else if (event === 'prior_tool_result') {
            parsePriorityToolResult(props, curScene, requestIdRef, contextIdRef.current, appCodeRef, addSingleMsg, saveConversationForm, getfunction(), setUseTool, schemaRef, setDisableInputBox, userInfoRef.current, msyPayloadRef, data);
          } else {
            parseAiResultEvent(event, data);
          }

          scrollToBottom();
      }, ex => {
          if (!evtSourceClosed) {
              console.error(ex);
              sseClient.close();
          }
          setDisableInputBox(false);
      }, se => {
        // 服务端已结束
          console.info('sse closed after server completed.');
          sseClient.close();
          setEvtSourceClosed(true);
          setMarkdownContent('');
          setReasoningContent('');

          // 如果mkcontent不为空，则添加到消息列表
          if (mkcontent.length > 0 && !mkcontent.includes('use_tool')) { 
            // TODO remove
            if (mkcontent.includes('<TDD>')) {
              mkcontent = '设计完成，您可以编辑设计内容或者直接去生成应用';
            }

            // 解析内容，判断意图
            const { intent, result } = extractIntentAndResultRobust(mkcontent);
            if (intent !== 'design') {
              msg_data['done'] = true;
              msg_data['reasoningContent'] = reasoncontent;
              addSingleMsg(msg_data, mkcontent);
            } else {
              // 添加提示消息
              addSingleMsg(msg_data, brdWelcomeMsg);
              // 处理Ai生成的内容
              dealAigcResult(curScene, contextIdRef.current, addSingleMsg, mkcontent);
            }
          }

          // 后置处理
          postHandlerForSse(curScene, ucid, msgPayload, mkcontent, sseChatUrl, body, msg_data, props, setMarkdownContent, lastRequirementRef.current, sseMsgHandler, getfunction(), urlParams, contextIdRef.current, appCodeRef, setUseTool, beforePageSchema, setDisableInputBox, schemaRef, setShowInputBox, saveConversationForm, addSingleMsg, fileUrlRef); 
        });

      sseClient.connect(body);
      sseClientRef.current = sseClient;
  }
 
  // 修改页面
  const generateHandler = (ucid, requestId, curScene, msgPayload, extParams) => {
    try {
      // 保存消息
      const query_data = { id: randomId(), type: 120, direction: 2, query_content: msgPayload, scene_id: curScene }
      saveConversationMsg(query_data);
    } catch (error) {
      console.log(error)
    }

    // 执行生成逻辑
    exePipeline(ucid, requestId, curScene, msgPayload, "AIGC-Chat-Form-Pipeline", contextIdRef, appCodeRef, beforePageSchema, setDisableInputBox, schemaRef, addSingleMsg, setShowInputBox, setUseTool, saveConversationForm, props, urlParams?.spm, urlParams?.appCode, formCodeRef.current, selectedJsonSchema.current, extParams);
  } 


  // 收到主窗口数据
  const handleEvent = (event) => {
    // console.log("收到event:", event)
    const { type, newPageSchemaJson, selectedJson, initData, props, data } = event?.data;
    if (type === "schemaReload") {
      if (newPageSchemaJson) {
        // 最新的表单元数据
        beforePageSchema.current = newPageSchemaJson;
      }
      if (selectedJson) {
        // 选中的json
        selectedJsonSchema.current = selectedJson;
      }
    } else if (type === "initAppData" && initData) {
      // 当前应用编码
      appCodeRef.current = initData?.appCode;
      initDataRef.current = initData;
    } else if (type === "reloadExtParams" && props) {
      // 收到洛书应用的扩展参数
      luoshuExtParamsRef.current = JSON.parse(props);
    } else if (type === "urlParams" && data) {
      const { code, parentUrl } = data;
      if (code) {
        formCodeRef.current = code;
        // initDataRef.current对象中增加parentUrl属性
        initDataRef.current = {
          ...initDataRef.current,
          parentUrl
        };
      }
    } else if (type === "aiAppEventChange") {
      // 收到AI生成应用事件变更
      const { eventType, activeAiRecordId } = data;
      if (eventType === "activeAiRecord") {
        // 当前选中的AI结果记录
        currentAiRecordIdRef.current = activeAiRecordId;
      }

    } else {
      // 其他事件处理
      listenEventAndHandleMsg(chat, userInfoRef.current, getfunction(), sceneRef.current, contextIdRef.current, appCodeRef, requestIdRef, initDataRef.current, msyPayloadRef, addSingleMsg, setUseTool, event, actionCodeRef);
    }
  };

  // 重新加载页面schema
  const reloadPageSchema = () => {
    // 监听父页面的元数据变更
    window.addEventListener("message", handleEvent);
  }

  // （向父窗口）获取应用数据
  const fetchAppData = () => {
    window.parent.postMessage({ "type": "aiFetch", "data": "appData" }, "*");
  }

  // 监听消息
  const localEventListener = () => {
    // 监听模拟用户发送消息
    ImBus.on("SIMULATE_USER_MSG_SEND", (data: any) => {
      handleBtnAction(userInfoRef.current, getfunction(), sceneRef.current, contextIdRef.current, urlParams, appCodeRef, requestIdRef, actionCodeRef, initDataRef.current, addSingleMsg, setUseTool, chat, beforePageSchema, setDisableInputBox, schemaRef, setShowInputBox, saveConversationForm, props, lastRequirementRef.current, msyPayloadRef, fileUrlRef, data, reasonThinkEnabledRef.current);
    });
  }

  useEffect(() => {
    scrollToBottom();
    reloadPageSchema();
    fetchAppData();
    sseListener();
    localEventListener();

    return () => {
      window.removeEventListener('message', handleEvent);
    };
  }, []);

  useEffect(() => {
    sceneRef.current = props.curScene;
    contextIdRef.current = props.contextId;
    actionCodeRef.current = props.bubbleCode;
  }, [props]);

  useEffect(() => {
    // sseClient 为空
    if (props.isClickStopBtn && sseClientRef.current && !evtSourceClosed) {
      // 停止执行，关闭SSE
      console.log("手动点击了停止按钮");
      setEvtSourceClosed(true);
      sseClientRef.current.close();
      const msg_data = { id: randomId(), type: -225, direction: 2, query_content: msyPayloadRef.current, scene_id: sceneRef.current }
      addSingleMsg(msg_data, markdownContent);
      setMarkdownContent('');
    }
  }, [props.isClickStopBtn]);

  useEffect(() => {
    enableInputOutputParamsRef.current = props.enableInputOutputParams;
  }, [props.enableInputOutputParams]);

  useEffect(() => {
    scrollToBottom();
  }, [useTool]);

  return (
      <>
       {props.msgList.map((msg: any) => (<MsgItem key={`${msg.id}${msg.revocation}`} {...msg} msgId={msg.msgIdSequence} scrollToBottom={scrollToBottom} />))}
       { hasContent && !evtSourceClosed &&
        <div className="msg-wrapper service-msg">
         <img className="avatar" src={replaceProtocol(baseInfo.robotAvatar)} />
         <div>
            { hasContent && !evtSourceClosed  &&
              <>
                <div className="loading">
                  <div>
                    <span /><span /><span />
                  </div>
                </div>
              </>
            }
            
            <ChatPMPilot data={markdownContent} extRegionInfos={extRegionInfos} reasoningContent={reasoningContent} collapseMarkdown={collapseMarkdownRef.current} />
         </div> 
        </div>
       }

      {
        useTool && 
        <div className="msg-wrapper service-msg loading">
          <span /><span /><span />
        </div>
      }
      </>
  )
}

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(MsgSection);