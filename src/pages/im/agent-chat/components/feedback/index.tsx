import React, { useEffect, useState } from "react";
import { Toast, Modal, TextareaItem } from "antd-mobile";
import { doSaveConversationFeedbackForm,parseArtifactResult,onDigAIToolData,scrollToBottom } from "../utils/ai-chat-utils";
import md from "../utils/markdown-utils";
import { DIG_TYPE, EVT_ID, report_dig, UI_CODE } from '../utils/aigc_dig';

import "./index.less"; 

function AiFeedback(props): JSX.Element {
    const [showActionBtn, setShowActionBtn] = useState(true);
    const { modalShow, setModalShow, feedback, setFeedback } = props;
    const { 
        firstBizScene, 
        secondBizScene, 
        id, 
        type, 
        data, 
        content,
        scene_id, 
        sessionId, 
        cur_func, 
        next_action, 
        env, 
        immediatelyToProd,
        timestamp,
        source,
    } = props.props;
    const { ucId, name } = props.props.userInfo
    
    console.log("AiFeedback:", props);
     

    let msgContent = data || content;
    if (type === -225 || type === '-225' || type === -227 || type === '-227') {
        const ar = parseArtifactResult(msgContent);
        if (ar) {
          const { artifactType, artifactContent } = ar;
          if (artifactType === 'extRegionInfos') {
            msgContent = artifactContent;
          }
        }
    }

    // 判断是否是有效内容
    const isValidRequirementContent = () => {
        return !msgContent.includes('重试') && !msgContent.includes('没有找到相关内容') && !msgContent.includes('重新生成') && !msgContent.includes('此场景还没有开放配置');
    }


    const has_content = isValidRequirementContent();


    // 行为按钮
    const actionButton = () => {
        const buttonConfigs = [
            {
                condition: next_action === 'applyBigDataTable',
                button: { text: '申请明细', data: { msg: '申请数据明细', sendmsg: true, categroy: 'ai_workbench' } }
            },
            {
                condition: next_action === 'pubProd',
                button: { text: '上线', data: { msg: '应用上线', sendmsg: true } }
            },
            {
                condition: next_action === 'retryPubApp',
                button: { text: '重试', data: { msg: '发布重试', sendmsg: true, categroy: 'ai_workbench', env, className: 'warn-action-btn', immediatelyToProd } }
            },
            // {
            //     condition: (scene_id === 'ai_agent_app' || source === 'ai_agent_app') && !next_action  && isValidRequirementContent(),
            //     button: { text: '开始设计' , data: {  msg: '去设计', sendmsg: false, content: msgContent, nextAction: 'generatePreviewApp' } , icon: <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="#ffffff" d="M10 14.175L11 12l2.175-1L11 10l-1-2.175L9 10l-2.175 1L9 12l1 2.175ZM10 19l-2.5-5.5L2 11l5.5-2.5L10 3l2.5 5.5L18 11l-5.5 2.5L10 19Zm8 2l-1.25-2.75L14 17l2.75-1.25L18 13l1.25 2.75L22 17l-2.75 1.25L18 21Zm-8-10Z"/></svg> }
            // },
            {
                condition: (cur_func === 'requirement2PageCode') && isValidRequirementContent(),
                button: { text: '生成页面' , data: {  msg: '前端选型评估', sendmsg: false, content: msgContent, } , icon: <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="#ffffff" d="M10 14.175L11 12l2.175-1L11 10l-1-2.175L9 10l-2.175 1L9 12l1 2.175ZM10 19l-2.5-5.5L2 11l5.5-2.5L10 3l2.5 5.5L18 11l-5.5 2.5L10 19Zm8 2l-1.25-2.75L14 17l2.75-1.25L18 13l1.25 2.75L22 17l-2.75 1.25L18 21Zm-8-10Z"/></svg> }
            },
            {
                condition: scene_id !== 'ai_form' 
                    && msgContent && (
                    msgContent.includes('需求分析是否符合您的预期') 
                    || msgContent.includes("页面结构") || msgContent.includes("页面整体布局") || msgContent.includes("布局结构设计")
                    )
                ,
                button: { text: '确认', data: { msg: '没问题', sendmsg: true, content: msgContent, nextAction: 'generatePreviewApp' } }
            }
        ];

        // 返回第一个符合条件的按钮
        const firstButton = buttonConfigs.find(config => config.condition);
        return firstButton ? [firstButton.button] : [];
    };

    // 调用反馈接口
    const doFeedback = (action, feedback) => {
        if ("点踩" === action && !modalShow) {
            setModalShow(true)
        } else {
            doSaveConversationFeedbackForm(name, sessionId, action, data, feedback, id, () => {
                Toast.success("反馈成功，非常感谢", 1);
            }, err => {
                Toast.fail("反馈失败: " + err, 1);
            });
            onDigData(action, feedback);
        }
    }
    // 提交反馈
    const submitFeedback = (feedback) => {
        doFeedback("点踩", feedback);
        setModalShow(false);
    }

    // 向父窗口发送AI结果
    const sendAiResult = () => {
        const html = md.render(msgContent.trim().replace(/\\n\\n/g, '\n\n').replace(/\\n/g, '\n\n'));
        window.parent.postMessage({ type: "aiResult", subType:"sendMsg", data: html.trim() }, "*");
        onDigData('发送', msgContent);
    }

    // 复制AI结果
    const copyAiResult = () => {
        const html = md.render(msgContent.trim().replace(/\\n\\n/g, '\n\n').replace(/\\n/g, '\n'));
        window.parent.postMessage({ msg: "addIMMsgToInput", text: html.trim() }, "*");
        onDigData('复制', msgContent);
    }

    // 行为埋点
    const onDigData = (action, data) => {
        if (data && data.length > 700) {
            data = data.substring(0, 700);
        }
        onDigAIToolData(ucId, scene_id, cur_func, {
            context_id: sessionId,
            click_value: action,
            content: data,
            biz_scene1: firstBizScene,
            biz_scene2: secondBizScene
        });
    }

    // 模拟用户发送消息（快捷按钮）
    const simulationSendUserMsg = (data) => {
        if (scene_id === 'ai_agent_app' || source === 'ai_agent_app') {
            const contextId = sessionId.replace('ai', '')
            report_dig('ai_agent_app', DIG_TYPE.MODULE_CLICK, {'str1': contextId, 'str2': 'requirement'}, EVT_ID.BEGIN_DESIGN_ONCLICK, UI_CODE.REQUIRE_DESIGN_CODE)
        }
        window.ImBus.emit("SIMULATE_USER_MSG_SEND", data);
    }

    // 监听新消息事件
    const listenNewMsg = () => {
        ImBus.on("LAST_SEND_MSG", (data: any) => {
            // 如果当前消息不是最新消息，则不展示行为按钮
            setShowActionBtn(timestamp === data.timestamp);
        });
    }

    useEffect(() => {
        if (props.props.lastMsg === 'false') {
            setShowActionBtn(false);
        }
    }, [props.props.lastMsg])

    useEffect(() => {
        scrollToBottom();
        listenNewMsg();

        const timer = setTimeout(() => {
            scrollToBottom();
        }, 500);
    
        return () => clearTimeout(timer);
    }, []);

    return (
      <>
      {
        showActionBtn && actionButton().map((button, index) => (
          <div key={index} className="ai-action-op-container operator-container cus-bottom relative flex justify-center">
              <button 
                className={`result-action-btn ${button.data?.className}`}
                onClick={() => simulationSendUserMsg(button.data)}
              >
                {
                    button.icon && 
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="#ffffff" d="M10 14.175L11 12l2.175-1L11 10l-1-2.175L9 10l-2.175 1L9 12l1 2.175ZM10 19l-2.5-5.5L2 11l5.5-2.5L10 3l2.5 5.5L18 11l-5.5 2.5L10 19Zm8 2l-1.25-2.75L14 17l2.75-1.25L18 13l1.25 2.75L22 17l-2.75 1.25L18 21Zm-8-10Z"/></svg>
                }
                {button.text}
              </button>
          </div>
        ))
      }
      
      {
        has_content && scene_id === 'ai_agent' && 
        <div className="operator-container absolute -bottom-3.5 -right-1.5 sm:right-2 group relative">
            <div className="border-0.5 border-border-300 flex items-center translate-y-1/2 rounded-lg px-2 pb-1 pt-3 shadow-sm transition scale-95 group-hover:scale-100 group-hover:opacity-100 bg-neutral-200">
                <div className="text-text-400 -mx-1 -mt-2 flex items-stretch justify-between gap-0.5">
                    <div className="flex gap-0.5">
                        <button onClick={() => doFeedback('点赞', '')} title="Share positive feedback" className="flex flex-row items-center gap-1 rounded-md p-1 py-0.5 text-xs transition-opacity delay-100 hover:bg-bg-200">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 256 256">
                            <path d="M234,80.12A24,24,0,0,0,216,72H160V56a40,40,0,0,0-40-40,8,8,0,0,0-7.16,4.42L75.06,96H32a16,16,0,0,0-16,16v88a16,16,0,0,0,16,16H204a24,24,0,0,0,23.82-21l12-96A24,24,0,0,0,234,80.12ZM32,112H72v88H32ZM223.94,97l-12,96a8,8,0,0,1-7.94,7H88V105.89l36.71-73.43A24,24,0,0,1,144,56V80a8,8,0,0,0,8,8h64a8,8,0,0,1,7.94,9Z"></path>
                            </svg>
                        </button>
                        <button onClick={() => doFeedback('点踩', '')} title="Report issue" className="flex flex-row items-center gap-1 rounded-md p-1 py-0.5 text-xs transition-opacity delay-100 hover:bg-bg-200">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 256 256">
                            <path d="M239.82,157l-12-96A24,24,0,0,0,204,40H32A16,16,0,0,0,16,56v88a16,16,0,0,0,16,16H75.06l37.78,75.58A8,8,0,0,0,120,240a40,40,0,0,0,40-40V184h56a24,24,0,0,0,23.82-27ZM72,144H32V56H72Zm150,21.29a7.88,7.88,0,0,1-6,2.71H152a8,8,0,0,0-8,8v24a24,24,0,0,1-19.29,23.54L88,150.11V56H204a8,8,0,0,1,7.94,7l12,96A7.87,7.87,0,0,1,222,165.29Z"></path>
                            </svg>
                        </button>
                    </div>
                    
                    {
                        firstBizScene === 'IM' &&
                        <div className="flex gap-0.5 ml-1">
                            <button onClick={ copyAiResult } className="flex flex-row items-center gap-1 rounded-md p-1 py-0.5 text-xs transition-opacity delay-100 hover:bg-bg-200">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24"><g fill="none" stroke="#666666" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="M7 9.667A2.667 2.667 0 0 1 9.667 7h8.666A2.667 2.667 0 0 1 21 9.667v8.666A2.667 2.667 0 0 1 18.333 21H9.667A2.667 2.667 0 0 1 7 18.333z"/><path d="M4.012 16.737A2 2 0 0 1 3 15V5c0-1.1.9-2 2-2h10c.75 0 1.158.385 1.5 1"/></g></svg>
                                复制
                            </button>
                            <button onClick={ sendAiResult } className="flex flex-row items-center gap-1 rounded-md p-1 py-0.5 text-xs transition-opacity delay-100 hover:bg-bg-200">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24"><path fill="none" stroke="#666666" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14L21 3m0 0l-6.5 18a.55.55 0 0 1-1 0L10 14l-7-3.5a.55.55 0 0 1 0-1z"/></svg>
                                发送
                            </button>
                        </div>
                    }
                    
                </div>
            </div>
        </div>
      }
        <Modal
            transparent
            visible={modalShow}
            maskClosable={true}
            onClose={() => setModalShow(false)}
            animationType="slide-up"
            footer={[
            { text: "取消", onPress: () => submitFeedback(feedback) },
            { text: "确认", onPress: () => submitFeedback(feedback)}
            ]}
        >
            <h4>反馈</h4>
            <TextareaItem
            onChange={e => setFeedback(e)}
            placeholder={"请输入对结果不满的地方，非常感谢您的反馈..." }
            rows={5}
            />
        </Modal>
      </>
    )
}

export default AiFeedback