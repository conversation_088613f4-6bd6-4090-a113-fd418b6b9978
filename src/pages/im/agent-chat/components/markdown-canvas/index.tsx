import React, { useEffect, useRef } from 'react';
import { Crepe } from '@milkdown/crepe';
import { Tooltip } from 'react-tooltip';

import './index.less';
import './markdown-styles.css';

interface MarkdownCanvasProps {
  initialMarkdown?: string;
  onChange: (markdown: string) => void;
  onClose?: () => void;
  onStartDesign?: () => void;
  showStartDesign?: boolean;
}

const MarkdownCanvas: React.FC<MarkdownCanvasProps> = ({ 
  initialMarkdown = '',
  onChange,
  onClose,
  onStartDesign,
  showStartDesign
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const crepeRef = useRef<Crepe | null>(null);
  const prevMarkdownRef = useRef<string>(initialMarkdown);

  useEffect(() => {
    if (!editorRef.current) return;

    const initEditor = async () => {
      editorRef.current!.innerHTML = '';
      
      // 正确配置Crepe编辑器
      const crepe = new Crepe({
        root: editorRef.current,
        defaultValue: initialMarkdown,
        featureConfigs: {  
          [Crepe.Feature.Placeholder]: {  
            text: '请输入内容...' // 自定义占位符文本  
          }  
        } 
      });

      crepe.on((api) => {
        api.markdownUpdated((ctx, markdown, prevMarkdown) => {
          onChange(markdown);
        });
      });

      await crepe.create();
      crepeRef.current = crepe;
      prevMarkdownRef.current = initialMarkdown;
    };

    // 如果编辑器已存在且内容不同，则更新内容
    if (crepeRef.current && initialMarkdown !== prevMarkdownRef.current) {
      // 先销毁旧编辑器再创建新编辑器
      crepeRef.current.destroy();
      initEditor();
    } else {
      // 否则初始化编辑器
      initEditor();
    }

    return () => {
      if (crepeRef.current) {
        crepeRef.current.destroy();
      }
    };
  }, [initialMarkdown, onChange]);

  return (
    <div className="markdown-canvas-wrapper">
      <div 
        ref={editorRef} 
        className="markdown-canvas-container"
      />
      <div id="markdown-canvas-close" className="markdown-canvas-close" onClick={onClose}>
        ×
      </div>
      <Tooltip
        anchorSelect="#markdown-canvas-close"
        content="关闭产品方案"
        />

        {
          showStartDesign &&
          <button 
            className="markdown-canvas-design-btn"
            onClick={onStartDesign}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="#ffffff" d="M10 14.175L11 12l2.175-1L11 10l-1-2.175L9 10l-2.175 1L9 12l1 2.175ZM10 19l-2.5-5.5L2 11l5.5-2.5L10 3l2.5 5.5L18 11l-5.5 2.5L10 19Zm8 2l-1.25-2.75L14 17l2.75-1.25L18 13l1.25 2.75L22 17l-2.75 1.25L18 21Zm-8-10Z"/></svg>
            开始设计
          </button>
        }
      
    </div>
  );
};

export default MarkdownCanvas;
