.markdown-canvas-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.markdown-canvas-container {
  border-radius: 4px;
  min-height: 300px;
  width: 100%;
  height: 100%;
  overflow-y: scroll;
}

.markdown-canvas-close {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 23px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.1);
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  z-index: 10;
}

.markdown-canvas-design-btn {
  position: absolute;
  bottom: 20px;
  right: 20px;
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  z-index: 10;
  display: flex;
  align-items: center;

  &:hover {
    transform: scale(1.1);
  }
}