.milkdown {
  --crepe-color-background: #ffffff;
  --crepe-color-on-background: #000000;
  --crepe-color-surface: #f7f7f7;
  --crepe-color-surface-low: #ededed;
  --crepe-color-on-surface: #1c1c1c;
  --crepe-color-on-surface-variant: #4d4d4d;
  --crepe-color-outline: #a8a8a8;
  --crepe-color-primary: #333333;
  --crepe-color-secondary: #cfcfcf;
  --crepe-color-on-secondary: #000000;
  --crepe-color-inverse: #f0f0f0;
  --crepe-color-on-inverse: #1a1a1a;
  --crepe-color-inline-code: #ba1a1a;
  --crepe-color-error: #ba1a1a;
  --crepe-color-hover: #e0e0e0;
  --crepe-color-selected: #d5d5d5;
  --crepe-color-inline-area: #cacaca;

  --crepe-font-title: '<PERSON><PERSON> Serif', Cambria, 'Times New Roman', Times, serif;
  --crepe-font-default: 'Noto Sans', Arial, Helvetica, sans-serif;
  --crepe-font-code:
    'Space Mono', Fira Code, <PERSON>lo, Monaco, 'Courier New', Courier, monospace;

  --crepe-shadow-1:
    0px 1px 3px 1px rgba(0, 0, 0, 0.15), 0px 1px 2px 0px rgba(0, 0, 0, 0.3);
  --crepe-shadow-2:
    0px 2px 6px 2px rgba(0, 0, 0, 0.15), 0px 1px 2px 0px rgba(0, 0, 0, 0.3);
}


.milkdown .milkdown-block-handle[data-show='false'] {
      opacity: 0;
      pointer-events: none;
    }
    .milkdown .milkdown-block-handle {
    transition: all 0.2s;
    position: absolute;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2px;
}
    .milkdown .milkdown-block-handle .operation-item {
      border-radius: 4px;
      width: 32px;
      height: 32px;
      padding: 4px;
    }
    .milkdown .milkdown-block-handle .operation-item svg {
        width: 24px;
        height: 24px;
        fill: var(--crepe-color-outline);
      }
    .milkdown .milkdown-block-handle .operation-item:hover {
        background: var(--crepe-color-hover);
      }
    .milkdown .milkdown-block-handle .operation-item.active {
        background: var(--crepe-color-selected);
      }
  .milkdown .milkdown-slash-menu[data-show='false'] {
      display: none;
    }
  .milkdown .milkdown-slash-menu {
    position: absolute;
    z-index: 10;
    display: block;
    font-family: var(--crepe-font-default);
    color: var(--crepe-color-on-surface);
    background: var(--crepe-color-surface);
    border-radius: 12px;
    box-shadow: var(--crepe-shadow-1);
}
  .milkdown .milkdown-slash-menu ul {
      list-style-type: none;
    }
  .milkdown .milkdown-slash-menu ul li {
        cursor: pointer;
        border-radius: 8px;
      }
  .milkdown .milkdown-slash-menu .tab-group {
      border-bottom: 1px solid
        color-mix(in srgb, var(--crepe-color-outline), transparent 80%);
      padding: 12px 12px 0;
    }
  .milkdown .milkdown-slash-menu .tab-group ul {
        padding: 8px 10px;
        display: flex;
        gap: 10px;
        flex-wrap: nowrap;
      }
  .milkdown .milkdown-slash-menu .tab-group ul li {
          padding: 6px 10px;
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 20px;
        }
  .milkdown .milkdown-slash-menu .tab-group ul li:hover {
            background: var(--crepe-color-hover);
          }
  .milkdown .milkdown-slash-menu .tab-group ul li.selected {
            background: var(--crepe-color-selected);
          }
  .milkdown .milkdown-slash-menu .menu-groups {
      padding: 0 12px 12px;
      max-height: 420px;
      overflow: auto;
      overscroll-behavior: contain;
      scroll-behavior: smooth;
    }
  .milkdown .milkdown-slash-menu .menu-groups .menu-group h6 {
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 20px;
          padding: 14px 10px;
          text-transform: uppercase;
          color: color-mix(
            in srgb,
            var(--crepe-color-on-surface),
            transparent 40%
          );
        }
  .milkdown .milkdown-slash-menu .menu-groups .menu-group li {
          min-width: 220px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          gap: 16px;
          padding: 14px 10px;
        }
  .milkdown .milkdown-slash-menu .menu-groups .menu-group li.hover {
            background: var(--crepe-color-hover);
          }
  .milkdown .milkdown-slash-menu .menu-groups .menu-group li.active {
            background: var(--crepe-color-selected);
          }
  .milkdown .milkdown-slash-menu .menu-groups .menu-group li svg {
            width: 24px;
            height: 24px;
            color: var(--crepe-color-outline);
            fill: var(--crepe-color-outline);
          }
  .milkdown .milkdown-slash-menu .menu-groups .menu-group li > span {
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 20px;
          }
  .milkdown .milkdown-slash-menu .menu-groups .menu-group + .menu-group::before {
        content: '';
        display: block;
        height: 1px;
        background: color-mix(
          in srgb,
          var(--crepe-color-outline),
          transparent 80%
        );
        margin: 0 10px;
      }
.milkdown .milkdown-code-block {
    display: block;
    position: relative;
    padding: 8px 20px 20px;
    background: var(--crepe-color-surface);
    margin: 4px 0;
  }

    .milkdown .milkdown-code-block .language-picker {
      padding-top: 10px;
      width: -moz-max-content;
      width: max-content;
      position: absolute;
      z-index: 999;
    }

    .milkdown .milkdown-code-block .hidden {
      display: none !important;
    }

    .milkdown .milkdown-code-block.selected {
      outline: 1px solid var(--crepe-color-primary);
    }

    .milkdown .milkdown-code-block .cm-editor {
      outline: none !important;
      background: var(--crepe-color-surface);
    }

    .milkdown .milkdown-code-block .cm-gutters {
      border-right: none;
      background: var(--crepe-color-surface);
    }

    .milkdown .milkdown-code-block .cm-panel {
      font-family: var(--crepe-font-default);
      background: var(--crepe-color-surface);
      color: var(--crepe-color-on-surface);
    }

    .milkdown .milkdown-code-block .cm-panel input {
        caret-color: var(--crepe-color-outline);
        border-radius: 4px;
        background: var(--crepe-color-surface-low);
      }

    .milkdown .milkdown-code-block .cm-panel > button {
        text-transform: capitalize;
        background: var(--crepe-color-surface-low);
        color: var(--crepe-color-on-surface-variant);
        border: 1px solid var(--crepe-color-outline);
        font-weight: 600;
        cursor: pointer;
        border-radius: 4px;
      }

    .milkdown .milkdown-code-block .cm-panel > button:hover {
          background: var(--crepe-color-hover);
        }

    .milkdown .milkdown-code-block .cm-panel > label {
        display: inline-flex;
        align-items: center;
        text-transform: capitalize;
      }

    .milkdown .milkdown-code-block .cm-panel > label input[type='checkbox'] {
          border-radius: 4px;
          cursor: pointer;
          -moz-appearance: none;
               appearance: none;
          -webkit-appearance: none;
          background: var(--crepe-color-surface-low);
          width: 1.15em;
          height: 1.15em;
          border: 1px solid var(--crepe-color-outline);
          display: grid;
          place-content: center;
        }

    .milkdown .milkdown-code-block .cm-panel > label input[type='checkbox']::before {
            content: '';
            transform-origin: bottom left;
            width: 0.65em;
            height: 0.65em;
            transform: scale(0);
            transition: 120ms transform ease-in-out;
            box-shadow: inset 1em 1em var(--crepe-color-outline);
            clip-path: polygon(
              14% 44%,
              0 65%,
              50% 100%,
              100% 16%,
              80% 0%,
              43% 62%
            );
          }

    .milkdown .milkdown-code-block .cm-panel > label input[type='checkbox']:checked::before {
            transform: scale(1);
          }

    .milkdown .milkdown-code-block .tools {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .milkdown .milkdown-code-block .tools input {
        caret-color: var(--crepe-color-outline);
      }

    .milkdown .milkdown-code-block .tools .tools-button-group {
        display: flex;
        gap: 2px;
      }

    .milkdown .milkdown-code-block .tools .tools-button-group button {
          background: var(--crepe-color-secondary);
          color: var(--crepe-color-on-surface-variant);
          padding: 4px 10px;
          opacity: 0;
          cursor: pointer;
          border-radius: 4px;
          font-size: 12px;
          line-height: 16px;
          font-weight: 600;
          font-family: var(--crepe-font-default);
          transition: opacity 0.2s ease-in-out;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 4px;
        }

    .milkdown .milkdown-code-block .tools .tools-button-group button svg {
            width: 14px;
            height: 14px;
            fill: var(--crepe-color-on-surface-variant);
          }

    .milkdown .milkdown-code-block .tools .tools-button-group button:first-child {
          border-top-left-radius: 100px;
          border-bottom-left-radius: 100px;
        }

    .milkdown .milkdown-code-block .tools .tools-button-group button:last-child {
          border-top-right-radius: 100px;
          border-bottom-right-radius: 100px;
        }

    .milkdown .milkdown-code-block .tools .language-button {
        display: flex;
        align-items: center;
        font-family: var(--crepe-font-default);
        gap: 6px;
        padding: 2px 4px 2px 8px;
        background: var(--crepe-color-surface-low);
        color: var(--crepe-color-on-surface-variant);
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        line-height: 16px;
        margin-bottom: 8px;
        opacity: 0;
        cursor: pointer;
        transition: opacity 0.2s ease-in-out;
      }

    .milkdown .milkdown-code-block .tools .language-button:hover {
          background: var(--crepe-color-hover);
        }

    .milkdown .milkdown-code-block .tools .language-button .expand-icon {
          transition: transform 0.2s ease-in-out;
          width: 18px;
          height: 18px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

    .milkdown .milkdown-code-block .tools .language-button .expand-icon svg {
          width: 14px;
          height: 14px;
          color: var(--crepe-color-outline);
        }

    .milkdown .milkdown-code-block .tools .language-button[data-expanded='true'] .expand-icon {
          transform: rotate(180deg);
        }

    .milkdown .milkdown-code-block .tools .language-button .expand-icon svg:focus,
        .milkdown .milkdown-code-block .tools .language-button .expand-icon:focus-visible {
          outline: none;
        }

    .milkdown .milkdown-code-block:hover .language-button {
      opacity: 1;
    }

    .milkdown .milkdown-code-block:hover .tools-button-group > button {
      opacity: 1;
    }

    .milkdown .milkdown-code-block .list-wrapper {
      background: var(--crepe-color-surface-low);
      border-radius: 12px;
      box-shadow: var(--crepe-shadow-1);
      width: 240px;
      padding-top: 12px;
    }

    .milkdown .milkdown-code-block .language-list {
      height: 410px;
      overflow-y: auto;
      overscroll-behavior: contain;
      margin: 0;
      padding: 0;
    }

    .milkdown .milkdown-code-block .language-list-item {
      cursor: pointer;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px 22px;
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
    }

    .milkdown .milkdown-code-block .language-list-item:hover {
        background: var(--crepe-color-hover);
      }

    .milkdown .milkdown-code-block .language-list-item:focus-visible {
        outline: none;
        background: var(--crepe-color-hover);
      }

    .milkdown .milkdown-code-block .language-list-item .leading,
      .milkdown .milkdown-code-block .language-list-item .leading svg {
        width: 24px;
        height: 24px;
      }

    .milkdown .milkdown-code-block .language-list-item.no-result {
        cursor: default;
        opacity: 0.6;
      }

    .milkdown .milkdown-code-block .language-list-item.no-result:hover {
          background: transparent;
        }

    .milkdown .milkdown-code-block .search-box {
      display: flex;
      align-items: center;
      margin: 0 12px 8px;
      background: transparent;
      border-radius: 4px;
      outline: 1px solid var(--crepe-color-primary);
      gap: 8px;
      padding: 6px 10px;
    }

    .milkdown .milkdown-code-block .search-box:has(input:focus) {
        outline: 2px solid var(--crepe-color-primary);
      }

    .milkdown .milkdown-code-block .search-box .search-input {
        width: 100%;
        color: var(--crepe-color-on-surface);
      }

    .milkdown .milkdown-code-block .search-box .search-icon {
        display: none;
      }

    .milkdown .milkdown-code-block .search-box .clear-icon {
        cursor: pointer;
        width: 20px;
        height: 20px;
      }

    .milkdown .milkdown-code-block .search-box .clear-icon svg {
          width: 20px;
          height: 20px;
          color: var(--crepe-color-primary);
          fill: var(--crepe-color-primary);
        }

    .milkdown .milkdown-code-block .search-box .clear-icon:hover {
          background: var(--crepe-color-hover);
        }

    .milkdown .milkdown-code-block .search-box input {
        font-family: var(--crepe-font-default);
        font-size: 14px;
        line-height: 20px;
        background: transparent;
      }

    .milkdown .milkdown-code-block .search-box input:focus {
        outline: none;
      }

    .milkdown .milkdown-code-block .preview-panel .preview-divider {
        height: 1px;
        opacity: 0.2;
        background: var(--crepe-color-outline);
        margin: 6px 0;
      }

    .milkdown .milkdown-code-block .preview-panel .preview-label {
        margin: 6px 0;
        font-size: 12px;
        color: color-mix(
          in srgb,
          var(--crepe-color-on-surface),
          transparent 40%
        );
        font-weight: 600;
        text-transform: uppercase;
        font-family: var(--crepe-font-default);
      }

    .milkdown .milkdown-code-block .preview-panel .preview {
        text-align: center;
        overflow-x: auto;
      }

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}

.ProseMirror.virtual-cursor-enabled {
  /* Hide the native cursor */
  caret-color: transparent;
}

.ProseMirror-focused {
  /* Color of the virtual cursor */
  --prosemirror-virtual-cursor-color: red;
}

.ProseMirror .prosemirror-virtual-cursor {
  position: absolute;
  cursor: text;
  pointer-events: none;
  transform: translate(-1px);
  user-select: none;
  -webkit-user-select: none;
  border-left: 2px solid var(--prosemirror-virtual-cursor-color);
}

.ProseMirror .prosemirror-virtual-cursor-left {
  width: 1ch;
  transform: translate(calc(-1ch + -1px));
  border-bottom: 2px solid var(--prosemirror-virtual-cursor-color);
  border-right: 2px solid var(--prosemirror-virtual-cursor-color);
  border-left: none;
}

.ProseMirror .prosemirror-virtual-cursor-right {
  width: 1ch;
  border-bottom: 2px solid var(--prosemirror-virtual-cursor-color);
  border-left: 2px solid var(--prosemirror-virtual-cursor-color);
  border-right: none;
}

.ProseMirror-focused .prosemirror-virtual-cursor-animation {
  animation: prosemirror-virtual-cursor-blink 1s linear infinite;
  animation-delay: 0.5s;
}

@keyframes prosemirror-virtual-cursor-blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.milkdown .crepe-drop-cursor {
    background-color: color-mix(
      in srgb,
      var(--crepe-color-outline),
      transparent 50%
    );
    opacity: 0.5;
    transition: all 0.2s;
    pointer-events: none;
  }
.milkdown .ProseMirror-gapcursor:after {
    box-sizing: border-box;
    border-top: 1px solid var(--crepe-color-on-background);
  }

.milkdown .ProseMirror-focused {
    --prosemirror-virtual-cursor-color: var(--crepe-color-outline);
  }

.milkdown .milkdown-image-inline {
    outline: none;
    display: inline-flex;
    vertical-align: text-bottom;
  }

    .milkdown .milkdown-image-inline input {
      background: transparent;
      outline: none;
      border: 0;
      caret-color: var(--crepe-color-outline);
    }

    .milkdown .milkdown-image-inline > .empty-image-inline {
      display: inline-flex;
    }

    .milkdown .milkdown-image-inline > .empty-image-inline .confirm {
      cursor: pointer;
    }

    .milkdown .milkdown-image-inline > .empty-image-inline .link-importer {
      position: relative;
      flex: 1;
    }

    .milkdown .milkdown-image-inline > .empty-image-inline .link-importer > .link-input-area {
      width: 208px;
      color: var(--crepe-color-on-background);
      display: flex;
    }

    .milkdown .milkdown-image-inline > .empty-image-inline .link-importer .placeholder {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      cursor: text;
    }

    .milkdown .milkdown-image-inline > .empty-image-inline .link-importer .placeholder .uploader {
      cursor: pointer;
      display: flex;
    }

    .milkdown .milkdown-image-inline .hidden {
      display: none !important;
    }

    .milkdown .milkdown-image-inline.empty.selected {
      background: none;
      outline: none;
    }

    .milkdown .milkdown-image-inline.empty.selected .empty-image-inline {
        box-shadow: var(--crepe-shadow-1);
      }

    .milkdown .milkdown-image-inline.selected {
      background: none;
      outline: 1px solid var(--crepe-color-primary);
    }

    .milkdown .milkdown-image-inline.selected :not(input)::-moz-selection {
        background: transparent;
      }

    .milkdown .milkdown-image-inline.selected :not(input)::selection {
        background: transparent;
      }

    .milkdown .milkdown-image-inline .empty-image-inline {
      align-items: center;
      padding: 4px 10px;
      gap: 10px;
      background: var(--crepe-color-surface);
      font-family: var(--crepe-font-default);
      border-radius: 8px;
      font-size: 16px;
    }

    .milkdown .milkdown-image-inline .empty-image-inline .image-icon svg {
        width: 18px;
        height: 18px;
        fill: var(--crepe-color-outline);
      }

    .milkdown .milkdown-image-inline .empty-image-inline .image-icon {
      padding: 3px;
      width: 24px;
      height: 24px;
}

    .milkdown .milkdown-image-inline .empty-image-inline .link-importer {
      height: 24px;
    }

    .milkdown .milkdown-image-inline .empty-image-inline .link-importer .placeholder {
      color: color-mix(
        in srgb,
        var(--crepe-color-on-background),
        transparent 60%
      );
    }

    .milkdown .milkdown-image-inline .empty-image-inline .link-importer .placeholder :not(input)::-moz-selection {
        background: transparent;
      }

    .milkdown .milkdown-image-inline .empty-image-inline .link-importer .placeholder :not(input)::selection {
        background: transparent;
      }

    .milkdown .milkdown-image-inline .empty-image-inline .link-importer .link-input-area {
      line-height: 24px;
    }

    .milkdown .milkdown-image-inline .empty-image-inline .link-importer .placeholder .uploader {
      gap: 8px;
      color: var(--crepe-color-primary);
      justify-content: center;
      transition: color 0.2s;
      font-family: var(--crepe-font-default);
    }

    .milkdown .milkdown-image-inline .empty-image-inline .link-importer.focus .placeholder .uploader {
      color: unset;
    }

    .milkdown .milkdown-image-inline .empty-image-inline .link-importer .placeholder .uploader:hover {
      color: var(--crepe-color-primary);
    }

    .milkdown .milkdown-image-inline .empty-image-inline .link-importer .placeholder .text {
      margin-left: 8px;
    }

    .milkdown .milkdown-image-inline .empty-image-inline .confirm svg {
        width: 18px;
        height: 18px;
      }

    .milkdown .milkdown-image-inline .empty-image-inline .confirm {
      display: flex;
      width: 24px;
      height: 24px;
      padding: 3px;
      border-radius: 8px;
      color: var(--crepe-color-primary);
}

    .milkdown .milkdown-image-inline .empty-image-inline .confirm:hover {
        background: var(--crepe-color-hover);
      }
  .milkdown .milkdown-image-block {
    outline: none;
    margin: 4px 0;
    display: block;
  }
  .milkdown .milkdown-image-block > .image-wrapper {
      position: relative;
      width: -moz-fit-content;
      width: fit-content;
      margin: 0 auto;
      min-width: 100px;
    }
  .milkdown .milkdown-image-block > .image-wrapper .operation {
      position: absolute;
      display: flex;
    }
  .milkdown .milkdown-image-block > .image-wrapper .operation > .operation-item {
      cursor: pointer;
    }
  .milkdown .milkdown-image-block > .image-wrapper img {
      max-width: 100%;
      min-height: 100px;
      display: block;
      -o-object-fit: cover;
         object-fit: cover;
    }
  .milkdown .milkdown-image-block > .image-wrapper > .image-resize-handle {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }
  .milkdown .milkdown-image-block > .image-wrapper > .image-resize-handle:hover {
      cursor: row-resize;
    }
  .milkdown .milkdown-image-block input {
      background: transparent;
      outline: none;
      border: 0;
      caret-color: var(--crepe-color-outline);
    }
  .milkdown .milkdown-image-block > .caption-input {
      display: block;
      width: 100%;
      text-align: center;
      color: var(--crepe-color-on-background);
    }
  .milkdown .milkdown-image-block > .image-edit {
      display: flex;
    }
  .milkdown .milkdown-image-block > .image-edit .confirm {
      cursor: pointer;
    }
  .milkdown .milkdown-image-block > .image-edit .link-importer {
      position: relative;
      flex: 1;
    }
  .milkdown .milkdown-image-block > .image-edit .link-importer > .link-input-area {
      width: 100%;
    }
  .milkdown .milkdown-image-block > .image-edit .link-importer .placeholder {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      cursor: text;
    }
  .milkdown .milkdown-image-block > .image-edit .link-importer .placeholder .uploader {
      cursor: pointer;
      display: flex;
    }
  .milkdown .milkdown-image-block .hidden {
      display: none !important;
    }
  .milkdown .milkdown-image-block.selected > .image-edit:not(:has(input:focus)) {
      position: relative;
    }
  .milkdown .milkdown-image-block.selected > .image-edit:not(:has(input:focus))::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: color-mix(
          in srgb,
          var(--crepe-color-selected),
          transparent 60%
        );
        pointer-events: none;
      }
  .milkdown .milkdown-image-block.selected > .image-wrapper {
        position: relative;
      }
  .milkdown .milkdown-image-block.selected > .image-wrapper::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: color-mix(
            in srgb,
            var(--crepe-color-selected),
            transparent 60%
          );
        }
  .milkdown .milkdown-image-block.selected :not(input)::-moz-selection {
        background: transparent;
      }
  .milkdown .milkdown-image-block.selected :not(input)::selection {
        background: transparent;
      }
  .milkdown .milkdown-image-block .image-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  .milkdown .milkdown-image-block .image-wrapper .operation {
      gap: 12px;
      right: 12px;
      top: 12px;
      opacity: 0;
      transition: all 0.2s;
    }
  .milkdown .milkdown-image-block:hover > .image-wrapper .operation {
      opacity: 1;
    }
  .milkdown .milkdown-image-block .image-wrapper .operation > .operation-item {
      color: var(--crepe-color-on-inverse);
      padding: 4px;
      background: var(--crepe-color-inverse);
      opacity: 0.6;
      border-radius: 50%;
      width: 32px;
      height: 32px;
    }
  .milkdown .milkdown-image-block .image-wrapper .operation > .operation-item svg {
        width: 24px;
        height: 24px;
      }
  .milkdown .milkdown-image-block .image-wrapper .image-resize-handle {
      height: 4px;
      bottom: -2px;
      max-width: 160px;
      width: 100%;
      background: var(--crepe-color-outline);
      opacity: 0;
      transition: all 0.2s;
      border-radius: 4px;
    }
  .milkdown .milkdown-image-block:hover > .image-wrapper .image-resize-handle {
      opacity: 1;
    }
  .milkdown .milkdown-image-block .caption-input {
      margin: 4px auto;
      font-family: var(--crepe-font-default);
    }
  .milkdown .milkdown-image-block .image-edit {
      align-items: center;
      padding: 16px 24px;
      gap: 16px;
      background: var(--crepe-color-surface);
      height: 56px;
    }
  .milkdown .milkdown-image-block .image-edit .image-icon {
      color: var(--crepe-color-outline);
    }
  .milkdown .milkdown-image-block .image-edit .image-icon svg {
      width: 24px;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      fill: var(--crepe-color-outline);
    }
  .milkdown .milkdown-image-block .image-edit .link-importer .placeholder {
      color: color-mix(
        in srgb,
        var(--crepe-color-on-background),
        transparent 60%
      );
    }
  .milkdown .milkdown-image-block .image-edit .link-importer .placeholder :not(input)::-moz-selection {
        background: transparent;
      }
  .milkdown .milkdown-image-block .image-edit .link-importer .placeholder :not(input)::selection {
        background: transparent;
      }
  .milkdown .milkdown-image-block .image-edit .link-importer .link-input-area {
      line-height: 24px;
      color: var(--crepe-color-on-background);
    }
  .milkdown .milkdown-image-block .image-edit .link-importer .placeholder .uploader {
      gap: 8px;
      color: var(--crepe-color-primary);
      justify-content: center;
      transition: color 0.2s;
      font-weight: 600;
    }
  .milkdown .milkdown-image-block .image-edit .link-importer.focus .placeholder .uploader {
      color: unset;
    }
  .milkdown .milkdown-image-block .image-edit .link-importer .placeholder .uploader:hover {
      color: var(--crepe-color-primary);
    }
  .milkdown .milkdown-image-block .image-edit .link-importer .placeholder .text {
      margin-left: 8px;
    }
  .milkdown .milkdown-image-block .image-edit .confirm {
      background: var(--crepe-color-secondary);
      color: var(--crepe-color-on-secondary);
      line-height: 40px;
      padding: 0 24px;
      border-radius: 100px;
      font-size: 14px;
      font-weight: 600;
    }
  .milkdown .milkdown-image-block .image-edit .confirm:hover {
        background:
          linear-gradient(
            0deg,
            rgba(29, 25, 43, 0.08) 0%,
            rgba(29, 25, 43, 0.08) 100%
          ),
          var(--crepe-color-secondary);
      }
.milkdown span[data-type='math_inline'] {
    padding: 0 4px;
    display: inline-block;
    vertical-align: bottom;
    color: var(--crepe-color-primary);
  }

.milkdown .milkdown-latex-inline-edit[data-show='false'] {
      display: none;
    }

.milkdown .milkdown-latex-inline-edit{
    position: absolute;
    background: var(--crepe-color-surface);
    box-shadow: var(--crepe-shadow-1);
    border-radius: 8px;
    padding: 2px 6px 2px 12px;
}

.milkdown .milkdown-latex-inline-edit .container {
      display: flex;
      gap: 6px;
      align-items: flex-start;
    }

.milkdown .milkdown-latex-inline-edit .container button {
        width: 24px;
        height: 24px;
        cursor: pointer;
        border-radius: 8px;
      }

.milkdown .milkdown-latex-inline-edit .container button:hover {
          background: var(--crepe-color-hover);
        }

.milkdown .milkdown-latex-inline-edit .ProseMirror {
      padding: 0;
      min-width: 174px;
      max-width: 294px;
      font-family: var(--crepe-font-code);
    }
.milkdown .milkdown-link-preview {
    position: absolute;
    z-index: 10;
  }
    .milkdown .milkdown-link-preview[data-show='false'] {
      display: none;
    }
    .milkdown .milkdown-link-preview > .link-preview {
      height: 32px;
      display: flex;
      justify-content: center;
      padding: 4px 10px;
      background: var(--crepe-color-surface);
      gap: 10px;
      border-radius: 8px;
      cursor: pointer;
      box-shadow: var(--crepe-shadow-1);
    }
    .milkdown .milkdown-link-preview > .link-preview > .link-display {
        text-decoration: none;
        color: unset;
      }
    .milkdown .milkdown-link-preview > .link-preview > .link-display:hover:before {
        display: block;
      }
    .milkdown .milkdown-link-preview > .link-preview > .link-icon > svg {
          width: 18px;
          height: 18px;
          color: var(--crepe-color-outline);
          fill: var(--crepe-color-outline);
        }
    .milkdown .milkdown-link-preview > .link-preview > .link-icon {
        border-radius: 8px;
        padding: 3px;
        line-height: 24px;
}
    .milkdown .milkdown-link-preview > .link-preview > .link-icon:hover {
          background: var(--crepe-color-hover);
        }
    .milkdown .milkdown-link-preview > .link-preview > .link-display {
        width: 240px;
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 14px;
        white-space: nowrap;
      }
    .milkdown .milkdown-link-preview > .link-preview > .link-display:hover {
          text-decoration: underline;
        }
    .milkdown .milkdown-link-preview > .link-preview > .button > svg {
          width: 18px;
          height: 18px;
          color: var(--crepe-color-outline);
          fill: var(--crepe-color-outline);
        }
    .milkdown .milkdown-link-preview > .link-preview > .button {
        padding: 3px;
        border-radius: 8px;
        line-height: 24px;
}
    .milkdown .milkdown-link-preview > .link-preview > .button:hover {
          background: var(--crepe-color-hover);
        }
  .milkdown .milkdown-link-edit {
    position: absolute;
    z-index: 10;
  }
  .milkdown .milkdown-link-edit[data-show='false'] {
      display: none;
    }
  .milkdown .milkdown-link-edit > .link-edit {
      height: 32px;
      display: flex;
      justify-content: center;
      padding: 4px 10px 4px 20px;
      background: var(--crepe-color-surface);
      gap: 8px;
      border-radius: 8px;
      box-shadow: var(--crepe-shadow-1);
    }
  .milkdown .milkdown-link-edit > .link-edit > .input-area {
        outline: none;
        background: transparent;
        width: 200px;
        font-size: 14px;
        color: var(--crepe-color-on-background);
      }
  .milkdown .milkdown-link-edit > .link-edit > .button > svg {
          width: 18px;
          height: 18px;
          color: var(--crepe-color-outline);
          fill: var(--crepe-color-outline);
        }
  .milkdown .milkdown-link-edit > .link-edit > .button {
        padding: 3px;
        cursor: pointer;
        border-radius: 8px;
        font-size: 12px;
        line-height: 24px;
}
  .milkdown .milkdown-link-edit > .link-edit > .button:hover {
          background: var(--crepe-color-hover);
        }
  .milkdown .milkdown-link-edit > .link-edit > .button.hidden {
          visibility: hidden;
        }
.milkdown .milkdown-list-item-block {
    display: block;
    padding: 0;
  }

    .milkdown .milkdown-list-item-block > .list-item {
      display: flex;
      align-items: flex-start;
    }

    .milkdown .milkdown-list-item-block > .list-item > .children {
      min-width: 0;
      flex: 1;
    }

    .milkdown .milkdown-list-item-block li {
      gap: 10px;
    }

    .milkdown .milkdown-list-item-block li .label-wrapper {
        color: var(--crepe-color-outline);
      }

    .milkdown .milkdown-list-item-block li .label-wrapper svg {
          fill: var(--crepe-color-outline);
        }

    .milkdown .milkdown-list-item-block li .label-wrapper {
        height: 32px;
        width: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
}

    .milkdown .milkdown-list-item-block li .label-wrapper .label {
          height: 32px;
          padding: 4px 0;
          width: 24px;
          text-align: right;
        }

    .milkdown .milkdown-list-item-block li .label-wrapper .checked,
        .milkdown .milkdown-list-item-block li .label-wrapper .unchecked {
          cursor: pointer;
        }

    .milkdown .milkdown-list-item-block li .label-wrapper .readonly {
          cursor: not-allowed;
        }

.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror pre {
  white-space: pre-wrap;
}

.ProseMirror li {
  position: relative;
}

.ProseMirror-hideselection *::selection { background: transparent; }
.ProseMirror-hideselection *::-moz-selection { background: transparent; }
.ProseMirror-hideselection { caret-color: transparent; }

/* See https://github.com/ProseMirror/prosemirror/issues/1421#issuecomment-1759320191 */
.ProseMirror [draggable][contenteditable=false] { user-select: text }

.ProseMirror-selectednode {
  outline: 2px solid #8cf;
}

/* Make sure li selections wrap around markers */

li.ProseMirror-selectednode {
  outline: none;
}

li.ProseMirror-selectednode:after {
  content: "";
  position: absolute;
  left: -32px;
  right: -2px; top: -2px; bottom: -2px;
  border: 2px solid #8cf;
  pointer-events: none;
}

/* Protect against generic img rules */

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
}

.milkdown .crepe-placeholder::before {
    position: absolute;
    color: color-mix(
      in srgb,
      var(--crepe-color-on-background),
      transparent 60%
    );
    pointer-events: none;
    height: 0;
    content: attr(data-placeholder);
  }
.milkdown {
  position: relative;
}

  .milkdown * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  .milkdown button,
  .milkdown input {
    border: none;
    background: none;
    box-shadow: none;
  }

  .milkdown button:focus, .milkdown input:focus {
      outline: none;
    }

  .milkdown :focus-visible {
    outline: none;
  }

  .milkdown {

  font-family: var(--crepe-font-default);
  color: var(--crepe-color-on-background);
  background: var(--crepe-color-background);
}

  .milkdown .milkdown-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .milkdown .ProseMirror-focused {
    outline: none;
  }

  .milkdown .ProseMirror {
    padding: 14px 20px;
  }

  .milkdown .ProseMirror *::-moz-selection {
      background: var(--crepe-color-selected);
    }

  .milkdown .ProseMirror *::selection {
      background: var(--crepe-color-selected);
    }

  .milkdown .ProseMirror li.ProseMirror-selectednode {
      background: var(--crepe-color-selected);
      outline: none;
    }

  .milkdown .ProseMirror li.ProseMirror-selectednode ::-moz-selection {
        background: transparent;
      }

  .milkdown .ProseMirror li.ProseMirror-selectednode ::selection {
        background: transparent;
      }

  .milkdown .ProseMirror li.ProseMirror-selectednode::-moz-selection {
        background: transparent;
      }

  .milkdown .ProseMirror li.ProseMirror-selectednode::selection {
        background: transparent;
      }

  .milkdown .ProseMirror li.ProseMirror-selectednode:after {
      all: unset;
    }

  .milkdown .ProseMirror .ProseMirror-selectednode {
      background: var(--crepe-color-selected);
      outline: none;
      background: color-mix(
        in srgb,
        var(--crepe-color-selected),
        transparent 60%
      );
    }

  .milkdown .ProseMirror .ProseMirror-selectednode ::-moz-selection {
        background: transparent;
      }

  .milkdown .ProseMirror .ProseMirror-selectednode ::selection {
        background: transparent;
      }

  .milkdown .ProseMirror .ProseMirror-selectednode::-moz-selection {
        background: transparent;
      }

  .milkdown .ProseMirror .ProseMirror-selectednode::selection {
        background: transparent;
      }

  .milkdown .ProseMirror[data-dragging='true']::-moz-selection, .milkdown .ProseMirror[data-dragging='true'] *::-moz-selection {
        background: transparent;
      }

  .milkdown .ProseMirror[data-dragging='true'] .ProseMirror-selectednode,
      .milkdown .ProseMirror[data-dragging='true']::selection,
      .milkdown .ProseMirror[data-dragging='true'] *::selection {
        background: transparent;
      }

  .milkdown .ProseMirror[data-dragging='true'] input::-moz-selection {
        background: var(--crepe-color-selected);
      }

  .milkdown .ProseMirror[data-dragging='true'] input::selection {
        background: var(--crepe-color-selected);
      }

  .milkdown .ProseMirror img {
      vertical-align: bottom;
      max-width: 100%;
    }

  .milkdown .ProseMirror img.ProseMirror-selectednode {
        background: none;
        outline: 2px solid var(--crepe-color-primary);
      }

  .milkdown .ProseMirror h1,
    .milkdown .ProseMirror h2,
    .milkdown .ProseMirror h3,
    .milkdown .ProseMirror h4,
    .milkdown .ProseMirror h5,
    .milkdown .ProseMirror h6 {
      font-family: var(--crepe-font-title);
      font-weight: 400;
      padding: 2px 0;
    }

  .milkdown .ProseMirror h1 {
      font-size: 32px;
      line-height: 50px;
      margin-top: 32px;
    }

  .milkdown .ProseMirror h2 {
      /* font-size: 36px;
      line-height: 44px;
      margin-top: 28px; */
      font-size: 22px;
      line-height: 44px;
      margin-top: 3px;
    }

  .milkdown .ProseMirror h3 {
      font-size: 20px;
      line-height: 40px;
      margin-top: 24px;
    }

  .milkdown .ProseMirror h4 {
      font-size: 16px;
      line-height: 36px;
      margin-top: 20px;
    }

  .milkdown .ProseMirror h5 {
      font-size: 14px;
      line-height: 32px;
      margin-top: 16px;
    }

  .milkdown .ProseMirror h6 {
      font-size: 12px;
      font-weight: 700;
      line-height: 28px;
      margin-top: 16px;
    }

  .milkdown .ProseMirror p {
      font-size: 16px;
      line-height: 24px;
      padding: 4px 0;
    }

  .milkdown .ProseMirror code {
      color: var(--crepe-color-inline-code);
      background: color-mix(
        in srgb,
        var(--crepe-color-inline-area),
        transparent 40%
      );
      font-family: var(--crepe-font-code);
      padding: 0 2px;
      border-radius: 4px;
      font-size: 87.5%;
      display: inline-block;
      line-height: 1.4286;
    }

  .milkdown .ProseMirror a {
      color: var(--crepe-color-primary);
      text-decoration: underline;
    }

  .milkdown .ProseMirror pre {
      background: color-mix(
        in srgb,
        var(--crepe-color-inline-area),
        transparent 40%
      );
      padding: 10px;
      border-radius: 4px;
    }

  .milkdown .ProseMirror pre code {
        padding: 0;
        background: transparent;
      }

  .milkdown .ProseMirror blockquote {
      position: relative;
      padding-left: 40px;
      padding-top: 0;
      padding-bottom: 0;
      box-sizing: content-box;
      margin: 4px 0;
    }

  .milkdown .ProseMirror blockquote::before {
        content: '';
        width: 4px;
        left: 0;
        top: 4px;
        bottom: 4px;
        position: absolute;
        background: var(--crepe-color-selected);
        border-radius: 100px;
      }

  .milkdown .ProseMirror blockquote hr {
        margin-bottom: 16px;
      }

  .milkdown .ProseMirror hr {
      border: none;
      background-color: color-mix(
        in srgb,
        var(--crepe-color-outline),
        transparent 80%
      );
      background-clip: content-box;
      padding: 6px 0;
      height: 13px;
      position: relative;
    }

  .milkdown .ProseMirror hr.ProseMirror-selectednode {
        outline: none;
        background-color: color-mix(
          in srgb,
          var(--crepe-color-outline),
          transparent 20%
        );
        background-clip: content-box;
      }

  .milkdown .ProseMirror hr.ProseMirror-selectednode::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          right: 0;
          background-color: color-mix(
            in srgb,
            var(--crepe-color-outline),
            transparent 80%
          );
          pointer-events: none;
        }

  .milkdown .ProseMirror ul,
    .milkdown .ProseMirror ol {
      padding: 0;
    }
.milkdown .milkdown-table-block {
    display: block;
    margin: 4px 0;
  }

.milkdown .milkdown-table-block th,
    .milkdown .milkdown-table-block td {
      border: 1px solid
        color-mix(in srgb, var(--crepe-color-outline), transparent 80%);
      padding: 4px 16px;
    }

.milkdown .milkdown-table-block th .ProseMirror-selectednode, .milkdown .milkdown-table-block td .ProseMirror-selectednode {
        background-color: transparent !important;
      }

.milkdown .milkdown-table-block th:has(.ProseMirror-selectednode), .milkdown .milkdown-table-block td:has(.ProseMirror-selectednode) {
        outline: 1px solid var(--crepe-color-primary);
        outline-offset: -1px;
      }

.milkdown .milkdown-table-block .selectedCell::after {
        background-color: var(--crepe-color-selected);
        opacity: 0.4;
      }

.milkdown .milkdown-table-block .selectedCell ::-moz-selection {
        background: transparent;
      }

.milkdown .milkdown-table-block .selectedCell ::selection {
        background: transparent;
      }

.milkdown .milkdown-table-block .drag-preview {
      background-color: var(--crepe-color-surface);
      opacity: 0.4;
      position: absolute;
      z-index: 100;
      display: flex;
      flex-direction: column;
      outline: 1px solid var(--crepe-color-primary);
      outline-offset: -1px;
    }

.milkdown .milkdown-table-block .drag-preview[data-show='false'] {
        display: none;
      }

.milkdown .milkdown-table-block .drag-preview th:has(.ProseMirror-selectednode), .milkdown .milkdown-table-block .drag-preview td:has(.ProseMirror-selectednode) {
          outline: none;
        }

.milkdown .milkdown-table-block .handle {
      position: absolute;
      font-size: 14px;
      transition: opacity ease-in-out 0.2s;
    }

.milkdown .milkdown-table-block .handle[data-show='false'] {
      opacity: 0;
    }

.milkdown .milkdown-table-block svg {
      fill: var(--crepe-color-outline);
    }

.milkdown .milkdown-table-block .cell-handle {
      z-index: 50;
      left: -999px;
      top: -999px;
      cursor: grab;
      background-color: var(--crepe-color-surface);
      color: var(--crepe-color-outline);
      border-radius: 100px;
      box-shadow: var(--crepe-shadow-1);
      transition: background-color 0.2s ease-in-out;
    }

.milkdown .milkdown-table-block .cell-handle:hover {
        background-color: var(--crepe-color-hover);
      }

.milkdown .milkdown-table-block .cell-handle:has(.button-group:hover) {
        background-color: var(--crepe-color-surface);
      }

.milkdown .milkdown-table-block .cell-handle[data-role='col-drag-handle'] {
        transform: translateY(50%);
        padding: 0 6px;
        width: 28px;
        height: 16px;
      }

.milkdown .milkdown-table-block .cell-handle[data-role='row-drag-handle'] {
        transform: translateX(50%);
        padding: 6px 0;
        width: 16px;
        height: 28px;
      }

.milkdown .milkdown-table-block .cell-handle .button-group {
        position: absolute;
        transform: translateX(-50%);
        left: 50%;
        top: -52px;
        display: flex;
        background-color: var(--crepe-color-surface);
        border-radius: 8px;
        box-shadow: var(--crepe-shadow-1);
      }

.milkdown .milkdown-table-block .cell-handle .button-group::after {
          content: '';
          position: absolute;
          bottom: -8px;
          height: 8px;
          background-color: transparent;
          width: 100%;
        }

.milkdown .milkdown-table-block .cell-handle .button-group[data-show='false'] {
          display: none;
        }

.milkdown .milkdown-table-block .cell-handle .button-group button {
          cursor: pointer;
          margin: 6px;
          padding: 4px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 4px;
        }

.milkdown .milkdown-table-block .cell-handle .button-group button svg {
            width: 24px;
            height: 24px;
          }

.milkdown .milkdown-table-block .cell-handle .button-group button:hover {
            border-radius: 8px;
            background-color: var(--crepe-color-hover);
          }

.milkdown .milkdown-table-block .cell-handle .button-group button:active {
            background: var(--crepe-color-selected);
          }

.milkdown .milkdown-table-block .cell-handle:hover {
        opacity: 1;
      }

.milkdown .milkdown-table-block .line-handle {
      z-index: 20;
      background-color: var(--crepe-color-primary);
    }

.milkdown .milkdown-table-block .line-handle:hover {
        opacity: 1;
      }

.milkdown .milkdown-table-block .line-handle .add-button {
        cursor: pointer;
        background-color: var(--crepe-color-surface);
        color: var(--crepe-color-outline);
        border-radius: 100px;
        box-shadow: var(--crepe-shadow-1);
        transition: background-color 0.2s ease-in-out;
      }

.milkdown .milkdown-table-block .line-handle .add-button svg {
          width: 16px;
          height: 16px;
        }

.milkdown .milkdown-table-block .line-handle .add-button:hover {
          background-color: var(--crepe-color-hover);
        }

.milkdown .milkdown-table-block .line-handle .add-button:active {
          background: var(--crepe-color-selected);
        }

.milkdown .milkdown-table-block .line-handle[data-role='x-line-drag-handle'] {
        height: 1px;
        z-index: 2;
      }

.milkdown .milkdown-table-block .line-handle[data-role='x-line-drag-handle'] .add-button {
          position: absolute;
          transform: translateX(-50%) translateY(-50%);
          padding: 6px 0;
          width: 16px;
          height: 28px;
        }

.milkdown .milkdown-table-block .line-handle[data-role='y-line-drag-handle'] {
        width: 1px;
        z-index: 1;
      }

.milkdown .milkdown-table-block .line-handle[data-role='y-line-drag-handle'] .add-button {
          position: absolute;
          transform: translateY(-50%) translateX(-50%);
          padding: 0 6px;
          width: 28px;
          height: 16px;
        }

.milkdown .milkdown-table-block .line-handle[data-display-type='indicator'] .add-button {
          display: none;
        }

.milkdown .milkdown-table-block.readonly .handle {
      display: none;
    }
.milkdown:has(.milkdown-link-preview[data-show='true']) .milkdown-toolbar,
  .milkdown:has(.milkdown-link-edit[data-show='true']) .milkdown-toolbar {
    display: none;
  }
  .milkdown .milkdown-toolbar[data-show='false'] {
      display: none;
    }
  .milkdown .milkdown-toolbar {
    z-index: 10;
    position: absolute;
    display: flex;
    background: var(--crepe-color-surface);
    box-shadow: var(--crepe-shadow-1);
    border-radius: 8px;
    overflow: hidden;
}
  .milkdown .milkdown-toolbar .divider {
      width: 1px;
      background: color-mix(
        in srgb,
        var(--crepe-color-outline),
        transparent 80%
      );
      height: 24px;
      margin: 10px;
    }
  .milkdown .milkdown-toolbar .toolbar-item {
      width: 32px;
      height: 32px;
      margin: 6px;
      padding: 4px;
      cursor: pointer;
      border-radius: 4px;
    }
  .milkdown .milkdown-toolbar .toolbar-item:hover {
        background: var(--crepe-color-hover);
      }
  .milkdown .milkdown-toolbar .toolbar-item:active {
        background: var(--crepe-color-selected);
      }
  .milkdown .milkdown-toolbar .toolbar-item svg {
        height: 24px;
        width: 24px;
        color: var(--crepe-color-outline);
        fill: var(--crepe-color-outline);
      }
  .milkdown .milkdown-toolbar .toolbar-item.active svg {
          color: var(--crepe-color-primary);
          fill: var(--crepe-color-primary);
        }
