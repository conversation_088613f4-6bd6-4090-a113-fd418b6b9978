import React, { Component, useState } from "react";

import { track } from "@/common/tracks/actions/im";
import { genUrlParams } from "@/common/utils/url";

import "./index.less";

interface WikiItemProps {
    id: string,
    title: string,
    updateUserName: string,
    updateUser: string,
    updateDate: string,
    spaceName: string,
    index: string,
    datasetType: string
}

interface RelatedItemsProps {
    items: WikiItemProps[],
    scene_id: string,
    firstBizScene: string,
    secondBizScene: string
}

const urlParams = genUrlParams() || {};

// 打开详情
const openDetail = (id: string, title: string, datasetType: string, scene_id: string, firstBizScene: string, secondBizScene: string) => {
    // 结果点击事件埋点
    track(63970, {
        result_type: '相关内容',
        id: id,
        title: title,
        scene_id: scene_id,
        biz_scene1: firstBizScene,
        biz_scene2: secondBizScene,
      }, {
        event: 'Custom_Track',
        uicode: 'ai_pm_copilot'
    });

    const url = `https://xiaobei.ke.com/#/detail/${id}?pure=true`;
    if (urlParams.contextId) {
    // 在父窗口打开新页面
    window.parent.postMessage({ "type": "aiResult", "subType":"openKnowledgeDetail", "data":  url }, "*");
    } else {
    // 打开新页面
    window.open(url);
    }
};

// 点击更多
const clickMore = (scene_id: string, firstBizScene: string, secondBizScene: string) => {
    track(63970, {
        result_type: '相关内容点击更多',
        id: "无",
        title: "无",
        scene_id: scene_id,
        biz_scene1: firstBizScene,
        biz_scene2: secondBizScene,
      }, {
        event: 'Custom_Track',
        uicode: 'ai_pm_copilot'
    });
}

// 相关资料
const RelatedItemList = (props: RelatedItemsProps) => {
    const konwledgeList = props.items;
    if (!Array.isArray(konwledgeList) || (Array.isArray(konwledgeList) && !konwledgeList.length)) return null;

    const [isShowMore, setIsShowMore] = useState(true);

    const isShowFold = konwledgeList.length > 3 && isShowMore;
    const rebuildList = isShowFold ? konwledgeList.slice(0, 3) : konwledgeList;

    return (
        <>
        {
            rebuildList.length > 0 &&
            <div className="pt-4"> 
                <h2 className="text-lg font-semibold mb-2">相关内容：</h2>                
                <ul className="list-disc pl-5"> 
                    { rebuildList.map((item, index) => 
                    <li className="cursor-pointer text-indigo-600" key={index} onClick={() => openDetail(item.id, item.title, item.datasetType, props.scene_id, props.firstBizScene, props.secondBizScene)}>{item.title} </li>) }                  
                </ul>
                {
                    isShowFold && (<p className="list-fold" onClick={() => {
                    setIsShowMore(false);
                    clickMore(props.scene_id, props.firstBizScene, props.secondBizScene);
                    }}>查看更多</p>)
                }  
            </div>
        }               
        </>
    );
};

export default RelatedItemList