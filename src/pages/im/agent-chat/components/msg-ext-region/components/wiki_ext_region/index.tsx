
import React, { Component, useState } from "react";

import RelatedUserList from "../related-user-list"
import RelatedWikiList from "../related-wiki-list"

interface WikiItemProps {
    id: string,
    title: string,
    updateUserName: string,
    updateUser: string,
    updateDate: string,
    spaceName: string,
    index: string,
    datasetType: string,
}

interface AuthorAggProps {
    user: string,
    userName: string,
    positionName?: string,
    orgName?: string,
    wikiIds: string[],
    wikiIndexs: string[],
}

interface PropTypes {
    items: WikiItemProps[],
    aggResults: AuthorAggProps[],
    relatedDataItems: WikiItemProps[],
    scene_id: string,
    firstBizScene: string,
    secondBizScene: string
}

function WikiExtRegion(props: PropTypes): JSX.Element {

    return (
      <>
        <div className="wiki-ext-region" >
            {/* <RelatedUserList items={props.aggResults} /> */}
            <RelatedWikiList items={props.items} scene_id={props.scene_id} firstBizScene={props.firstBizScene} secondBizScene={props.secondBizScene} />
            {/* <RelatedWikiDataList items={props.relatedDataItems} /> */}
        </div> 
      </>
    )
}

export default WikiExtRegion