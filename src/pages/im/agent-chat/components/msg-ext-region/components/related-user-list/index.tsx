import React, { Component, useState } from "react";


interface ItemLinkProps {
  wikiIds: string[],
  wikiIndexs: string[],
}

interface RelatedUserItemProps {
  user: string,
  userName: string,
  positionName?: string,
  orgName?: string,
  wikiIds: string[],
  wikiIndexs: string[],
}

interface RelatedUserItemsProps {
  items: RelatedUserItemProps[],
}

const ItemLink = (props: ItemLinkProps) => {
  return (
    <span>                
      { props.wikiIds && props.wikiIds.map((item, index) => (
        <a key={index} 
          target="_blank" 
          href={`https://wiki.lianjia.com/pages/viewpage.action?pageId=${item}`}>
          [{props.wikiIndexs[index + '']}]
        </a>
       ))
      }
    </span>
  );
};

const RelatedUserList = (props: RelatedUserItemsProps) => {
  return (
      <>           
      {
        props.items && props.items.length > 0 &&
        <div className="pt-4"> 
        <h2 className="text-lg font-semibold mb-2">相关作者：</h2>                
        <ul className="list-disc pl-5"> 
          { props.items && props.items.map((item, index) => <li key={index}>{item.userName} ({item.user}-{item.positionName}-{item.orgName})({item.wikiIds.length}篇, <ItemLink wikiIds={item.wikiIds} wikiIndexs={item.wikiIndexs} />)</li>) }                  
        </ul> 
        </div>
      }           
      </>
  );
};

export default RelatedUserList