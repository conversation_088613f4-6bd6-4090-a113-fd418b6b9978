import React, { Component, useState } from "react";
import { track } from "@/common/tracks/actions/im";


interface WikiItemProps {
    id: string,
    title: string,
    updateUserName: string,
    updateUser: string,
    updateDate: string,
    spaceName: string,
    index: string,
}

interface RelatedItemsProps {
    items: WikiItemProps[],
}

const wikiDetail = (id: string, title: string) => {

    // 结果点击事件埋点
    track(63970, {
        result_type: '相关数据',
        id: id,
        title: title
      }, {
        event: 'Custom_Track',
        uicode: 'ai_pm_copilot'
      });

    // 打开新页面
    window.open(`https://wiki.lianjia.com/pages/viewpage.action?pageId=${id}`);
};

// 相关数据
const RelatedWikiDataList = (props: RelatedItemsProps) => {
return (
    <>
    {
        props.items && props.items.length > 0 && 
        <div className="pt-4">   
        <h2 className="text-lg font-semibold mb-2">相关内容：</h2>                
        <ul className="list-disc pl-5"> 
            { props.items.map((item, index) => <li className="cursor-pointer text-indigo-600" key={index} onClick={() => wikiDetail(item.id, item.title)}>{item.title} <span className="float-right"> by {item.spaceName}-{item.updateUserName} {item.updateDate} </span></li>) }                  
        </ul>    
        </div>
    }             
     </>  
);
};

export default RelatedWikiDataList