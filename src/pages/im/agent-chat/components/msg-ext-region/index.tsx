

import React, { useContext } from "react";
import { connect } from "react-redux";

import WikiItems from "./components/wiki_ext_region";

import "./index.less";

interface PropTypes {
  toolName: string,
  data?: object,
  scene_id?: string,
}

const ComponentMap: any = {
  "mix_retrieval": WikiItems, // 综合信息检索列表项
  "knowledge_retrieval": WikiItems, // 知识检索列表项
  "knowledgeRetrieval": WikiItems
};

const mapStateToProps = (state: any) => ({
  userInfo: state.user.userInfo
});

function MsgExtItem(props: PropTypes): JSX.Element {
  if (!props || !props.data) return null;
  const MsgComponent = ComponentMap[props.toolName];

  return (
    <div className={`tool-ext-region`}>
      <MsgComponent
        {... props.data }
        scene_id={props.scene_id}
      />
    </div>
  );
}

export default connect(
  mapStateToProps
)(MsgExtItem);