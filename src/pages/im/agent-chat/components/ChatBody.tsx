import React from "react";

import MsgSection from "./msg-section";
import HistorySection from "./history-section";

function ChatBody(props: any): JSX.Element {
  const { 
    curScene, 
    contextId, 
    setShowInputBox, 
    setDisableInputBox, 
    getfunction, 
    changeStop, 
    isClickStopBtn, 
    enableInputOutputParams,
    history,
    bubbleCode
  } = props;

   
  return (
    <div className="flex flex-col gap-4" >
      <div
        className={`border-[#9999] break-words border-2 rounded-xl self-end  max-w-[80%] ${
          (curScene === 'ai_agent') ? 'agent-chat-body' : 'px-3 py-3'
          }`}
      >
        {
          history && 
          <HistorySection />
        }
        
        
        <MsgSection 
          curScene={curScene} 
          contextId={contextId} 
          setShowInputBox={setShowInputBox} 
          setDisableInputBox={setDisableInputBox} 
          getfunction={getfunction} 
          changeStop={changeStop} 
          isClickStopBtn={isClickStopBtn} 
          enableInputOutputParams={enableInputOutputParams}
          bubbleCode={bubbleCode}
        />
      </div>
      <div id="bottomPlaceholder" />
    </div>
  );
}

export default ChatBody;
