import React, { useState } from 'react';

import './index.less';

const Switch = (props) => {
  const [enabled, setEnabled] = useState(props.enabled || true);

  const toggleSwitch = () => {
    setEnabled(!enabled);
    props.toggleSwitch(!enabled);
  };

  return (
    <div className="flex items-center justify-between w-full p-4">
      <div className="ml-auto flex items-center space-x-4">
        <span className={` mr-2 text-sm ${enabled ? 'text-green-600' : 'text-gray-600'}`}>
          {enabled ? props.enabledText : props.disabledText}
        </span>
        <button
          onClick={toggleSwitch}
          className={`relative items-center h-6 rounded-full w-11 transition-colors focus:outline-none ${
            enabled ? 'bg-blue-600 block' : 'bg-slate-300 inline-flex'
          }`}
        >
          <span
            className={`inline-block w-4 h-4 transform bg-white rounded-full transition-transform ${
              enabled ? 'translate-x-2.5' : 'translate-x-6'
            }`}
          />
        </button>
      </div>
    </div>
  );
};

export default Switch;
