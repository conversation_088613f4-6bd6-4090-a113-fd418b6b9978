import React, {useState, useEffect, useRef, useContext, useCallback } from 'react';
import { ImContext, MsgListContext } from "../context";
import { blankTrim } from "@/common/utils/regexp";
import { Toast } from 'antd-mobile';
import ChatInput from './components/ChatInput';
import ChatBody from './components/ChatBody';
import { genUrlParams } from "@/common/utils/url";
import { randomId } from "@/common/utils"
import { getAgentConfig, sceneExtConfig } from './components/utils/scene-config-utils';
import { findAiAppInfo } from './components/utils/agent-api-utils'
import QuickBubbles from './components/quick-bubble';
import Card from './components/card';
import { DIG_TYPE, EVT_ID, report_dig, UI_CODE } from './components/utils/aigc_dig';

import './pc.less';

// 默认的首屏内容
const DefaultFirstScreenContent = (props) => {
  const { curScene, functions, curFunction } = props;
  const sceneConfig = sceneExtConfig[curScene];
  let desc = "";
  if (sceneConfig && sceneConfig?.welcomeTips) {
    desc = sceneConfig.welcomeTips;
  } else if (functions && curFunction) {
    desc = functions.find(obj => obj.key === curFunction).functionDesc || "";
  }
  return desc ? (
    <>
    {
      curScene !== 'ai_form' && 
      <Card title="欢迎体验小洛~！" subtitle={
        <div dangerouslySetInnerHTML={{ __html: desc }} />
      } />
    }
    </>
  ) : null;
}

const App = () => {
  const { imConfig } = useContext(ImContext);
  const { sendMsg } = useContext(MsgListContext);
  const urlParams = genUrlParams() || {};
  const [curScene ] = useState(urlParams.scene || "ai_app");
  const [contextId, setContextId] = useState(urlParams.contextId || randomId());
  const [showInputBox, setShowInputBox] = useState(true);
  const [disableInputBox, setDisableInputBox] = useState(false);
  const [curFunction, setCurFunction] = useState("");
  const lastFunctionRef = useRef("");
  const [isShowStopBtn, setIsShowStopBtn] = useState(false);
  const [isClickStopBtn, setIsClickStopBtn] = useState(false);
  const [enableInputOutputParams, setEnableInputOutputParams] = useState(true);
  const [sceneFunctions, setSceneFunctions] = useState({});
  const [quickPrompts, setQuickPrompts] = useState([]);
  const [extParams] = useState({});
  const [agentConfig, setAgentConfig] = useState({});
  const defaultPlaceholder = "您可以通过对话调整需求描述";
  const [inputPlaceholder, setInputPlaceholder] = useState(defaultPlaceholder);
  const sceneFunctionsRef = useRef({});
  const bubbleCode = useRef("");
  const [aiAppInfo, setAiAppInfo] = useState({'contextId': contextId, 'status': 'requirement'})

  // 事件处理
  const handleEventResult = (event) => {
    const { type, data, extParams ={} } = event.data;
    switch (type) {
      case 'doSendMsg':
        // 发送消息
        handleSendMsg(data, { actionCode: bubbleCode.current, ...extParams });
        break;
    }
  }

  // 添加监听器
  const addEventListener = () => {
    window.addEventListener("message", handleEventResult);
  }

  useEffect(() => {
    const fetchAgentConfig = async () => {
      const aiConfig = await getAgentConfig(curScene);
      setAgentConfig(aiConfig);
      console.log("agentConfig:", aiConfig);
      setSceneFunctions(aiConfig.functions);
      sceneFunctionsRef.current = aiConfig.functions;
      initFunction();
      changeAgentFunctionInfo(aiConfig.functions, aiConfig, curFunction);

      // 使用智能体头像
      imConfig.baseInfo.robotAvatar = aiConfig.agentAvatar;

    };
    
    fetchAgentConfig();
    addEventListener();
    loadAppInfo();
    return () => {
      window.removeEventListener('message', handleEventResult);
    };
  }, []);

  //查询项目信息
  const loadAppInfo = async () => {
    if(curScene == 'ai_agent_app') {
      const app = await findAiAppInfo(contextId);
      if(app) {
        const appInfo = {...aiAppInfo, ...app}
        setAiAppInfo(appInfo);
      } 
    }
  }

  // 初始化当前默认功能
  const initFunction = () => {
    if (urlParams.projectId && urlParams.projectId !== 'undefined') {
      setCurFunction(urlParams.projectId);
    } else {
      setCurFunction(getfunction());
    }
  }

  // 获取功能下拉框中的默认选项
  const getfunction = useCallback(() => {
    if (lastFunctionRef.current) {
      return lastFunctionRef.current;
    } else if (sceneFunctionsRef.current[curScene] && sceneFunctionsRef.current[curScene].length > 0) {
      return sceneFunctionsRef.current[curScene][0].key;
    }
    return "";
  }, [curScene]);

  // 初始化输入框placeholder等
  const changeAgentFunctionInfo = (agent_functions, agent_config, cur_func) => {
    if (agent_functions[curScene] && agent_functions[curScene].length > 0) {
      if (!cur_func) {
        cur_func = agent_functions[curScene][0].key;
      }
      let curPlaceholder = defaultPlaceholder;
      agent_functions[curScene].forEach((f) => {
        if (f.key === cur_func && f.inputPlaceholder) {
          curPlaceholder = f.inputPlaceholder;
        }
      });
      setInputPlaceholder(curPlaceholder);

      if (agent_config.bubbles) {
        // 切换功能，展示动态气泡
        setQuickPrompts(agent_config.bubbles[cur_func]);
      }
      
    }
  }

  // 切换功能
  const changeFunction = (func) => {
    setCurFunction(func);
    changeAgentFunctionInfo(sceneFunctionsRef.current, agentConfig, func);
    if (curScene !== 'ai_agent') {
      // 切换功能后重置contextId
      setContextId(randomId());
    }
    bubbleCode.current = "";
  }

  // 改变Stop按钮状态
  const changeStop = (status) => {
    setIsShowStopBtn(status);
    if (!status) {
      // 点击了停止按钮
      setIsClickStopBtn(true);
    } else {
      setIsClickStopBtn(false);
    }
  }
  // 切换switch
  const toggleSwitch = (status) => {
    setEnableInputOutputParams(status);
  };

  // 点击快捷指令（气泡）
  const handleQuickPrompt = (item) => {
    // 这里可以直接发送消息或者将prompt填入输入框
    bubbleCode.current = item.bubbleCode;
    const msg = item.content ? item.content : item.bubbleName;
    if (checkBubbleAction(item)) {
      handleSendMsg(msg, { ...extParams, action: item.bubbleAction, code: item.bubbleCode, dataSource: item.dataSource }, () => {});
    }
  };

  // 检查气泡行为
  const checkBubbleAction = (item) => {
    if ("triggerToCanvas" === item.bubbleAction) {
      // 触发画布
      window.parent.postMessage({ type: "showCanvas", data: item.content }, "*");
      return false;
    } else if ("aiRegenerate" === item.bubbleAction) {
      if(curScene == 'ai_agent_app') {
        report_dig(curScene, DIG_TYPE.MODULE_CLICK, {'str1': contextId, 'str2': aiAppInfo.status}, EVT_ID.RE_GENERATE_ONCLICK, UI_CODE.REQUIRE_DESIGN_CODE)
      }
      // 触发重新生成
      window.ImBus.emit("SIMULATE_USER_MSG_SEND", {  sendmsg: false, nextAction: 'aiRegenerate' });
      return false;
    }
    return true;
  }

  // 发送消息
  const handleSendMsg = (msg, extParams?, callback?) => {
    const valueTrim = blankTrim(msg);
    if (!valueTrim) return;
    sendMsg({ msgType: -1, msgPayload: valueTrim, ...extParams, extParams:{ ...extParams } })
      .then(() => {
        if (callback) {
          callback();
        }
      })
      .catch(err => {
        Toast.fail("发送消息失败: " + err?.message || "")
      })
  }

  useEffect(() => {
    lastFunctionRef.current = curFunction;
  }, [curFunction]);

  return (
    <div className="pilot-container">

      {/* 中间区域 */}
      <div className="flex-1 bg-white flex flex-col">

        {/* 聊天区域 */}
        <div className={`h-[90%] overflow-auto w-full w-95 min-w-[20rem] py-8 px-4 self-center
       scrollbar-thumb-slate-400 scrollbar-thin scrollbar-track-gray-transparent scrollbar-thumb-rounded-md `}>
          
          <DefaultFirstScreenContent 
          curScene={curScene} 
          functions={sceneFunctions[curScene]} 
          curFunction={curFunction || getfunction()} 
          />
          <ChatBody 
            curScene={curScene} 
            contextId={contextId} 
            setShowInputBox={setShowInputBox} 
            setDisableInputBox={setDisableInputBox} 
            getfunction={getfunction} 
            changeStop={changeStop} 
            isShowStopBtn={isShowStopBtn} 
            isClickStopBtn={isClickStopBtn} 
            enableInputOutputParams={enableInputOutputParams}
            history={agentConfig['history']}
            bubbleCode={bubbleCode.current}
            aiAppInfo={aiAppInfo}
          />
        </div>
        
        {/* 快捷指令气泡 */}
        <div className="w-full w-90 min-w-[20rem] self-center">
          <QuickBubbles bubbles={quickPrompts} onClick={handleQuickPrompt} />
        </div>
        
        {/* 输入框 */}
        <div className={`w-full w-90 min-w-[20rem] self-center chat-input-container ${
          showInputBox ? '' : ' hidden'
        }`}>
          <ChatInput 
            curScene={curScene} 
            contextId={contextId}
            disableInputBox={disableInputBox} 
            functions={sceneFunctions[curScene]} 
            changeFunction={changeFunction} 
            isShowStopBtn={isShowStopBtn} 
            changeStop={changeStop} 
            curFunction={curFunction || getfunction()} 
            toggleSwitch={toggleSwitch} 
            handleSendMsg={handleSendMsg}
            inputPlaceholder={inputPlaceholder}
            setInputPlaceholder={setInputPlaceholder}
          />
        </div>
      </div>

    </div>
  );
};

export default App;