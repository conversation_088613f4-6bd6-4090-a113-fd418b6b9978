.w-4 {
    width: 1rem;
}
.w-5 {
    width: 1.25rem;
}
.w-64 {
    width: 16rem;
}
.w-8 {
    width: 2rem;
}
.w-16 {
    width: 4rem;
}
.h-5 {
    height: 1.25rem;
}
.h-6 {
    height: 1.5rem;
}
.h-8 {
    height: 2rem;
}
.h-16 {
    height: 4rem;
}
.max-w-4xl {
    max-width: 56rem;
}
.min-w-\[20rem\] {
    min-width: 20rem;
}
.w-full {
    width: 100%;
}
.h-\[90\%\] {
    height: 90%;
}
.max-w-4xl {
    max-width: 56rem;
}
.min-w-\[20rem\] {
    min-width: 20rem;
}
.w-full {
    width: 100%;
}
.w-83 {
    width: 83%;
}
.w-95 {
    width: 95%;
}
.w-90 {
    width: 90%;
}
.w-80 {
    width: 80%;
}
.w-76 {
    width: 76%;
}
.max-h-40 {
    max-height: 10rem;
}
.max-h-28 {
    max-height: 7rem;
}
.max-h-52 {
    max-height: 13rem;
}
.max-h-56 {
    max-height: 14rem;
}
.max-h-60 {
    max-height: 15rem;
}
.max-h-64 {
    max-height: 16rem;
}
.max-h-68 {
    max-height: 17rem;
}
.min-h-\[24px\] {
    min-height: 24px;
}
.min-h-\[6rem\] {
    min-height: 6rem;
}
.h-24 {
    height: 6rem;
}

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}
.flex-1 {
    flex: 1 1 0%;
}
.bg-\[rgb\(246\2c 246\2c 247\)\] {
    --tw-bg-opacity: 1;
    background-color: rgb(246 246 247 / var(--tw-bg-opacity));
}
.bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-stone-50 {
    background-color: rgb(250 250 249);
}
.bg-gray-400 {
    --tw-bg-opacity: 1;
    background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}
.bg-neutral-200 {
    background-color: rgb(229 229 229);
}
.mt-auto {
    margin-top: auto;
}
.ml-1 {
    margin-left: 0.25rem;
}
.ml-2 {
    margin-left: 0.5rem;
}
.mb-2 {
    margin-bottom: 0.5rem;
}
.mb-4 {
    margin-bottom: 1rem;
}
.-mb-8 {
    margin-bottom: -2rem;
}
.-mb-20 {
    margin-bottom: -5rem;
}
.-mb-24 {
    margin-bottom: -6rem;
}
.ml-2\.5 {
    margin-left: 0.625rem;
}
.mr-2 {
    margin-right: 0.5rem;
}

.py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
}
.p-4 {
    padding: 1rem;
}
.p-6 {
    padding: 1.5rem;
}
.p-cmx {
    padding-top: 5rem;
    padding-left: 1rem;
}
.pb-px {
    padding-bottom: 1px;
}
.pb-4 {
    padding-bottom: 1rem;
}
.p-2 {
    padding: 0.5rem;
}
.px-2\.5 {
    padding-left: 0.625rem;
    padding-right: 0.625rem;
}
.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}
.py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
}
.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
}
.p-0\.5 {
    padding: 0.125rem;
}


.left-4 {
    left: 1rem;
}
.top-4 {
    top: 1rem;
}
.fixed {
    position: fixed;
}
.rounded-md {
    border-radius: 0.375rem;
}

.text-slate-500 {
    --tw-text-opacity: 1;
    color: rgb(100 116 139 / var(--tw-text-opacity));
}
.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}
.text-cmx {
    font-size: 1.025rem;
    line-height: 1.25rem;
}

.text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity));
}
.text-indigo-600 {
    color: rgb(79 70 229);
}
.leading-8 {
    line-height: 2rem;
}
.rounded-full {
    border-radius: 9999px;
}


.scrollbar-thumb-slate-400 {
    --scrollbar-thumb: #94a3b8 !important;
}
.scrollbar-thin {
    scrollbar-width: thin;
}
.scrollbar-thin {
    --scrollbar-track: initial;
    --scrollbar-thumb: initial;
    --scrollbar-corner: initial;
    --scrollbar-track-hover: var(--scrollbar-track);
    --scrollbar-thumb-hover: var(--scrollbar-thumb);
    --scrollbar-corner-hover: var(--scrollbar-corner);
    --scrollbar-track-active: var(--scrollbar-track-hover);
    --scrollbar-thumb-active: var(--scrollbar-thumb-hover);
    --scrollbar-corner-active: var(--scrollbar-corner-hover);
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
    overflow: overlay;
}

.overflow-auto {
    overflow: auto;
}
.overflow-scroll {
    overflow: scroll;
}
.self-center {
    align-self: center;
}
.gap-2 {
    gap: 0.5rem;
}
.gap-4 {
    gap: 1rem;
}
.self-center {
    align-self: center;
}


.bg-opacity-10 {
    --tw-bg-opacity: 0.1;
}
.bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.border-slate-300 {
    --tw-border-opacity: 1;
    border-color: rgb(203 213 225 / var(--tw-border-opacity));
}
.border {
    border: 1px solid rgb(203 213 225);
}
.rounded-lg {
    border-radius: 0.5rem;
}
.overflow-auto {
    overflow: auto;
}

.mb-8 {
    margin-bottom: 2rem;
}
.relative {
    position: relative;
}
.outline-none {
    outline: 2px solid transparent;
    outline-offset: 2px;
}
.text-neutral-700 {
    --tw-text-opacity: 1;
    color: rgb(64 64 64 / var(--tw-text-opacity));
}
.font-serif {
    font-family: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
}
.font-sans {
    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

.bg-transparent {
    background-color: transparent;
}
.bg-gray-100 {
    background-color: rgb(243 244 246);
}
.bg-gray-200 {
    background-color: rgb(229 231 235);
}
.bg-sky-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(12 74 110 / var(--tw-bg-opacity));
}
.border-0 {
    border-width: 0px;
}
.w-11 {
    width: 2.75rem;
}
.w-11\/12 {
    width: 91.666667%;
}
textarea {
    // resize: vertical;
    resize: none
}
.ease-in {
    transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.duration-100 {
    transition-duration: 100ms;
}
.right-3 {
    right: 0.75rem;
}
.top-4 {
    top: 1rem;
}
.absolute {
    position: absolute;
}
.p-2 {
    padding: 0.5rem;
}

.left-4 {
    left: 1rem;
}
.top-4 {
    top: 1rem;
}
.right-4 {
    right: 1rem;
}
.right-7 {
    right: 1.75rem;
}
.bottom-5 {
    bottom: 1.25rem; 
}
.fixed {
    position: fixed;
}
.hidden {
    display: none !important;
}
.block {
    display: block !important;
}

.cursor-pointer {
    cursor: pointer;
}
.cursor-copy {
    cursor: copy;
}
.float-right {
    float: right;
}

.float-left {
    float: left;
}

.sky-800 {
    color: #075985
}
.font-bold {
    font-weight: 700;
}
.max-w-sm {
    max-width: 24rem;
}
.rounded {
    border-radius: 0.25rem;
}
.overflow-hidden {
    overflow: hidden;
}
.shadow-lg {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}
.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}
.flex-wrap {
    flex-wrap: wrap;
}
.justify-center {
    justify-content: center;
}
.inline-flex {
    display: inline-flex;
}
.inline-block {
    display: inline-block;
  }
  
.color-red {
    color: #e3342f;
}
.color-orange {
    color: orange;
}

.w-5\/6 {
    width: 83.333333%;
}
.mx-auto { margin-right: auto; margin-left: auto; }
.mt-24 { margin-top: 6rem; }
.mb-20 { margin-bottom: 5rem; }
.overflow-x-scroll {
    overflow-x: scroll;
}
.scrollbar-hide {
    scrollbar-width: none; 
    -ms-overflow-style: none; 
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;
}
.w-72 {
    width: 18rem;
}
.h-4 {
    height: 1rem; 
}
.h-48 {
    height: 12rem;
}
.ml-4 {
    margin-left: 1rem;
}
.rounded-md {
    border-radius: 0.375rem;
}
.bg-blue-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(219 234 254 / var(--tw-bg-opacity));
}
.bg-blue-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(191 219 254 / var(--tw-bg-opacity));
}
.bg-blue-300 {
    --tw-bg-opacity: 1;
    background-color: rgb(66 153 225 / var(--tw-bg-opacity));
}
.bg-blue-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(0 112 243 / var(--tw-bg-opacity));
}
.text-blue-800 {
    --tw-text-opacity: 1;
    color: rgb(0 77 155 / var(--tw-text-opacity));
}
.text-right {
    text-align: right;
}
.text-center {
    text-align: center;
}
.bg-zinc-500	{
    background-color: rgb(113 113 122);
}
.m-1 {
    margin: 0.25rem;
}
.-bottom-0 {
    bottom: 0
}
.-bottom-3 {
    bottom: -0.34rem;
}
.-bottom-3\.5 {
    bottom: -0.875rem;
}
.-bottom-6 {
    bottom: -1.5rem
}

.-bottom-7 {
    bottom: -1.75rem
}

.-bottom-8 {
    bottom: -2rem
}

.-left-3 {
    left: -.75rem
}

.-left-6 {
    left: -1.5rem
}

.-right-0 {
    right: 0
}

.-right-0\.5 {
    right: -.125rem
}

.-right-1 {
    right: -.25rem
}

.-right-1\.5 {
    right: -.375rem
}

.-right-3 {
    right: -.75rem
}

.-right-6 {
    right: -1.5rem
}

.-right-\[0\.4em\] {
    right: -.4em
}

.-top-0 {
    top: 0
}

.-top-0\.5 {
    top: -.125rem
}

.-top-3 {
    top: -.75rem
}

.bottom-0 {
    bottom: 0
}

.bottom-1 {
    bottom: .25rem
}

.bottom-10 {
    bottom: 2.5rem
}

.bottom-2 {
    bottom: .5rem
}

.bottom-4 {
    bottom: 1rem
}

.bottom-\[0\.05em\] {
    bottom: .05em
}

.bottom-\[0\.5px\] {
    bottom: .5px
}

.bottom-full {
    bottom: 100%
}

.left-0 {
    left: 0
}

.left-0\.5 {
    left: .125rem
}

.left-1 {
    left: .25rem
}

.left-1\/2 {
    left: 50%
}

.left-2 {
    left: .5rem
}

.left-3 {
    left: .75rem
}

.left-3\.5 {
    left: .875rem
}

.left-4 {
    left: 1rem
}

.left-\[2\.3rem\] {
    left: 2.3rem
}

.left-full {
    left: 100%
}

.right-0 {
    right: 0
}

.right-1 {
    right: .25rem
}

.right-1\.5 {
    right: .375rem
}

.right-2 {
    right: .5rem
}

.right-3 {
    right: .75rem
}

.right-3\.5 {
    right: .875rem
}

.right-4 {
    right: 1rem
}

.right-9 {
    right: 2.25rem
}

.right-14 {
    right: 3.5rem
}

.right-16 {
    right: 4rem
}

.right-18 {
    right: 4.5rem
}

.right-20 {
    right: 5rem
}

.right-24 {
    right: 6rem
}

.right-full {
    right: 100%
}

.top-0 {
    top: 0
}

.top-1 {
    top: .25rem
}

.top-1\.5 {
    top: .375rem
}

.top-1\/2 {
    top: 50%
}

.top-12 {
    top: 3rem
}

.top-2 {
    top: .5rem
}

.top-2\.5 {
    top: .625rem
}

.top-3 {
    top: .75rem
}

.top-3\.5 {
    top: .875rem
}

.top-4 {
    top: 1rem
}

.top-5 {
    top: 1.25rem
}

.top-8 {
    top: 2rem
}
.top-32 {
    top: 8rem
}

.top-full {
    top: 100%
}

.isolate {
    isolation: isolate
}
@media (min-width: 640px) {
    .sm\:right-2 {
        right: .5rem;
    }
}
.border-0 {
    border-width: 0
}

.border-0\.5 {
    border-width: .5px
}

.border-1\.5 {
    border-width: 1.5px
}

.border-2 {
    border-width: 2px
}
.\!border-border-300 {
    border-color: hsl(var(--border-300)/.25)!important
}
.items-center {
    align-items: center;
}
.translate-y-1\/2 {
    --tw-translate-y: 50%;
}
.bg-bg-000 {
    --tw-bg-opacity: 1;
    background-color: hsl(var(--bg-000) / var(--tw-bg-opacity));
}
.px-2 {
    padding-left: .5rem;
    padding-right: .5rem;
}
.px-3 {
    padding-left: .75rem;
    padding-right: .75rem;
}
.pb-1 {
    padding-bottom: .25rem;
}
.pt-3 {
    padding-top: .75rem;
}
.shadow-sm {
    --tw-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
}
.scale-95 {
    --tw-scale-x: .95;
    --tw-scale-y: .95;
}
.scale-95, .scale-\[0\.975\] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.text-text-400 {
    --tw-text-opacity: 1;
    color: hsl(var(--text-400) / var(--tw-text-opacity));
}
.gap-0\.5 {
    gap: .125rem;
}
.justify-between {
    justify-content: space-between;
}
.items-stretch {
    align-items: stretch;
}
.-mt-2 {
    margin-top: -.5rem;
}
.-mt-4 {
    margin-top: -1rem;
}
.-mt-12 {
    margin-top: -3rem;
}
.-mx-1 {
    margin-left: -.25rem;
    margin-right: -.25rem;
}
.delay-100 {
    transition-delay: .1s;
}
.transition-opacity {
    transition-property: opacity;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    transition-duration: .15s;
}
.text-xs {
    font-size: .75rem;
    line-height: 1rem;
}
.py-0\.5 {
    padding-top: .125rem;
    padding-bottom: .125rem;
}
.py-1 {
    padding-top: .25rem;
    padding-bottom: .25rem;
}
.py-1\.5 {
    padding-top: .375rem;
    padding-bottom: .375rem;
}
.p-1 {
    padding: .25rem;
}
.rounded-md {
    border-radius: .375rem;
}
.gap-1 {
    gap: .25rem;
}
.flex-row {
    flex-direction: row;
}
[role=button], button {
    cursor: pointer;
}
button  {
    -webkit-appearance: button;
    // background-color: transparent;
    background-image: none;
}
button {
    font-family: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    font-size: 100%;
    font-weight: inherit;
    line-height: inherit;
    letter-spacing: inherit;
    color: inherit;
    margin: 0;
    padding: 0;
}


.bg-bg-100 {
    --tw-bg-opacity: 1;
    background-color: hsl(var(--bg-100)/var(--tw-bg-opacity))
}

.bg-bg-200 {
    /* --tw-bg-opacity:1; */
    background-color: hsl(var(--bg-200)/var(--tw-bg-opacity));
}
.opacity-0 {
    opacity: 0;
}
.opacity-100 {
    opacity: 1;
}
.hover\:bg-bg-fff:hover{--tw-bg-opacity:1;background-color:#fff}
.hover\:bg-bg-200:hover{--tw-bg-opacity:1;background-color:#f4f4f5}
.hover\:bg-neutral-200:hover{--tw-bg-opacity:1;background-color:#e5e5e5}
.hover\:opacity-100:hover{opacity:1}
.transition-opacity {
    transition-property: opacity;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    transition-duration: .15s
}

.transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    transition-duration: .15s
}

.delay-100 {
    transition-delay: .1s
}

.space-x-4 { margin-left: 1rem; }
.text-gray-600	{
    color: rgb(75 85 99);
}
.transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-duration: 150ms;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.focus\:outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}
.bg-gray-300 {
    --tw-bg-opacity: 1;
    background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}
.transform {
    transform: none;
}
.translate-x-1 {
    --tw-translate-x: 0.25rem; /* 4px */
    transform: translateX(var(--tw-translate-x));
}
.translate-x-2\.5 {
    --tw-translate-x: 0.625rem; 
    transform: translateX(var(--tw-translate-x));
}
.bg-slate-200 {
    background-color: rgb(226 232 240);
}
.bg-slate-300 {
    background-color: rgb(203 213 225);
}
.object-cover {
    object-fit: cover;
}

.flex {
    display: flex;
}

.min-h-screen {
    min-height: 100vh;
}

.bg-gray-50 {
    background-color: #f9fafb;
}

.text-gray-800 {
    color: #1f2937;
}

.font-sans {
    font-family: sans-serif;
}

.w-56 {
    width: 14rem;
}

.bg-white {
    background-color: #ffffff;
}

.border-r {
    border-right: 1px solid;
}

.border-gray-100 {
    border-color: #f3f4f6;
}

.p-5 {
    padding: 1.25rem;
}

.shadow-sm {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.text-sm {
    font-size: 0.875rem;
}

.font-medium {
    font-weight: 500;
}

.rounded-md {
    border-radius: 0.375rem;
}

.px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

.py-1-5 {
    padding-top: 0.375rem;
    padding-bottom: 0.375rem;
}

.transition-all {
    transition: all 0.2s;
}

.duration-200 {
    transition-duration: 200ms;
}

.bg-gray-100 {
    background-color: #f3f4f6;
}

.text-gray-900 {
    color: #111827;
}

.border {
    border-width: 1px;
}

.border-gray-200 {
    border-color: #e5e7eb;
}

.mr-1-5 {
    margin-right: 0.375rem;
}

.text-gray-500 {
    color: #6b7280;
}
.mt-2 {
    margin-top: 0.5rem;
}

.mt-8 {
    margin-top: 2rem;
}

.text-xs {
    font-size: 0.75rem;
}
.text-2xs {
    font-size: 1.5rem;
}

.uppercase {
    text-transform: uppercase;
}

.tracking-wider {
    letter-spacing: 0.05em;
}

.mb-3 {
    margin-bottom: 0.75rem;
}

.space-y-0-5 > * + * {
    margin-top: 0.125rem;
}

.bg-indigo-50 {
    background-color: #eef2ff;
}

.text-indigo-700 {
    color: #4338ca;
}

.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.border-l-2 {
    border-left-width: 2px;
}

.border-indigo-500 {
    border-color: #6366f1;
}

.hover\:bg-indigo-100:hover {
    background-color: #e0e7ff;
}

.flex-1 {
    flex: 1;
}

.overflow-hidden {
    overflow: hidden;
}

.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, #ffffff, #f9fafb);
}

.p-8 {
    padding: 2rem;
}

.justify-center {
    justify-content: center;
}

.border-dashed {
    border-style: dashed;
}

.animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out;
}

.h-14 {
    height: 3.5rem;
}

.px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
}

.justify-between {
    justify-content: space-between;
}

.gap-2 {
    gap: 0.5rem;
}
.gap-3 {
    gap: .75rem;
}

.w-8 {
    width: 2rem;
}

.h-8 {
    height: 2rem;
}

.text-gray-600 {
    color: #4b5563;
}

.leading-relaxed {
    line-height: 1.625;
}

.list-disc {
    list-style-type: disc;
}

.pl-5 {
    padding-left: 1.25rem;
}

.space-y-1 > * + * {
    margin-top: 0.25rem;
}
  
.space-y-6 > * + * {
    margin-top: 1.5rem;
}


@keyframes fadeIn {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
  
.grid {
    display: grid;
}

.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
}

.gap-6 {
    gap: 1.5rem;
}

.bg-purple-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(168 85 247 / var(--tw-bg-opacity));
}

.hover\:bg-purple-600:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(147 51 234 / var(--tw-bg-opacity));
}

.focus\:outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

.focus\:ring-2:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-purple-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity));
}

.text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity));
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
}

.bg-\[\#1A2D4E\] {
    background-color: #1A2D4E;
}

  