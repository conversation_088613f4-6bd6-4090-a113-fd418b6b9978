/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-06-03 11:08:14
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-16 14:12:25
 */

import React, { useState, useContext, useEffect } from "react";
import { PullToRefresh } from "antd-mobile";

import { track } from "@/common/tracks/actions/im";

import HistorySction from "./components/history-section";
import FunctionSection from "./components/function-section";
import InputSection from "./components/input-section";
import MsgSection from "./components/msg-section";
import QuestionnaireTool from "./components/questionnaire-tool";
import WelcomeSection from "./components/welcome-section";
import SceneModal from "./components/scene-modal";
import { ImContext } from "./context/index";

function Mobile(): JSX.Element {
  const { imConfig, initStatus, urlParams, toolBox, isPc } = useContext(ImContext);
  const [showHistory, setShowHistory] = useState(false);
  const [refreshing, setRefreshing] = useState(true);
  const [hasHistory, setHasHistory] = useState(true);
  const [activeTab, setActiveTab] = useState({});
  const [sceneModalVisible, setSceneModalVisible] = useState(false);
  const [historyDone, setHistoryDone] = useState(false);
  const [loadCount, setLoadCount] = useState(initStatus !== 0 ? 1 : 0);

  const [sceneCard] = (imConfig.extRegionInfos || []).filter((info: any) => info.key === "scene_card");

  useEffect(() => {
    const config = sessionStorage.getItem('tabConfig');
    try {
      if (!config) {
        setSceneModalVisible(true);
      } else {
        setSceneModalVisible(false);
        setActiveTab({ tabKey: JSON.parse(config).toolBizCode, time: Date.now() });
      }
    } catch (error) {
      console.log(error);
    }
  }, [])

  const onRefresh = () => {
    // 这里看着很奇妙，但其实是为了覆盖默认的 refresh 回弹的逻辑
    setRefreshing(true);
    setRefreshing(false);
    setShowHistory(true);
    track(42740);
    loadHistory();
  };

  const loadHistory = () => {
    if (hasHistory) {
      setShowHistory(true);
      setLoadCount((loadCount) => loadCount + 1);
    }
  };

  const setTabConfig = config => {
    if (!config) return;
    // 点击小工具回来之后 定位在之前的tab 且不再弹窗
    sessionStorage.removeItem('tabConfig');
    sessionStorage.setItem('tabConfig', JSON.stringify(config));

    setActiveTab({ tabKey: config.toolBizCode, time: Date.now() });
    setSceneModalVisible(false);
  }

  return (
    <div className="im-wrapper" id="mobileWrapper">
      <PullToRefresh
        damping={80}
        indicator={hasHistory ? { activate: "下拉加载历史记录", finish: "加载成功" } : { activate: "已经到顶了", finish: "已经到顶了" }}
        direction={"down"}
        refreshing={refreshing}
        onRefresh={onRefresh}
        style={{ flex: "1", overflowY: "scroll" }}
      >
        <div className="mobile-content-wrapper">
          <HistorySction visible={showHistory || initStatus !== 0} loadCount={loadCount} setHasHistory={setHasHistory} setHistoryDone={setHistoryDone} />
          {initStatus === 0 && (
            <>
              <WelcomeSection imConfig={imConfig} />
              <FunctionSection
                tab={activeTab}
                toolBox={toolBox}
                extRegionInfos={imConfig.extRegionInfos}
              />
            </>
          )}
          <MsgSection historyDone={historyDone} />
        </div>
        <div id="bottomPlaceholder" />
      </PullToRefresh>
      <InputSection onClickSceneModal={() => { setSceneModalVisible(true) }} />
      <QuestionnaireTool extRegionInfos={imConfig.extRegionInfos || []} spm={urlParams.spm} />
      <SceneModal
        isPc={isPc}
        data={sceneCard}
        visible={sceneModalVisible && initStatus !== 2 && initStatus !== 1}
        onChange={config => { setTabConfig(config) }}
        onClose={() => { setSceneModalVisible(false) }}
      />
    </div>
  )
}

export default Mobile;