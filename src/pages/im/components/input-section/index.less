@import "../../../../styles/variable.less";

.input-section-wrapper {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: fixed;
  bottom: 0;
  z-index: 1;
  align-items: center;
  padding: 0 0 10px;
  padding-bottom: calc(constant(safe-area-inset-bottom) / 2 + 10px);
  padding-bottom: calc(env(safe-area-inset-bottom) / 2 + 10px);
  width: 100%;
  background: #f4f4f4;
  border-top: 1px solid @border-base;

  .input-section {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    margin: 10px 0;
    padding: 0 10px;
    width: 100%;

    .send-btn {
      flex-shrink: 0;
      margin-left: 8px;
      cursor: pointer;
    }

    .voice-bar {
      width: 100%;
      height: 39px;
      line-height: 39px;
      background: #fff;
      border: 1px solid #ccc;
      border-radius: 10px;
      text-align: center;
    }

    .am-list-item {
      padding: 7px 12px;
      flex-grow: 1;
      max-height: 100px !important; // 4行
      min-height: auto;
      border: 1px solid #ccc;
      border-radius: 4px;
      overflow: scroll;
      scrollbar-width: none;
  
      .am-textarea-control {
        padding: 0;
  
        textarea {
          font-size: 14px;
        }
      }
    }
  }
}

.evaluate-modal {
  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 50px;
    border-bottom: 1px solid @border-base;
    flex-direction: row-reverse;
    position: absolute;
    right: 0;
    width: 100%;
    background: #FFF;

    .title {
      font-size: 15px;
      font-weight: 500;
    }

    .btn-close {
      font-size: 14px;
      color: @font-blue;
      cursor: pointer;
    }
  }

  height: 60vh;

  .evaluate-iframe {
    margin-top: 50px;
    width: 100%;
    height: 100%;
  }

  // // pc 兼容
  &.evaluate-pc-modal {
    height: 64vh;
    width: 56vh;
  }
}