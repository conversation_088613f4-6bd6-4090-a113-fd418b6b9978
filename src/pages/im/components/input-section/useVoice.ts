/*
 * @Author: liuzhenkun002
 * @Date: 2021-04-08 16:39:04
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-21 15:41:32
 */

import { useState, useEffect, useContext } from "react";
import { Toast } from "antd-mobile";
import VoiceCommander from "@ke/voice-cmd";
import UaHelper from "@/common/utils/ua";
import { MsgListContext } from "../../context";

import ImApi from "@/common/service/im";

import "./index.less";

// 试图使用 useReducer 的失败案例

let voiceState: any = {
  startScreenY: undefined, // 手指开始位置
  currentScreenY: undefined, // 手指结束位置
  shouldSaveVoice: true, // 能否提交
  recording: false, // 录音中
  uploadUrl: undefined, // 上传录音文件地址
  voiceReady: false // 是否可录音
};

let voiceCmd: any = null; // VoiceCmd 实例
let startDelay: NodeJS.Timeout = null;

function useVoice() {
  const [canUseVoice, setCanUseVoice] = useState(false);
  const { sendMsg } = useContext(MsgListContext);

  // 获取当前支持的业务方 以及 对应的版本号
  const getVoiceConfig = (bridge, webStatus) => {
    const buses = ["isLinkApp", "isDeyou"]; // 支持的端
    let APP_LEVEL = "";
    if (webStatus.isDecorateHome) {
      buses.push("isDecorateHome");
      APP_LEVEL = "2.62.0";
    }
    if (webStatus.isDecorateJingGongB) {
      buses.push("isDecorateJingGongB");
      APP_LEVEL = "2.48.0";
    }
    const voiceConfig = {
      buses,
      limit: 69, // 语音时长为limit-10，60s的音频语音转文本会失效，故设置上限为59s
      bridgeConfig: {
        format: "wav",
        sampleRate: 16000,
        channels: 1 // 2 =>STEREO(立体声),其他=>MONO(单声道)
      },
      APP_LEVEL
    };
    
    voiceConfig.buses = buses;
    return voiceConfig;
  }

  const initVoice = (bridge, webStatus) => {
    const voiceConfig = getVoiceConfig(bridge, webStatus);

    const listeners = {
      onCanUse(resp: any) {
        if (!resp.status) return;
        setCanUseVoice(true);
      },

      onAutoEnd(resp: any) {
        const { status, msg, duration } = resp;
        voiceState.recording = false;

        if (!voiceState.shouldSaveVoice) {
          voiceState.shouldSaveVoice = true;

          return;
        }

        if (!status) {
          Toast.info(msg);
          return;
        }
        
        sendMsg({
          msgType: -3,
          msgPayload: JSON.stringify({ type: "mp3", duration, url: voiceState.uploadUrl })
        });
      }
    };

    voiceCmd = new VoiceCommander(voiceConfig, listeners);
  };

  // 点击发送语音
  const voiceOntouchstart = (event: any) => {
    const newState = {
      startScreenY: event.targetTouches[0].clientY,
      currentScreenY: event.targetTouches[0].clientY,
      recording: true,
      shouldSaveVoice: true,
      voiceReady: false
    };
  
    voiceState = { ...voiceState, ...newState };

    clearTimeout(startDelay);

    // 加点延迟，防止频繁点击
    startDelay = setTimeout(() => {
      voiceOntouchstartHandle();
    }, 0);
  };

  const voiceOntouchstartHandle = () => {
    voiceGetUploadUrl((data: any) => {
      const { uploadPath, filePath } = data;
      voiceState.uploadUrl = filePath;
      voiceCmd.startVoice(uploadPath, (res: any) => {
        voiceState.voiceReady = true;

        // 提前结束录音，则放弃本次录音
        if (!voiceState.recording) {
          setTimeout(() => {
            voiceCmd.cancelVoice(() => {});
          }, 200);
          return;
        }

        // 录音失败提示
        if (!res.status) {
          Toast.info(res.msg);
        }
      });
    });
  };

  // 上滑取消语音
  const voiceOntouchmove = (event: any) => {
    voiceState.currentScreenY = event.targetTouches[0].clientY;
    // 超出一定范围，显示取消录音提示
    if (voiceState.startScreenY - voiceState.currentScreenY > 100) {
      if (voiceState.shouldSaveVoice) {
        voiceState.shouldSaveVoice = false;
        voiceCmd.askCancel();
      }
      return;
    }

    if (!voiceState.shouldSaveVoice) {
      voiceState.shouldSaveVoice = true;
      voiceCmd.waiveCancel();
    }
  };

  // 松手结束/取消语音
  const voiceOntouchend = () => {
    // 防止频繁触发录音
    clearTimeout(startDelay);

    // 如果自动结束或录音未开始，则返回
    if (!voiceState.recording || !voiceState.voiceReady) {
      voiceState.recording = false;
      return;
    }

    voiceState.recording = false;
  
    // 保存或取消录音
    if (voiceState.shouldSaveVoice) {
      voiceCmd.endVoice((data: any) => {
        const { status, msg, duration } = data;
        if (!status) {
          Toast.info(msg);
          return;
        }

        sendMsg({
          msgType: -3,
          msgPayload: JSON.stringify({ type: "mp3", duration, url: voiceState.uploadUrl })
        });
      });
    } else {
      voiceCmd.cancelVoice((res: any) => {
        if (res.status) return;
        Toast.info(res.msg);
      });
    }
  };

  // 获取上传的文件地址
  const voiceGetUploadUrl = (callback: Function) => {
    const data = {
      type: "audio/wav",
      name: `${Date.now()}.wav`
    };

    ImApi.getUploadPath(data)
      .then((res: any) => {
        callback({
          uploadPath: res.uploadPath,
          filePath: res.filePath
        });
      }).catch(() => {
        Toast.info("请检查网络～");
      });
  };

  useEffect(() => {
    $ljBridge.ready(function (bridge, webStatus) {
      initVoice(bridge, webStatus);
    });
  }, []);

  return {
    canUseVoice,
    voiceOntouchstart,
    voiceOntouchmove,
    voiceOntouchend
  };
}

export default useVoice;