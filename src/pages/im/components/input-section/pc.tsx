/*
 * @Author: liuzhenkun002
 * @Date: 2021-06-09 14:54:38
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-16 14:56:01
 */

import React, { useState, useEffect, useRef, useContext } from "react";
import { TextareaItem, Modal } from "antd-mobile";

import ImApi from "@/common/service/im";
import { MsgListContext, ImContext } from "../../context";

import BubbleArea from "./components/bubble-area";
import SugArea from "./components/sug-area";
import ToolArea from "./components/tool-area";

import "./pc.less";

function PcInput(props: any): JSX.Element {
  const {
    sugUrl,
    sugShow,
    openAskModal,
    handleEvaluate,
    inputValue,
    inputFocus,
    inputSend,
    inputChange,
    setInputValue,
    inService,
    handleClickSceneBtn,
    handleCompositionStart,
    handleCompositionEnd
  } = props;
  const [modalShow, setModalShow] = useState(false);
  const [pasteImgList, setPasteImgList] = useState([]);
  const inputRef = useRef();
  const { sendMsg } = useContext(MsgListContext);
  const { disableInput, isChatGPT } = useContext(ImContext);


  const keyPress = (event: any) => {
    if (event.key === "Enter" || event.keyCode === 13) {
      inputSend();
      event.preventDefault();
    }
  };

  const imageSend = () => {
    pasteImgList.forEach(img => {
      const msg = {
        msgType: -2,
        msgPayload: JSON.stringify({ original: img })
      }
      sendMsg(msg);
    });

    setPasteImgList([]);
    setModalShow(false);
  };

  const onPaste = (event: any, preventDefault = false) => {
    if (preventDefault) event.preventDefault();
    if (!inService) return;
    const cbd = event.clipboardData || event.dataTransfer;
    const imgPromises: any[] = [];

    [...cbd.items].forEach((item: any) => {
      const pasteImg = item.getAsFile();

      if (pasteImg && pasteImg !== 0 && (item.kind === "file" || item.type.indexOf("image") !== -1)) {
        imgPromises.push(ImApi.uploadFile(pasteImg));
      }
    });

    Promise.all(imgPromises)
      .then((res: any) => {
        setPasteImgList(res.map((file: any) => file.filePath));
      }).catch(x => x);
  };

  useEffect(() => {
    if (pasteImgList.length) {
      setModalShow(true);
    }
  }, [pasteImgList])

  return (
    <div className="input-section-pc-wrapper">
      <SugArea sugUrl={sugUrl || ""} sugShow={sugShow} clearInput={() => setInputValue("")}/>
      <div className="input-title">
        <BubbleArea isPc sendMsg={sendMsg} openAskModal={openAskModal} handleEvaluate={handleEvaluate} handleClickSceneBtn={handleClickSceneBtn} />
        {inService && <ToolArea isPc={true} sendMsg={sendMsg} hideToolArea={() => {}} />}
      </div>
      <div className="input-section">
        <TextareaItem
          ref={inputRef}
          value={inputValue}
          onFocus={inputFocus}
          onChange={e => inputChange(e)}
          onCompositionStart={handleCompositionStart}
          onCompositionEnd={handleCompositionEnd}
          placeholder={!isChatGPT ? "请描述您的问题" : "请描述您的需求"}
          rows={3}
          onKeyPress={e => keyPress(e)}
          onPaste={e => onPaste(e)}
          onDrop={e => onPaste(e, true)}
        />

        { !isChatGPT && <button className={`btn-primary send-btn ${disableInput ? ' disabled' : ''}`} onClick={!disableInput ? inputSend : null}>发送</button> }
      </div>

      <Modal
        transparent
        visible={modalShow}
        maskClosable={true}
        onClose={() => setModalShow(false)}
        animationType="slide-up"
        footer={[
          { text: "取消", onPress: () => setModalShow(false) },
          { text: "确认", onPress: () => imageSend() }
        ]}
      >
        <p>确定发送图片吗？</p>
      </Modal>
    </div>
  );
};

export default PcInput;