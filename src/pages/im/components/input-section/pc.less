@import "../../../../styles/variable.less";

.input-section-pc-wrapper {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  background: #f4f4f4;
  border-top: 1px solid @border-base;

  .input-section {
    box-sizing: border-box;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    margin: 10px 0;
    padding: 0 10px;
    width: 100%;

    .send-btn {
      padding: 12px 20px;
      flex-shrink: 0;
      margin-left: 10px;
      cursor: pointer;
    }

    .disabled {
      opacity: 0.5; /* 设置透明度为50% */
      cursor: not-allowed; /* 鼠标指针设置为不可用 */
    }
  }

  .input-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .am-list-item {
    padding: 5px 8px;
    flex-grow: 1;
    border: 1px solid #ccc;
    border-radius: 4px;
    overflow: scroll;
    scrollbar-width: none;

    .am-textarea-control {
      padding: 0;

      textarea {
        font-size: 14px;
        line-height: 22px;
      }
    }
  }
}
