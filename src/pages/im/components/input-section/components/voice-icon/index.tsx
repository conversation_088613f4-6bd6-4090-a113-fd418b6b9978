/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-04-13 11:39:51
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-21 16:08:37
 */
import React from "react";
import ImgVoice from "@/common/assets/image/voice.png";

import "./index.less";

interface PropTypes {
  inService: boolean,
  canUseVoice: boolean,
  toggleVoiceShow: any,
  isOpenINTVoice: boolean
}

function VoiceIcon(props: PropTypes):JSX.Element {
  const {canUseVoice, inService, toggleVoiceShow, isOpenINTVoice } = props;
  if (!canUseVoice) return null;
  if (!isOpenINTVoice && !inService) return null;

  return (
    <div className="voice-icon-wrapper" onClick={toggleVoiceShow}>
      <img className="voice-icon" src={ImgVoice} />
    </div>
  );
};

export default VoiceIcon;