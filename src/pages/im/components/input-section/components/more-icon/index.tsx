/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-04-13 11:39:51
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-21 16:09:00
 */
import React from "react";

import ImgMore from "@/common/assets/image/more.png";

import "./index.less";

interface PropTypes {
  inService: boolean,
  toggleToolShow: any
}

function MoreIcon(props: PropTypes):JSX.Element {
  const { inService, toggleToolShow } = props;
  
  if (!inService) return null;
  return (
    <div className="more-icon-wrapper" onClick={toggleToolShow}>
      <img className="more-icon" src={ImgMore} />
    </div>
  );
};

export default MoreIcon;