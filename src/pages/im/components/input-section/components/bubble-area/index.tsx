/*
 * @Author: liuzhenkun002
 * @Date: 2021-04-13 11:47:44
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-09 16:34:29
 */

import React, { useEffect } from "react";
import { connect } from "react-redux";

import { track } from "@/common/tracks/actions/im";
import MsgAttrMap from "@/pages/im/constant/msg-attr-map";

import BubbleType from "../../constant/bubble-type";

import "./index.less";

interface Bubble {
  name: string;
  type?: string;
  key?: string;
  iconUrl?: string;
}

interface PropTypes {
  isPc: boolean;
  bubbleList: Bubble[];
  sendMsg: Function;
  openAskModal: Function;
  handleEvaluate: Function;
  handleClickSceneBtn?: Function;
}

const mapStateToProps = (state: any) => ({
  bubbleList: state.im.bubbleList,
});

function BubbleArea(props: PropTypes): JSX.Element {
  const { isPc, sendMsg, openAskModal, handleEvaluate, bubbleList } = props;

  useEffect(() => {
    // 初始化手机端IM的padding bottom，因为 input 区域的高度不固定。
    if (isPc) return;

    const wrapperDom = document.getElementById("mobileWrapper");
    const inputDom = document.getElementById("mobileInput");

    if (!wrapperDom || !inputDom) return;
  }, []);

  if (!bubbleList.length) return null;
  const bubbleClick = (bubble: Bubble) => {
    track(41820, { shouye_qiuqu_click: bubble.name });

    switch (bubble.type) {
      case BubbleType.TO_END_SESSION:
      case BubbleType.TO_QUIT_QUEUQ:
      case BubbleType.TO_CONTINUE_QUEUQ:
        openAskModal(bubble);
        break;
      case BubbleType.TO_EVALUATE:
        handleEvaluate(bubble);
        break;
      case BubbleType.SEND_QUERY:
        sendMsg({
          msgType: -204,
          msgPayload: bubble.name,
          msgAttr: bubble.key,
        });
        break;
      case BubbleType.PREVIOUS_PAGE:
        props?.handleClickSceneBtn();
        break;
      default:
        sendMsg({
          msgType: -1,
          msgPayload: bubble.name,
          msgAttr: MsgAttrMap.bubble,
        });
        break;
    }
  };

  return (
    <div className="bubble-wrapper">
      {bubbleList.map((bubble) => {
        return (
          <div
            className="bubble"
            onClick={() => bubbleClick(bubble)}
            key={bubble.name}
          >
            {bubble.iconUrl ? <img src={bubble.iconUrl}></img> : null}
            {bubble.name}
          </div>
        );
      })}
    </div>
  );
}

export default connect(mapStateToProps)(BubbleArea);
