/*
 * @Author: liuzhenkun002
 * @Date: 2021-04-12 14:38:46
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-05-10 14:22:57
 */

// 写到这里我已经有点晕了

import React, { Component } from "react";
import onClickOutside from "react-onclickoutside";
import request from "@/common/request";
import { regStrFormat } from "@/common/utils/regexp";
import { track } from "@/common/tracks/actions/im";
import MsgAttrMap from "@/pages/im/constant/msg-attr-map";
import { genUrlParams } from "@/common/utils/url";

import "./index.less";

// TODO：sug 的实现方案有很大的改进空间
// 现在的 sug 有个比较大的缺陷，就是不能保证顺序
// 如果某个回调慢了就会取后面的回调结果
class SugArea extends Component {
  constructor(props) {
    super(props);
    this.state = {
      curValue: "",
      sugList: []
    };
  }

  componentDidMount() {
    ImBus.on("GET_SUG", (value) => this.getSugList(value));
    ImBus.on("CLEAR_SUG", () => this.clearSugList());
  }

  getSugList = (value) => {
    let { sugUrl } = this.props;
    if (!sugUrl) return;
    const { spm } = genUrlParams() || {};
    sugUrl = sugUrl.replace("query=&", "");
    sugUrl = sugUrl.replace("spm=&", "");
    request.get(sugUrl, {
      params: {
        query: value,
        spm
      }})
      .then((data) => {
        if (!data || !data.length) {
          // 需要异步否则没用，如果把 sug 改成按序的就不需要异步了。
          setTimeout(() => this.clearSugList(), 100);
          return;
        }

        const sugList = data.length > 5 ? data.slice(0, 5) : data;
        this.setState({ sugList, curValue: value });
      }).catch(x => x);
  }

  clearSugList() {
    this.setState({ sugList: [] });
  }

  sugSend(sug, index) {
    const { id, title, titleType = "knowledge", query } = sug;
    const { sugList } = this.state;
    const sugClickName = JSON.stringify({ id, title });
    const sugContent = JSON.stringify(sugList);
    const sendTime = Date.now();
    
    if (titleType === "tool") {
      ImBus.emit("MSG_SEND", {
        msgType: -1,
        msgPayload: query,
        sendTime
      });
    } else {
      ImBus.emit("MSG_SEND", {
        msgType: -1,
        msgPayload: title,
        knowledgeId: id,
        msgAttr: MsgAttrMap.sug,
        sendTime
      });
    }
    this.clearSugList();
    this.props.clearInput();
    track(41817, {
      sugtype: titleType,
      sug_click_name: sugClickName,
      sug_queryid: `${id}${sendTime}`, // queryid + 时间戳
      sug_query: title, // sug内容
      sug_list: sugContent, // 列表内容
      location: index, // 点击位置
    });
  }

  sugHeightLight(sug) {
    const { curValue } = this.state;
    const valueFormat = regStrFormat(curValue);
    const title = sug.title;
    const reg = new RegExp(valueFormat, "g");
    return title.replace(reg, `<span class="hight-light">${curValue}</span>`);
  }

  handleClickOutside = () => {    
    if (!this.state.sugList.length) return;
    this.clearSugList();
  };

  renderList() {
    const { sugList } = this.state;
    return (
      <>
        {
          sugList.map((item,index) => {
            const {titleType = "knowledge", id } = item
            return (
              <div
                className="sug-item"
                key={id}
                onClick={() => this.sugSend(item,index)}
              >
                {titleType === "tool" && <span className="sug-item-tag">工具</span>}
                <span dangerouslySetInnerHTML={{ __html: this.sugHeightLight(item) }} />
              </div>
            )}
          )
        }
      </>
    )
  }

  render() {
    if (!this.props.sugShow) return null;
    return (
      <div className="sug-wrapper">
        {this.renderList()}
      </div>
    );
  }
}

export default onClickOutside(SugArea);