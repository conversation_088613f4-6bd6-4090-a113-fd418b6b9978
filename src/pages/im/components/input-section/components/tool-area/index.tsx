/*
 * @Author: liuzhenkun002
 * @Date: 2021-04-13 14:20:06
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-09 16:23:55
 */

import React, { Component } from "react";
import onClickOutside from "react-onclickoutside";

import ImgPhoto from "@/common/assets/image/photo.png";
import { track } from "@/common/tracks/actions/im";

import ImApi from "@/common/service/im";

import "./index.less";

interface PropTypes {
  hideToolArea: Function,
  isPc: boolean,
  sendMsg: (msg: any) => void
}

class ToolArea extends Component<PropTypes, {}> {
  constructor(props: PropTypes) {
    super(props);
  }

  onChange = (event: any) => {
    const files = event.target.files;
    if (!files.length) return;
    const { sendMsg } = this.props;
    const promises = [...files].map((file: any) => {
      return new Promise((resolve) => {
        ImApi.uploadFile(file)
        .then((res: any) => {
          const msg = {
            msgType: -2,
            msgPayload: JSON.stringify({original: res.filePath})
          }
          resolve();
          sendMsg(msg);
        }).catch(x => x);
      });
    });

    Promise.all(promises)
      .then(() => {
        this.handleClickOutside();
      }).catch(x => x);
  };

  photoClick = () => {
    const { imgPicker } = this.refs;
    if (!imgPicker) return;
    imgPicker.click();
    track(41832, { click_result: "image" });
  };

  handleClickOutside = () => {
    this.props.hideToolArea();
  };

  renderPcTools() {
    return (
      <div className="tool-area-pc-wrapper">
        <div className="tool-item image">
          <img className="icon-photo" src={ImgPhoto} onClick={this.photoClick} />
          <input
            ref="imgPicker"
            className="image-picker"
            type="file"
            accept="image/*"
            multiple={true}
            onChange={this.onChange}
          />
        </div>
      </div>
    )
  };

  render() {
    if (this.props.isPc) {
      return this.renderPcTools();
    }

    return (
      <div className="tool-area-wrapper">
        <div className="tool-item image">
          <img className="icon-photo" src={ImgPhoto} onClick={this.photoClick} />
          <input
            ref="imgPicker"
            className="image-picker"
            type="file"
            accept="image/*"
            multiple={true}
            onChange={this.onChange}
          />
          <div className="tool-name">图片</div>
        </div>
      </div>
    );
  }
}

export default onClickOutside(ToolArea);