/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-03-31 15:34:06
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-09 16:15:37
 */
import React, { useContext, useEffect, useState, useRef } from "react";
import { connect } from "react-redux";
import { Modal, TextareaItem } from "antd-mobile";
import { debounce } from "lodash";

import { blankTrim } from "@/common/utils/regexp";
import { track } from "@/common/tracks/actions/im";
import xssFilter from "@/common/utils/xss";
import UaHelper from "@/common/utils/ua";
import {
  CHANGE_INQUEUE_STATUS,
  SET_BUBBLES,
  REMOVE_BUBBLE,
} from "@/common/store";

import { ImContext, MsgListContext } from "../../context";
import InitStatusMap from "../../constant/init-status-map";

import BubbleType from "./constant/bubble-type";
import BubbleArea from "./components/bubble-area";
import SugArea from "./components/sug-area";
import ToolArea from "./components/tool-area";
import MoreIcon from "./components/more-icon";
import VoiceIcon from "./components/voice-icon";
import useVoice from "./useVoice";
import PcInput from "./pc";

import "./index.less";

interface Bubble {
  name: string;
  type?: string;
  linkUrl?: string;
  iconUrl?: string;
}

const mapStateToProps = (state: any) => ({
  inService: state.im.inService,
  inQueue: state.im.inQueue,
});

const mapDispatchToProps = (dispatch: any) => ({
  inQueueChange: (inQueue: boolean) =>
    dispatch({ type: CHANGE_INQUEUE_STATUS, payload: inQueue }),
  setBubbles: (bubbles: any[]) =>
    dispatch({ type: SET_BUBBLES, payload: bubbles }),
  removeBubble: (bubble: any) =>
    dispatch({ type: REMOVE_BUBBLE, payload: bubble }),
});

// state 们这里面能不能用 useReducer 整合？
function InputSection(props: any): JSX.Element {
  const [inputValue, setInputValue] = useState("");
  const [modalShow, setModalShow] = useState(false);
  const [evaluateShow, setEvaluateShow] = useState(false);
  const [isComposing, setComposing] = useState(false); // 是否正在输入法键入中
  const [evaluateUrl, setEvaluateUrl] = useState("");
  const [modalContext, setModalContext] = useState(null);
  const [voiceBarShow, setVoiceBarShow] = useState(false);
  const [toolShow, setToolShow] = useState(false);
  const [sugShow, setSugShow] = useState(true); // 用于处理异步获取 sug，在发送后还展示的问题。
  const [voiceTip, setVoiceTip] = useState("按住说话");
  const { imConfig, initStatus, isPc } = useContext(ImContext);
  const { sendMsg } = useContext(MsgListContext);
  const { canUseVoice, voiceOntouchstart, voiceOntouchmove, voiceOntouchend } =
    useVoice();
  const voiceTouchBar = useRef();
  const { extConfig } = imConfig;
  const { sug, openIntelligentStageVoice} = extConfig || {}; // openIntelligentStageVoice 是否开启智能客服阶段语音
  const { sug_url: sugUrl } = sug || {};
  const isOpenINTVoice = +(openIntelligentStageVoice || {}).status === 1; // 根据接口标识判断智能客服阶段是否开启语音 1: 开启 0: 关闭
  const { onClickSceneModal } = props;

  const initBubble = () => {
    let systermBubble = [];
    if (imConfig && imConfig.extRegionInfos) {
      const { extRegionInfos } = imConfig;
      const bubbleConfig = extRegionInfos.find(
        (config: any) => config.key === "bubble"
      );
      if (bubbleConfig) {
        systermBubble = bubbleConfig.data.list;
      }
    }

    const initBubbleMap = {
      // 挂断
      hangUp: {
        name: "挂断",
        type: BubbleType.TO_END_SESSION,
        iconUrl:
          "https://file.ljcdn.com/psd-sinan-file/prod/appeal_evidence/6221982CF76546E09FA59421BF4713E0/%E7%BB%93%E6%9D%9F.png",
      },
      // 退出排队
      dropOut: {
        name: "退出排队",
        type: BubbleType.TO_QUIT_QUEUQ,
        iconUrl:
          "https://file.ljcdn.com/psd-sinan-file/prod/appeal_evidence/02704BCFAD60460EB6FBC10E38FAE42D/%E9%80%80%E5%87%BA.png",
      },
      // 继续排队
      keepOn: {
        name: "继续排队",
        type: BubbleType.TO_CONTINUE_QUEUQ,
        iconUrl:
          "https://file.ljcdn.com/psd-sinan-file/prod/appeal_evidence/8F0E05F54E494F3E93C96A29A19474CE/%E7%BB%A7%E7%BB%AD%E7%AD%89%E5%BE%85.png",
      },
      // 转人工
      /* manualWork: {
        name: "转人工",
        iconUrl:
          "https://file.ljcdn.com/psd-sinan-file/prod/appeal_evidence/A193BC3C73AD449BBA46BF491EA60A95/%E4%BA%BA%E5%B7%A5%E5%AE%A2%E6%9C%8D.png",
      }, */
    };

    // 刷新需要根据初始状态添加不同的气泡
    // TODO switch 改写，懒了。
    if (initStatus === InitStatusMap.inService) {
      systermBubble.unshift(initBubbleMap.hangUp);
    } else if (initStatus === InitStatusMap.inQueue) {
      systermBubble.unshift(initBubbleMap.dropOut);
    } else if (initStatus === InitStatusMap.continueQueue) {
      systermBubble.unshift(initBubbleMap.dropOut);
      systermBubble.unshift(initBubbleMap.keepOn);
    }

    props.setBubbles(systermBubble);
  };

  const initVoiceEvent = () => {
    if (!voiceTouchBar || !voiceTouchBar.current) return;
    const voiceDom: any = voiceTouchBar.current;

    voiceDom.addEventListener(
      "touchstart",
      (event: any) => {
        setVoiceTip("松开发送");
        event.preventDefault();
        voiceOntouchstart(event);

        // 重置语音发送状态, 用来区分 多端发送语音不重复发送 -228 请求
        if (isOpenINTVoice && !props.inService) {
          sessionStorage.setItem("voiceSendStart", "start");
        }
      },
      { passive: false }
    );

    voiceDom.addEventListener(
      "touchend",
      (event: any) => {
        setVoiceTip("按住说话");
        event.preventDefault();
        voiceOntouchend();
      },
      { passive: false }
    );

    voiceDom.addEventListener(
      "touchcancel",
      (event: any) => {
        setVoiceTip("按住说话");
        event.preventDefault();
        voiceOntouchend();
      },
      { passive: false }
    );

    voiceDom.addEventListener(
      "touchmove",
      (event: any) => {
        event.preventDefault();
        voiceOntouchmove(event);
      },
      { passive: false }
    );
  };

  const openAskModal = (bubble: Bubble) => {
    setModalContext(bubble);
    setModalShow(true);
  };

  const handleEvaluate = (bubble: Bubble) => {
    setEvaluateShow(true);
    setEvaluateUrl(bubble.linkUrl || "");
  };

  const askSure = (bubble: Bubble) => {
    const { name, type } = bubble;
    sendMsg({ msgType: -204, msgPayload: name, msgAttr: type });

    // 移除气泡
    props.removeBubble({ name, type });

    if (type === BubbleType.TO_QUIT_QUEUQ) {
      props.inQueueChange(false);
      props.removeBubble({
        name: "继续排队",
        type: BubbleType.TO_CONTINUE_QUEUQ,
        iconUrl:
          "https://file.ljcdn.com/psd-sinan-file/prod/appeal_evidence/8F0E05F54E494F3E93C96A29A19474CE/%E7%BB%A7%E7%BB%AD%E7%AD%89%E5%BE%85.png",
      });
    }

    setModalShow(false);
  };

  const inputSend = () => {
    const valueTrim = blankTrim(inputValue);
    if (!valueTrim) return;
    // const filterMsg = xssFilter(valueTrim);
    sendMsg({ msgType: -1, msgPayload: valueTrim })
      .then(() => {
        setInputValue("");
        setSugShow(false);
        ImBus.emit("CLEAR_SUG");
        track(41829, { query_content: valueTrim });
      })
      .catch((err) => {
        console.log(err, "发送消息失败");
      });
  };

  const inputChange = (value: string) => {
    setInputValue(value);
    if (isComposing) return;
    getSug(value);
  };

  const inputFocus = () => {
    ImBus.emit("CLEAR_SUG");
    setSugShow(true);
  };

  const getSug = debounce((value: string) => {
    const { inService, inQueue } = props;

    // 非排队中 && 非进线中才允许 sug
    if (inQueue || inService) return;

    ImBus.emit("GET_SUG", value);
    setSugShow(true);
  }, 200);

  const toggleVoiceShow = () => {
    setVoiceBarShow((prev) => !prev);
  };

  const toggleToolShow = () => {
    setVoiceBarShow(false);
    setToolShow((prev) => !prev);
    track(41831);
  };

  const handleCompositionStart = () => {
    setComposing(true);
  };

  const handleCompositionEnd = (e) => {
    setComposing(false);
    // 部分浏览器（safari/chrome...）的执行顺序为：compositionstart -> onChange -> compositionend
    // 其他compositionstart -> compositionend -> onChange，因此在end的时候执行一下sug操作
    getSug(e.target?.value);
  };

  const renderMobileInput = () => {
    return (
      <div className="input-section-wrapper" id="mobileInput">
        <SugArea
          sugUrl={sugUrl || ""}
          sugShow={sugShow}
          clearInput={() => setInputValue("")}
        />
        <BubbleArea
          isPc={false}
          sendMsg={sendMsg}
          openAskModal={openAskModal}
          handleEvaluate={handleEvaluate}
          handleClickSceneBtn={onClickSceneModal}
        />
        <div className="input-section">
          <VoiceIcon
            isOpenINTVoice={isOpenINTVoice}
            canUseVoice={canUseVoice}
            inService={props.inService} // 智能客服阶段不需要判断是否在服务中，一开始就有语音条
            toggleVoiceShow={toggleVoiceShow}
          />
          {voiceBarShow ? (
            <div className="voice-bar" ref={voiceTouchBar}>
              {voiceTip}
            </div>
          ) : (
            <TextareaItem
              value={inputValue}
              onFocus={inputFocus}
              onChange={(e) => inputChange(e)}
              onCompositionStart={handleCompositionStart}
              onCompositionEnd={handleCompositionEnd}
              placeholder="请简短描述您的问题"
              autoHeight
            />
          )}
          <MoreIcon
            inService={props.inService}
            toggleToolShow={toggleToolShow}
          />
          <button className="btn-primary send-btn" onClick={inputSend}>
            发送
          </button>
        </div>
        {toolShow && (
          <ToolArea hideToolArea={() => setToolShow(false)} sendMsg={sendMsg} />
        )}
      </div>
    );
  };

  // 如果后续pc mobile 分别的逻辑比较多，mobile 也拆出去
  // 公共逻辑保留，单独逻辑拆分（比如 voice 相关的）
  const renderPcInput = () => {
    return (
      <PcInput
        sugUrl={sugUrl}
        sugShow={sugShow}
        sendMsg={sendMsg}
        openAskModal={openAskModal}
        handleEvaluate={handleEvaluate}
        inputValue={inputValue}
        inputFocus={inputFocus}
        inputSend={inputSend}
        inputChange={inputChange}
        setInputValue={setInputValue}
        handleCompositionStart={handleCompositionStart}
        handleCompositionEnd={handleCompositionEnd}
        inService={props.inService}
        handleClickSceneBtn={onClickSceneModal}
      />
    );
  };

  useEffect(() => {
    initBubble();
  }, []);

  useEffect(() => {
    initVoiceEvent();
  }, [voiceBarShow]);

  return (
    <>
      {isPc ? renderPcInput() : renderMobileInput()}
      <Modal
        transparent
        visible={modalShow}
        maskClosable={true}
        onClose={() => setModalShow(false)}
        animationType="slide-up"
        footer={[
          { text: "取消", onPress: () => setModalShow(false) },
          { text: "确认", onPress: () => askSure(modalContext) },
        ]}
      >
        <p>{modalContext ? `确定${modalContext.name}?` : ""}</p>
      </Modal>
      <Modal
        className={`evaluate-modal ${isPc ? "evaluate-pc-modal" : ""}`}
        transparent={isPc}
        popup={!isPc}
        visible={evaluateShow}
        maskClosable={isPc}
        onClose={() => setEvaluateShow(false)}
        animationType="slide-up"
      >
        <div className="modal-header">
          <div className="btn-close" onClick={() => setEvaluateShow(false)}>
            关闭
          </div>
        </div>
        <iframe className="evaluate-iframe" frameBorder="0" src={evaluateUrl} />
      </Modal>
    </>
  );
}

export default connect(mapStateToProps, mapDispatchToProps)(InputSection);
