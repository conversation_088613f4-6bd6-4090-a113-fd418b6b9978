/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-04-20 14:53:33
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-20 15:02:25
 */

import React, { useEffect } from "react";

import { track } from "@/common/tracks/actions/im";

import MsgItem from "../msg-item";

function WelcomeSection(props: {imConfig: any}): JSX.Element {
  if (!props.imConfig) return null;

  const { baseInfo: { welcomeMsg } } = props.imConfig;
  const msg = { direction: 2, type: "-1", data: welcomeMsg };

  useEffect(() => {
    track(41807, { module_type: "welcome_msg" } );
  }, []);

  return <MsgItem domId="welcomeDom" {...msg} />
};

export default WelcomeSection;
