/*
 * @Author: liuzhenkun002
 * @Date: 2021-03-25 11:16:20
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-03 16:28:26
 */

import React, { useEffect, useState, useContext } from "react";
import { connect } from "react-redux";
import moment from "moment";
import { Toast } from "antd-mobile";
import { randomId } from "@/common/utils";

import ImApi from "@/common/service/im";
import {
  CHANGE_SERVICE_STATUS,
  REVERT_MSG,
  ADD_NEW_MSGS,
  ADD_BUBBLE,
  REMOVE_BUBBLE,
  CHANGE_INQUEUE_STATUS,
  UPDATE_MSG_MAP,
} from "@/common/store";
import initStatusMap from "@/pages/im/constant/init-status-map";
import { ImContext, MsgListContext } from "../../context";
import MsgItem from "../msg-item";
import SkillModal from "./components/skill-modal";
import KnowledgeModal from "./components/knowledge-modal";
import { escape } from "lodash";

interface Data {
  spm: string;
  fromUcid: string;
  msgIdSequence?: string;
  clientId: string;
}
const loopInterval = 1000; // 轮询间隔
const sequences: string[] = [];
let timer: NodeJS.Timeout = null;
let timeout: NodeJS.Timeout = null;
let pollingTimer: NodeJS.Timeout = null; // 判断模块是否被遮挡的定时器
let triggleTimeout: NodeJS.Timeout = null;
let latestMsgId: string = "";
let pageVisible: boolean = true;
let guardTimer: NodeJS.Timeout = null; // 31秒循环监测 eventSource 是否连接正常

const mapStateToProps = (state: any) => ({
  msgList: state.im.msgList,
  userInfo: state.user.userInfo,
});

const mapDispatchToProps = (dispatch: any) => ({
  inServiceChange: (inService: boolean) =>
    dispatch({ type: CHANGE_SERVICE_STATUS, payload: inService }),
  inQueueChange: (inQueue: boolean) =>
    dispatch({ type: CHANGE_INQUEUE_STATUS, payload: inQueue }),
  revertMsg: (msg: any[]) => dispatch({ type: REVERT_MSG, payload: msg }),
  updateMsgMap: (list: any[]) =>
    dispatch({ type: UPDATE_MSG_MAP, payload: list }),
  addNewMsgs: (msgList: any[]) =>
    dispatch({ type: ADD_NEW_MSGS, payload: msgList }),
  addBubble: (bubble: any) => dispatch({ type: ADD_BUBBLE, payload: bubble }),
  removeBubble: (bubble: any) =>
    dispatch({ type: REMOVE_BUBBLE, payload: bubble }),
});

function MsgSection(props: any): JSX.Element {
  const [skillList, setSkillList] = useState([]);
  const [skillModalVisible, setSkillModalVisible] = useState(false);
  const [knowModalVisible, setknowModalVisible] = useState(false);
  const [knowModalTitle, setknowModalTitle] = useState("");
  const [knowledgeUrl, setKnowledgeUrl] = useState("");
  const [modelQueue, setModelQueue] = useState([]); // 大模型队列仅允许两个连接同时存在
  const { imConfig, urlParams, isPc, initStatus } = useContext(ImContext);
  let subscribeList = []
  let subscribeLenGroup = []; // 用来记录每次31秒循环时订阅的消息数量,如果一直数量不变，说明连接失败了

  // 判断DOM元素的可见状态--
  const getDOMVisible = (element, targetIdName) => {
    if (!element) return null;

    let { x, y, width, height } = element.getBoundingClientRect();
    x |= 0;
    y |= 0;
    width |= 0;
    height |= 0;

    let elements = [
      document.elementFromPoint(x, y),
      document.elementFromPoint(x + width, y),
      document.elementFromPoint(x + 10, y + height - 2),
      document.elementFromPoint(x + width - 10, y + height),
    ];

    let isOverlaied = elements
      .filter((el) => !!el)
      .some((el) => {
        return el.id === targetIdName;
      });

    return isOverlaied;
  };

  // 判断最后一条消息是否显示在页面上层（Z轴的可见）
  const getLatestMsgDOMVisible = () => {
    const lastMsgIdName = `msg-${latestMsgId}`;
    let latestMsgDOM = null;
    if (latestMsgId) {
      latestMsgDOM = document.getElementById(lastMsgIdName);
    }

    return getDOMVisible(latestMsgDOM, lastMsgIdName);
  };

  // 判断DOM未被遮挡时，发送已读标识
  const triggleSendReadState = () => {
    let latestMsgDOMVisible = getLatestMsgDOMVisible();
    if (pollingTimer) {
      clearInterval(pollingTimer);
    }

    if (pageVisible) {
      if (latestMsgDOMVisible) {
        sendMsgReadState(latestMsgId);
      } else if (latestMsgDOMVisible !== null) {
        // 轮询判断模块是否可见
        pollingTimer = setInterval(() => {
          latestMsgDOMVisible = getLatestMsgDOMVisible();
          if (latestMsgDOMVisible) {
            sendMsgReadState(latestMsgId);
            // 结束轮询
            clearInterval(pollingTimer);
          }
        }, 1000);
      }
    }
  };

  // 滚到底部
  const scrollToBottom = (notNeedRead = undefined) => {
    const bottomDom = document.getElementById("bottomPlaceholder");
    if (!bottomDom) return;
    timeout = setTimeout(() => {
      bottomDom.scrollIntoView(true);
      // 触发已读的判断

      if (!notNeedRead) {
        triggleTimeout = setTimeout(() => {
          triggleSendReadState();
        }, 1000);
      }
      clearTimeout(timeout);
    }, 0);
  };

  const addNewMsgs = (addList: any[]) => {
    props.addNewMsgs(addList);
    props.updateMsgMap(addList);
    const newMsgListLength = addList && addList.length;
    latestMsgId =
      addList &&
      addList[newMsgListLength - 1] &&
      addList[newMsgListLength - 1].msgId;

    scrollToBottom();
  };

  const addSingleMsg = (
    msgType: number,
    msgPayload: any,
    direction?: number,
    msgId?: string
  ) => {
    let data = msgPayload;
    if (typeof msgPayload === "string") {
      try {
        if (typeof JSON.parse(msgPayload) === "object") {
          data = JSON.parse(msgPayload);
        }
      } catch (err) {
        // ignore
      }
    }
    const msg = {
      id: msgId,
      direction: direction === undefined ? 1 : direction,
      type: msgType,
      data,
      manual: false, // 为了direction不为1时头像为机器头像
    };
    if (msg.type == -1) {
      msg.data = escape(msg.data);
    }
    addNewMsgs([msg]);
  };

  const sendMsg = (msg: any) => {
    const data = {
      spm: urlParams.spm,
      fromUcid: props.userInfo.ucId,
      msgType: -1,
      msgPayload: "",
      sendTime: Date.now(),
      channel: imConfig.bizInfo.bizChannel,
      msgId: randomId(),
      clientId: imConfig.baseInfo.clientId,
      ...msg,
    };
    const { msgType, msgPayload, msgId } = data;

    addSingleMsg(msgType, msgPayload, undefined, msgId);

    window.dt?.sendInfo?.("发送消息4", {
      ...data,
    });
    ImApi.sendMsg(data).catch((x) => x);
  };

  const showSkillModal = (list: any[]) => {
    // 处理单一技能组选择否时，弹出技能组选择 modal 的情况
    if (list && list.length) {
      setSkillList(list);
      setSkillModalVisible(true);
      return;
    }

    const data = {
      spm: urlParams.spm,
      fromUcid: props.userInfo.ucId,
      channel: imConfig.bizInfo.bizChannel,
    };

    ImApi.getSkillInfo(data)
      .then((data: any) => {
        if (!data) return;

        // 返回多个技能组
        if (Array.isArray(data)) {
          setSkillList(data);
          setSkillModalVisible(true);
          return;
        }

        // 只有一个技能组则会变成一条意图消息
        addNewMsgs([{ type: "fe02", data, id: Math.random(), manual: false }]);
      })
      .catch((x) => x);
  };
  // 新消息处理中心
  const newMsgsHandle = (data: any) => {
    const newList: any[] = [];

    data.forEach((newMsg: any) => {
      //判断重复消息
      if (sequences.includes(newMsg.msgIdSequence)) return;
      sequences.push(newMsg.msgIdSequence);
      // 处理撤回消息
      if (newMsg.revocation === 1) {
        props.revertMsg(newMsg);
        props.updateMsgMap([newMsg]);
      } else {
        switch (newMsg.type) {
          case "-204": {
            // 处理气泡消息
            const {
              name,
              type,
              show,
              linkUrl = "",
              iconUrl = "",
            } = newMsg.data;
            if (type === "TO_END_SESSION" && !show) {
              props.inServiceChange(false);
            } else if (type === "TO_QUIT_QUEUQ") {
              // 退出排队出现表示排队中，消失表示不是排队中了。
              props.inQueueChange(show);
            }

            const bubbleAction = show ? props.addBubble : props.removeBubble;
            bubbleAction({ name, type, linkUrl, iconUrl });
            break;
          }
          case "-207":
            // 进线提醒
            props.inServiceChange(true);
            newList.push(newMsg);
            break;
          case "-219":
            // 大模型消息队列入队
            setModelQueue((modelQueue) => [...modelQueue, newMsg.msgId]);
            newList.push(newMsg);
            break;
          case "-221":
            // 匹配大模型消息 点赞效果展示
            ImBus.emit("LIKE_MSG_SEND", newMsg.data);
            break;
          default:
            newList.push(newMsg);
            break;
        }
      }
    });
    if (newList.length) {
      addNewMsgs(newList);
    }
  };

  const fetchReceive = () => {
    const data: Data = {
      spm: urlParams.spm,
      fromUcid: props.userInfo.ucId,
      clientId: imConfig.baseInfo.clientId,
    };
    if (sequences && sequences.length) {
      data.msgIdSequence = sequences[sequences.length - 1];
    }
    window.dt?.sendInfo?.("接受新消息3-拉取新消息", {
      data,
      stage: "fetch msg"
    })
    ImApi.receiveMsg(data)
      .then((res: any) => {
        window.dt?.sendInfo?.("接受新消息4-返回新消息", {
          res,
          stage: "fetch msg success"
        })
        if (!res || !res.length) return;
        newMsgsHandle(res);
      })
      .catch(x => {
        window.dt?.sendInfo?.("接受新消息5-返回新消息失败", {
          stage: "fetch msg error"
        })
        console.log(x)
      });
  }

  /**
   * 每秒都接收订阅，太频繁，因此批量20条上报一次
   * @param hasNewMsg 
   */
  const reportSubscribe = (hasNewMsg: boolean) => {
    subscribeList.push({
      hasNewMsg,
      time: moment(new Date().getTime()).format("YYYY-MM-DD HH:mm:ss.SSS")
    })

    if(subscribeList.length >= 20) {
      window.dt?.sendInfo?.("接受新消息6-批量上报接收状态", {
        subscribeList,
        stage: "pathSubscribe"
      })
      subscribeList = [];
      // subscribeLenGroup = [];
    }
  }

  const changeShortPolling = (evtSource, src) => {
    if (!evtSource) return;
    evtSource.close();
    clearTimer();
    timer = setInterval(fetchReceive, loopInterval);

    window.dt?.sendInfo?.("接受新消息10-连接断开原因", {
      stage: src
    });
  }

  /**
   * 根据subscribeUrl的有无来选择请求模式，SSE or 短轮询
   */
  const fetchMsg = () => {
    const { subscribeUrl } = imConfig;
     // 用来设置链接是否成功的标识，30s内 如果 subscribeList.length <= 20, 说明因为某种原因 连接失败了,这时断开连接，改为短链
    let isConnSucceed = undefined;

    if (guardTimer) {
      clearInterval(guardTimer);
    }

    if (subscribeUrl) {
      const evtSource = new EventSource(subscribeUrl, {
        withCredentials: true,
      });
      evtSource.onmessage = (e) => {
        const data = JSON.parse(e.data);
        reportSubscribe(data.hasNewMsg);
        if (data.hasNewMsg === true) {
          window.dt?.sendInfo?.("接受新消息1-有新消息", {
            data,
            stage: "subscribe"
          })
          fetchReceive();
        }
      };
      evtSource.onerror = (e) => {
        window.dt?.sendInfo?.("接受新消息2-连接断开", {
          stage: "SSE error"
        })
        Toast.info("连接断开，请检查网络～");
        console.error(e);
        isConnSucceed = false;
      };
      // 一些意外情况 导致 31s内如果没有收到消息，说明连接失败，关闭连接，改为循环拉取
      if (isConnSucceed === undefined) {
        guardTimer = setInterval(() => {
          const sendMsgLen = subscribeList.length;
          // 如果31s内没有收到新的消息，说明连接失败了
          if (sendMsgLen === subscribeLenGroup[subscribeLenGroup.length - 1] && sendMsgLen === subscribeLenGroup[subscribeLenGroup.length - 2]) {
              window.dt?.sendInfo?.("接受新消息9-连接仅订阅部分后被断开", {
                stage: subscribeList
              });

              changeShortPolling(evtSource, "接受新消息9");
          }
          if (sendMsgLen > 0 && sendMsgLen < 20 ) {
            window.dt?.sendInfo?.("接受新消息7-连接正常订阅", {
              stage: subscribeList
            });
            if (subscribeLenGroup.length === 10) { subscribeLenGroup = []; }
            subscribeLenGroup.push(sendMsgLen);
          } else {
            window.dt?.sendInfo?.("接受新消息8-连接不正常断开", {
              stage: subscribeList
            });
            changeShortPolling(evtSource, "接受新消息8");
          }

          console.log(subscribeLenGroup);
        }, 31 * 1000);
      }
    } else {
      timer = setInterval(fetchReceive, loopInterval);
    }
  };

  const clearTimer = () => {
    if (timer) {
      clearInterval(timer);
    }

    if (timeout) {
      clearTimeout(timeout);
    }

    if (triggleTimeout) {
      clearTimeout(triggleTimeout);
      triggleTimeout = null;
    }

    if (guardTimer) {
      clearInterval(guardTimer);
    }

    subscribeLenGroup = [];
  };

  const initEvent = () => {
    ImBus.on("MSG_SEND", (data: any) => sendMsg(data));
    ImBus.on("SHOW_SKILL_MODAL", (list: any[]) => showSkillModal(list));
    ImBus.on("SHOW_KONWLEDGE_MODAL", (payload: any) => {
      // 有 url 则直接跳 url，无则用 id 拼
      const { url, id, title } = payload;

      let jumpUrl = url;

      if (!jumpUrl) {
        jumpUrl = imConfig.baseInfo.knowDetailPageUrl;
        if (jumpUrl) {
          jumpUrl = jumpUrl.replace("%s", id);
        }
      }

      if (!jumpUrl) return;

      setKnowledgeUrl(jumpUrl);
      setknowModalVisible(true);
      if (title) setknowModalTitle(title);
    });
    ImBus.on("SCROLL_TO_BOTTOM", (isNeedRead) => scrollToBottom(isNeedRead));

    ImBus.on("DEL_MODEL_QUEQUE", (param) => {
      const { id, queue } = param || {};
      const _queue = JSON.parse(JSON.stringify(queue));
      const index = _queue.indexOf(id);

      if (index === -1) return;
      _queue.splice(index, 1);

      setModelQueue(_queue);
    });

    ImBus.on("RETRY_MODEL_QUEQUE", (param) => {
      const { id, queue = [] } = param || {};
      const _queue = JSON.parse(JSON.stringify(queue));
      if (_queue.includes(id)) return;
      if (!_queue.length) {
        _queue.push(id);
      } else {
        _queue.splice(2, 0, id);
      }
      setModelQueue(_queue);
    });

    // 监听当前页面是否为可见状态
    document.addEventListener("visibilitychange", () => {
      if (document.visibilityState === "visible") {
        pageVisible = true;
        clearInterval(pollingTimer);
        triggleSendReadState();
      } else if (document.visibilityState === "hidden") {
        pageVisible = false;
      }
    });

    sendQuery();
  };

  const sendQuery = () => {
    let { query } = urlParams;
    if (!query || initStatus === initStatusMap.inService) return;
    try {
      query = decodeURIComponent(query);
    } catch (err) {
      console.log(err);
    }
    const msg = {
      msgType: -1,
      msgPayload: query,
    };
    ImBus.emit("MSG_SEND", msg);
  };

  // 发送最新消息id，标记已读状态
  const sendMsgReadState = (msgId: any) => {
    const data = {
      msgId,
      spm: urlParams.spm,
      fromUcid: props.userInfo.ucId,
      msgType: 101,
      sendTime: Date.now(),
      channel: imConfig.bizInfo.bizChannel,
      clientId: imConfig.baseInfo.clientId,
    };

    ImApi.sendMsg(data).catch((x) => {
      sendMsgReadState(msgId);
    });
  };

  useEffect(() => {
    initEvent();
  }, []);

  useEffect(() => {
    if (!props.historyDone) return;
    fetchMsg();
    return clearTimer;
  }, [props.historyDone]);

  return (
    <MsgListContext.Provider
      value={{
        addNewMsgs,
        addSingleMsg,
        sendMsg,
        showSkillModal,
      }}
    >
      {props.msgList.map((msg: any) => (
        <MsgItem
          key={`${msg.id}${msg.revocation}`}
          {...msg}
          msgId={msg.msgIdSequence}
          modelQueue={modelQueue}
          scrollToBottom={scrollToBottom}
        />
      ))}
      <SkillModal
        isPc={isPc}
        modalVisible={skillModalVisible}
        skillList={skillList}
        setModalVisible={setSkillModalVisible}
      />

      <KnowledgeModal
        isPc={isPc}
        modalVisible={knowModalVisible}
        knowModalTitle={knowModalTitle}
        url={knowledgeUrl}
        setModalVisible={setknowModalVisible}
      />
    </MsgListContext.Provider>
  );
}

export default connect(mapStateToProps, mapDispatchToProps)(MsgSection);
