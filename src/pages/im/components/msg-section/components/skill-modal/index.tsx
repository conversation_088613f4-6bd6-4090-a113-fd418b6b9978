/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-04-14 12:06:08
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-03 15:26:08
 */
import React from "react";
import { Modal } from "antd-mobile";

import SkillList from "../skill-list";

import "./index.less";

interface PropTypes {
  isPc: boolean,
  modalVisible: boolean,
  setModalVisible: Function,
  skillList: any[]
}

function SkillModal(props: PropTypes): JSX.Element {
  if (props.isPc) {
    return (
      <Modal
        className="skill-pc-modal"
        maskClosable={false}
        transparent
        visible={props.modalVisible}
        onClose={() => props.setModalVisible(false)}
        animationType="slide-up"
      >
        <SkillList skillList={props.skillList} setModalVisible={props.setModalVisible}/>
      </Modal>
    );
  }

  return (
    <Modal
      popup
      maskClosable={false}
      visible={props.modalVisible}
      onClose={() => props.setModalVisible(false)}
      animationType="slide-up"
    >
      <SkillList skillList={props.skillList} setModalVisible={props.setModalVisible}/>
    </Modal>
  )
};

export default SkillModal;