@import "../../../../../../styles/variable.less";

.skill-list-wrapper {
  .list-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 50px;
    border-bottom: 1px solid @border-base;

    .title {
      font-size: 15px;
      font-weight: 500;
    }

    .btn-close {
      font-size: 14px;
      color: @font-blue;
      cursor: pointer;
    }
  }

  .skill-list {
    height: 65vh;
    overflow: scroll;
    scrollbar-width: none;
    padding: 0 20px;

    .skill-item {
      margin-top: 16px;
      text-align: left;

      .skill-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;

        .skill-name {
          font-size: 15px;
          font-weight: 500;
        }
      }

      .skill-content {
        color: @font-gray;
        font-size: 14px;
        cursor: pointer;
      }

      .sub-skills {
        .sub {
          height: 40px;
          line-height: 40px;
          color: @font-blue;
          font-size: 14px;
          border-bottom: 1px solid @border-base;
          cursor: pointer;
        }
      }
    }
  }
}