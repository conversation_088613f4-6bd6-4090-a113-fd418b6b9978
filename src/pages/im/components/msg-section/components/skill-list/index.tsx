/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-03-29 19:41:54
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-26 14:38:44
 */

import React, { useState, useContext, useRef, useEffect } from "react";
import { Icon } from "antd-mobile";
import { debounce } from "lodash";

import { MsgListContext } from "../../../../context";

import "./index.less";

interface Skill {
  id: number,
  skillCode: string,
  skillName: string,
  brief: string,
  subSkills?: Skill[]
}

interface PropTypes {
  skillList: Skill[],
  setModalVisible: Function
}

function SkillList(props: PropTypes): JSX.Element {
  const [curScroll, setCurScroll] = useState(0);
  const [statusList, setStatusList] = useState([]);
  const { sendMsg } = useContext(MsgListContext);
  const skillRef = useRef();

  const toggleSkillList = (event: any, hasSubSkills: boolean, skill: Skill, index: number) => {
    // 选择一级技能，没有二级技能时，直接发送
    if (!hasSubSkills) {
      handleSelectSkill({ msgType: -113, msgAttr: skill.id, msgPayload: skill.skillName });
      return;
    }

    const curStatus = statusList[index];
    const newStatusList = [...statusList];
    newStatusList[index] = !curStatus;
    setStatusList(newStatusList);

    // 处理滚动，要收起时不作任何处理，要展开时父元素滚动一定高度
    setCurScroll(curStatus ? 0 : event.currentTarget.offsetTop);
  };

  const addCloseListener = () => {
    ImBus.on("CLOSE_ALL_PAGES", () => {
      props.setModalVisible(false);
    })
  }

  const chooseSubSkill = (subSkill: Skill) => {
    // 选择二级技能
    handleSelectSkill({ msgType: -113, msgAttr: subSkill.id, msgPayload: subSkill.skillName });
  };
  
  
  // 技能列表页技能按钮增加防爆击功能
  const handleSelectSkill = debounce(params => {
    sendMsg(params)
    addCloseListener()
  }, 800)
  

  useEffect(() => {
    try {
      const tabConfig = JSON.parse(sessionStorage.getItem('tabConfig')) || {};
      const chosedSkillCode = tabConfig.skillCode || "";
      props.skillList.forEach((item, index) => {
        if (item.skillCode == chosedSkillCode) {
          handleSetStatusByIndex(index);
        }
      })
    } catch (error) {
      console.log(error);
    }
  }, [])

  useEffect(() => {
    if (curScroll && statusList.length) {
      const parentDom: any = skillRef.current;
      parentDom.scrollTop = curScroll - 67;
    }
  }, [curScroll]);

  useEffect(() => {
    const parentDom: any = skillRef.current;
    const spreadDomAll = parentDom.querySelectorAll(".spread-icon");

    if (spreadDomAll && spreadDomAll?.length === 1) spreadDomAll[0]?.scrollIntoView();
  }, [statusList])

  const handleSetStatusByIndex = index => {
    const curStatus = statusList[index];
    const newStatusList = [...statusList];
    newStatusList[index] = !curStatus;
    setStatusList(newStatusList);
  }

  const SubSkillList = (props: { subSkills: Skill[] }) => {
    return (
      <div className="sub-skills">
        { props.subSkills.map((subSkill: Skill) => (
            <div
              className="overflow-ellipsis sub"
              key={subSkill.id}
              onClick={() => chooseSubSkill(subSkill)}
            >
              { subSkill.skillName }
            </div>
          )
        )}
      </div>
    );
  };

  const SkillItem = (props: {skill: Skill, index: number}) => {
    const { skill, index } = props;
    const hasSubSkills = !!(skill.subSkills && skill.subSkills.length);

    return (
      <div className="skill-item">
        <div onClick={(e) => toggleSkillList(e, hasSubSkills, skill, index)}>
          <div className="skill-header">
            <div className="skill-name">{skill.skillName}</div>
            {
              hasSubSkills
              && <Icon type={statusList[index] ? "down" : "left"} size="xxs" color="#ccc" className={`${statusList[index] && "spread-icon"}`} />
            }
          </div>
          <div className="skill-content">{ skill.brief }</div>
        </div>
        {hasSubSkills && statusList[index] && <SubSkillList subSkills={skill.subSkills} />}
      </div>
    )
  };

  return (
    <div className="skill-list-wrapper">
      <div className="list-header">
        <div className="title">请选择问题分类</div>
        <div className="btn-close" onClick={() => props.setModalVisible(false)}>关闭</div>
      </div>
      <div className="skill-list" ref={skillRef}>
        {props.skillList.map((skill, index: number) => {
          return (
            <SkillItem skill={skill} key={skill.id} index={index} />
          );
        })}
      </div>
    </div>
  );
}

export default SkillList;