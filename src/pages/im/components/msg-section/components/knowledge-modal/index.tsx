/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-04-14 14:20:23
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-17 14:43:55
 */

import React from "react";
import { Modal } from "antd-mobile";

import "./index.less";

interface PropTypes {
  isPc: boolean,
  modalVisible: boolean,
  setModalVisible: Function,
  url: string,
  knowModalTitle?: string
}

function KnowledgeModal(props: PropTypes): JSX.Element {
  const modalProps = {
    className: `knowledge-modal ${props.isPc ? "knowledge-pc-modal" : ""}`,
    transparent: props.isPc,
    popup: !props.isPc,
    visible: props.modalVisible,
    maskClosable: props.isPc,
    onClose: () => props.setModalVisible(false),
    animationType: "slide-up"
  };

  return (
    <Modal {...modalProps} >
      <div className="modal-header">
        <div className="title">{props.knowModalTitle || "小贝知道"}</div>
        <div className="btn-close" onClick={() => props.setModalVisible(false)}>关闭</div>
      </div>
      <iframe className="knowledge-iframe" frameBorder="0" src={props.url} />
    </Modal>
  )
};

export default KnowledgeModal;