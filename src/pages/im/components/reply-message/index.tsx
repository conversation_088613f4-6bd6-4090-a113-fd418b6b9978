import React, { useEffect, useState } from 'react';
import { connect } from "react-redux";
import { Toast } from 'antd-mobile';
import "./index.less";

const mapStateToProps = (state: any) => ({
  allMsgMap: state.im.allMsgMap,
});

function ReplyMessage(props): JSX.Element {
  const [quoteItem, setQuoteItem] = useState(null);
  const { direction, data, msgSummary, type, msgId } = quoteItem || {};

  useEffect(() => {
    const { allMsgMap = {}, quoteId } = props;
    let quoteItem = allMsgMap[quoteId] || null;
    if (!quoteItem) {
      quoteItem = {
        type: -1,
        msgSummary: "[该消息已失效]"
      }
    };

    if(quoteItem?.revocation) {
      quoteItem = {
        type: -1,
        msgSummary: "[该消息已撤回]"
      }
    }
    setQuoteItem(quoteItem);
  }, [props.allMsgMap])

  const onReplyMsgClick = (event) => {
    const quoteEle = document.getElementById(`msg-${msgId}`);
    if (!quoteEle) {
      Toast.info("消息已失效，不支持定位");
      return;
    }
    quoteEle.scrollIntoView();
  }

  const _renderNegtive2 = () => {
    let original = '';
    if (typeof data === 'string') {
      original = JSON.parse(data).original
    } else {
      original = data.original;
    }
    return <img src={original}/>
  }

  const _renderNegtive3 = () => {
    let duration = ""
    if (typeof data === 'string') {
      duration = JSON.parse(data).duration;
    } else {
      duration = data.duration;
    }
    return <div className='reply-msg-neg3'>
      <div className='reply-msg-neg3-icon'/>
      {duration}"
    </div>
  }

  const _renderSummary = () => {
    return <div className='reply-msg-summary' dangerouslySetInnerHTML={{ __html: msgSummary }} />
  }

  const summaryMap = {
    "-3": _renderNegtive3,
    "-2": _renderNegtive2,
    "-1": _renderSummary,
    120: _renderSummary,
    331: _renderSummary
  }

  return <div className='reply-msg' onClick={onReplyMsgClick}>
    {direction && <div className='reply-msg-title'>{direction === 1 ? "咨询人" : "客服"}</div>}
    {summaryMap[type] ? summaryMap[type]() : `暂不支持${type || ""}类型引用消息`}
  </div>
}

export default connect(mapStateToProps)(ReplyMessage)