// 满意度调研弹窗
// 触发机制:
// A+/link移动端
// 用户点击返回和关闭时，调用后端接口，返回是则触发；返回否则允许用户直接退出或关闭
import React, { useEffect, useState } from "react";
import { AxiosResponse } from "axios";
import { Modal } from 'antd-mobile';
import jsBridge from "@/common/bridge";
import Store from "@/common/store";
import ImApi from "@/common/service/im";
import { genUrlParams } from "@/common/utils/url";
import "./index.less";

// 定义满意度弹窗命名空间
declare namespace commentModal {
  type shortUrlType = AxiosResponse<string>;
  interface ICommentStatusFun {
    () : Promise<AxiosResponse<boolean>>
  }
  interface ICommentUrlFun {
    () : Promise<void>
  }
}

// props接口定义
interface PropTypes {
  userInfo: {
    ucId: string
  }
}

function CommentModal(props: PropTypes): JSX.Element {
  const [showModal, setShowModal] = useState<boolean>(false); // 弹窗显示开关
  const [action, setAction] = useState<number>(0) // action：1代表点击的关闭按钮，0代表点击的返回按钮
  const [shortUrl , setShortUrl] = useState<commentModal.shortUrlType | string>("") // 满意度短链接
  // 满意度需要提交的参数
  const { im: { imConfig } } = Store.getState();
  const { baseInfo = {}, bizInfo = {} } = imConfig || {};
  // 获取spm
  const { spm } = genUrlParams() || {};
  const { bizPageSnapshot, bizPageUrl, bizSource, bizSys, pageSession, channelType, busType } = bizInfo;
  const { accessChannel } = baseInfo;
  const extParam = {
        access_channel: accessChannel,
        page_id: pageSession,
        biz_belong: bizSys,
        biz_source: bizSource,
        channel_type: channelType,
        page_url: bizPageUrl,
        page_scene: bizPageSnapshot,
        busType
  }; // 满意度评价提交时，需要提交的额外参数（im相关）

  const listener: () => void = (): void => {
    if(!$ljBridge.webStatus.isApp) return;
     $ljBridge.ready(async (bridge) => {
      window.addEventListener("message", (e) => {  // NOSONAR
      const { data } = e;
      if (data === "commentDone" && showModal) {
        // 提交成功后三秒自动关闭弹窗
        setTimeout(() => {
          setShowModal(false);
          // 自动执行用户上一步操作（关闭或者返回）
          if(action) {
            // action === 1
            // 点击了关闭
            bridge.closeWeb("拜拜");
          } else {
            // action === 0
            // 点击了返回
            if(window.history.length > 1) {
              // 有历史访问记录，则回退
              window.closeEvent = null;
              bridge.callAndBack(
                    JSON.stringify({
                        actionUrl: bridge.getSchemeLink(
                            'callback/subscribe?tag=closeEvent&cancel=true'
                        ),
                        functionName: 'closeEvent'
                    })
                );
              window.history.back();
            } else {
              // 无历史访问记录，则直接退出
              bridge.closeWeb("拜拜");
            } 
          }
        }, 1000)  
      }
    })
     })
    
  } 
  
  // 获取用户是否需要打开评价弹窗
  const getCommentStatus: commentModal.ICommentStatusFun = async () => {
    return await ImApi.getCommentStatus({ spm });
  } 
  
  // 获取满意度短链接
  const getShortCommentUrl: commentModal.ICommentUrlFun = async () => {
    const enCodeParams = encodeURIComponent(JSON.stringify(extParam));
    const params = {
      project: "im",
      businessType: "imExistMsg",
      type: "create",
      busId: spm,
      params: `&params=${enCodeParams}&ucId=${props.userInfo.ucId}&bizId=${spm}`
    }
    const shortCommentUrl: commentModal.shortUrlType = await ImApi.getShortCommentUrl(params);
    setShortUrl(shortCommentUrl);
  }

  useEffect((): void => {
    // 监听满意度评价页面（iframe）的postMessage消息
    listener(); 
    // 获取满意度短链接  
    getShortCommentUrl();
    // 监听客户端返回、关闭事件
    jsBridge.backOrClose(setShowModal, setAction, getCommentStatus);
  }, [])

  useEffect((): void => {
    setAction(action)
    listener(); 
  }, [action])

  useEffect(() => {
    listener()
  }, [showModal])

  return (
        <Modal
          popup
          maskClosable
          className="comment-modal"
          animationType="slide-up"
          visible={showModal}
          onClose={() => {
            setShowModal(false)
          }}
        >
          <div className="modal-header">
            <div className="btn-close" onClick={() => {
              setShowModal(false)}}>关闭</div>
          </div>
          <iframe className="comment-iframe" frameBorder="0" src={shortUrl as string} />
        </Modal>
)}

export default CommentModal;