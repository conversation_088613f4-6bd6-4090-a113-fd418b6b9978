.pc-tool-side {
  box-sizing: border-box;
  padding: 10px 15px;
  width: 30%;
  height: 90vh;
  background: #fff;
  border-left: 1px solid #f5f5f5;
  overflow-y: scroll;
  scrollbar-width: none;

  .tools-wrapper {
    display: flex;
    flex-wrap: wrap;

    .tool-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 24px;
      width: 33.3%;
      text-align: center;
      cursor: pointer;

      img {
        width: 44px;
        height: 44px;
      }

      .tool-name {
        margin-top: 6px;
        width: 60px;
        font-size: 12px;
        line-height: 16px;
      }
    }
  }

  .tools-tabs-wrapper {
    overflow: hidden;
    width: 100%;
    height: 86vh;
    position: relative;
    padding: 0 24px;
  }

  .tool-tabs-content {
    max-height: calc(86vh - 50px);
    overflow-y: scroll;
    scrollbar-width: none;
  }

  .tab-btn {
    position: absolute;
    cursor: pointer;
    width: 24px;
    height: 47px;
    top: -10px;
    text-align: center;
    background: #FFFFFF;
    box-shadow: -1px 1px 4px 0 rgba(0,0,0,0.05);
  }
  .next {
    right: 0;
    background: url(@/common/assets/image/next-tab.png) no-repeat;
    background-size: 10px 10px;
    background-position: center;
  }
  .up {
    left: 0;
    background: url(@/common/assets/image/previous-tab.png) no-repeat;
    background-size: 10px 10px;
    background-position: center;
  }
  .tools-tabs {
    overflow-x: scroll;
    scrollbar-width: none;
    overflow-y: hidden;
    white-space: nowrap;
    margin-bottom: 16px;
    border-bottom: 1px solid #F0F0F0;

    .tool-tabs-item {
      // flex-shrink: 0;
      padding: 6px 16px 14px 16px;
      color: #222222;
    }

    .active-tab {
      color: #0984F9;
      position: relative;
      display: inline-flex;
      justify-content: center;

      &::before {
        content: '';
        position: absolute;
        bottom: 0;
        width: 32px;
        height: 2px;
        background: #0984F9;
      }
    }
  }
}

.no-padding {
  padding-left: 0;
  padding-right: 0;
}