/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-06-15 14:10:44
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-16 14:22:11
 */
import React, { useEffect, useState } from "react";

import { track } from "@/common/tracks/actions/im";
import jsBridge from "@/common/bridge";
import "./index.less";

interface Tool {
  icon: string,
  name: string,
  url: string
}

interface PropTypes {
  toolInfo: {
    data: { list: Tool[] },
    key: string
  },
  isNewType?: boolean,
  tab?: string,
  moduleType?: string
}

function PcToolSection(props: PropTypes):JSX.Element {
  const { tab, isNewType, moduleType } = props;
  const { data, key } = props.toolInfo;
  const [activeTab, setActiveTab] = useState('');

  let { list = [] } = data || {};
  let tabsData = {};

  useEffect(() => {
    if (isNewType) {
      setTimeout(() => { document.querySelector(".active-tab")?.scrollIntoView(); }, 0);
    }

    let LimitExpoTool = [];
    list.forEach((item:any, index) => {
      const { bizKey, data } = item;
      if (tab === bizKey) {
        LimitExpoTool = data?.list?.map(item => { return { title: item.name } });
      }
    });

    const moduleContent = list.reduce((acc: any[], cur: Tool) => {
      acc.push({ title: cur.name });
      return acc;
    }, []);

    track(41807, {
      module_type: moduleType,
      module_content: JSON.stringify(isNewType ? LimitExpoTool : moduleContent)
    });
  }, [])

  if (isNewType) {
    list.forEach((item:any) => {
      const { bizKey, data = {}, name } = item || {};
      tabsData[item.bizKey] = {
        key: bizKey,
        title: name,
        list: Array.isArray(data.list) ? data.list : []
      };
    });
  }

  const toolClick = (tool: Tool) => {
    const { name, url } = tool;
    const moduleName = JSON.stringify({ name, url });
    jsBridge.openPage(url);
    track(41809, { module_type: key, module_name: moduleName });
  };

  const renderToolList = (data?: []) => {
    return (
      (data || list).map((tool: any) => {
        return (
          <div className="tool-item" key={tool.name} onClick={() => toolClick(tool)}>
            <div className="tool-box">
              <img src={tool.icon} />
            </div>
            <div className="tool-name">{ tool.name.slice(0, 10) }</div>
          </div>
        )
      })
    );
  }

  const handleChangeTab = (key, type) => {
    if (!key || !type) return;
    const keys = Object.keys(tabsData);
    const currIndex = keys.findIndex(item => item === key);
    const nextKey = keys[type == "up" ? currIndex - 1 : currIndex + 1];

    setActiveTab(nextKey);

    setTimeout(() => {
      const dom = document.querySelector(".active-tab");
      if (dom) dom.scrollIntoView();
    }, 200)
  }

  const handleClickTab = key => {
    setActiveTab(key);

    const curList = tabsData[key]?.list.map(item => { return {title: item.name} });
    track(41807, { module_type: props.moduleType, module_content: JSON.stringify(curList) });

    setTimeout(() => { document.querySelector(`.${key}`)?.scrollIntoView(); }, 0)
  }

  const renderNewToolList = () => {
    const keys = Object.keys(tabsData);
    const currKey = activeTab || tab || keys[0];
    const currList = activeTab ? tabsData[activeTab].list : tabsData[currKey].list;
    const isFirstTab = currKey === keys[0];
    const isLastTab = currKey === keys[keys.length - 1];

    return (
      <div className="tools-tabs-wrapper">
        { !isFirstTab && <span className="tab-btn up" onClick={() => { handleChangeTab(currKey, "up") }} />}
        { !isLastTab && <span className="tab-btn next" onClick={() => { handleChangeTab(currKey, "down") }} />}
        <div className="tools-tabs">
          { keys.map(key => (
            <span
              className={`tool-tabs-item ${key === currKey && "active-tab"} ${key}`}
              key={key}
              onClick={() => { handleClickTab(tabsData[key].key) }}>{tabsData[key].title}</span>
          ))}
        </div>
        <div className="tool-tabs-content tools-wrapper">
          {renderToolList(currList)}
        </div>
      </div>
    );
  }

  return (
    <div className={`pc-tool-side ${isNewType && "no-padding"}`}>
      { !isNewType && <h3>常用工具</h3> }
      <div className="tools-wrapper">
        { !isNewType && renderToolList() }
        { isNewType && renderNewToolList() }
      </div>
    </div>
  );
}

export default PcToolSection;
