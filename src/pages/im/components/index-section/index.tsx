/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-03-15 16:48:24
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-21 15:39:50
 */

import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import Viewer from "react-viewer";
import VConsole from "vconsole";
import { Toast } from 'antd-mobile';
import {
  ADD_NEW_MSGS,
  UPDATE_MSG_MAP
} from "@/common/store";
import { randomId } from "@/common/utils"

import "@/common/tracks";
import UserApi from "@/common/service/user";
import ImApi, { setApiHost, setWebHost } from "@/common/service/im";
import { genUrlParams } from "@/common/utils/url";
import UaHelper from "@/common/utils/ua";
import Dt from "@/common/utils/dt";
import ImBus from "@/common/event-bus";
import { track } from "@/common/tracks/actions/im";
import jsBridge from "@/common/bridge";
import {
  SET_IM_CONFIG,
  SAVE_USER_INFO,
  HIDE_IMAGE_VIEWER,
  CHANGE_SERVICE_STATUS,
  CHANGE_INQUEUE_STATUS
} from "@/common/store";

import PicturePreview from "../picture-reviewer";
import CommentModal from "../comment-modal";

import { ImContext, MsgListContext } from "../../context/index";
import Mobile from "../../mobile";
import Pc from "../../pc";
import InitStatusMap from "../../constant/init-status-map";
import { hex_md5 } from "@/common/utils/md5"
import { escape } from "lodash";
import AIAgent from "../../agent-chat/pc"

import "./index.less";


if (IS_DEV && UaHelper.isMobile()) {
  window.VConsole = new VConsole();
}

window.ImBus = new ImBus();

const mapStateToProps = (state: any) => ({
  showImgViewer: state.im.showImgViewer,
  msgImages: state.im.msgImages,
  userInfo: state.user.userInfo
});

const mapDispatchToProps = (dispatch: any) => ({
  inServiceChange: (inService: boolean) => dispatch({ type: CHANGE_SERVICE_STATUS, payload: inService }),
  inQueueChange: (inQueue: boolean) => dispatch({ type: CHANGE_INQUEUE_STATUS, payload: inQueue }),
  saveImConfig: (config: any) => dispatch({ type: SET_IM_CONFIG, payload: config }),
  saveUserInfo: (data: any) => dispatch({ type: SAVE_USER_INFO, payload: data }),
  hideImageViewer: () => dispatch({ type: HIDE_IMAGE_VIEWER }),
  addNewMsgs: (msgList: any[]) => dispatch({ type: ADD_NEW_MSGS, payload: msgList }),
  updateMsgMap: (list: any[]) => dispatch({type: UPDATE_MSG_MAP, payload: list}),
});

let timeout: NodeJS.Timeout = null;

function Im(props: any): JSX.Element {
  const urlParams = genUrlParams() || {};
  const [imConfig, setImConfig] = useState(null);
  const [initStatus, setInitStatus] = useState(0); // 初始化时用户状态: 1-进线中 2-排队中 0-其他状态
  const [loading, setLoading] = useState(true);
  const isMobile = UaHelper.isMobile();
  const isAnony = props.anon || false;
  const isChatGPT = props.chatgpt || false;
  const [disableInput, setDisableInput] = useState(false);

  // 发送消息  
  const sendMsg = (msg: any) => {
    const data = {
      spm: urlParams.spm,
      fromUcid: props.userInfo.ucId,
      msgType: -1,
      msgPayload: "",
      sendTime: Date.now(),
      channel: imConfig.bizInfo.bizChannel,
      msgId: randomId(),
      clientId: imConfig.baseInfo.clientId,
      ...msg
    };
    const { msgType, msgPayload, msgId } = data;
    const msgitem = {
      ...msg,
      id: msgId,
      msgId,
      direction: 1,
      type: msgType,
      msgPayload,
      manual: false,
    }

    return new Promise((resolve, reject) => {
      if (isChatGPT) {
        // 发送chatgpt query，触发sse
        window.ImBus.emit("NEW_SSE_MSG_SEND", msgitem);
        resolve(data);
      } else {
        // 默认逻辑
        window.dt?.sendInfo?.("发送消息2", {
          ...data
        })
        ImApi.sendMsg(data)
        .then((res) => {
          const msgItem = {
            ... msgitem,
            msgSummary: res?.msgSummary
          }
          addSingleMsg(msgItem);
          resolve(data);
        })
        .catch(err => {
          console.log(err);
          Toast.info("消息发送失败，请检查网络");
          reject(err);
        });
        }
    })
  };

  // 滚到底部
  const scrollToBottom = () => {
    const bottomDom = document.getElementById("bottomPlaceholder");
    if (!bottomDom) return;
    timeout = setTimeout(() => {
      bottomDom.scrollIntoView(true);
      clearTimeout(timeout);
    }, 0);
  };

  const addSingleMsg = (msgitem) => {
    let data = msgitem?.msgPayload;
    if (typeof data === "string") {
      try {
        if (typeof JSON.parse(data) === "object") {
          data = JSON.parse(data);
        }
      } catch (err) {
        // ignore
      }
    }
    if (msgitem?.type == -1) {
      data = escape(data);
    }
    props.addNewMsgs([{...msgitem, data}]);
    props.updateMsgMap([{...msgitem, data}])
    scrollToBottom();
  };
    
  const initData = async() => {
    let data : any;
    if (isAnony) {
        // 匿名
        data = await initAnonyData();
    } else {
        // 非匿名
        data = await initUnAnonyData();
    }
    

    try {
      // 初始化灯塔
      Dt(data);
    } catch (error) {
      // 避免灯塔出错影响页面加载
      console.log("灯塔初始化失败", error);
    }

    const configData = {
      ucid: data.ucId,
      spm: urlParams.spm,
      register: urlParams.register,
      accessSource: isMobile ? "app" : "pc",
      channelType: UaHelper.getAppChannel()
    };

    Promise.all([
      ImApi.getImConfig(configData),
      ImApi.getUserState({ fromUcid: data.ucId, spm: urlParams.spm })
    ]).then(([imConfig, initStatus]: [any, any]) => {
      setImConfig(imConfig); // imConfig 后续考虑收敛到一个地方
      props.saveImConfig(imConfig);
      setInitStatus(initStatus);
      props.inServiceChange(initStatus === InitStatusMap.inService);
      props.inQueueChange(initStatus === InitStatusMap.inQueue);
      setLoading(false);
      track(41805);
    }).catch(x => x);
  }

  const clearTimer = () => {
    if (timeout) {
      clearTimeout(timeout);
    }
  };

  /**
  * 是否禁用微信网页右上角里菜单
  */
  const disabledOptionMenu = () => {
    if (typeof WeixinJSBridge === "undefined") {
      if (document.addEventListener) {
        document.addEventListener('WeixinJSBridgeReady', onBridgeReady);
      }
    } else {
      onBridgeReady();
    }
  }

  const onBridgeReady = () => {
  if (typeof WeixinJSBridge !== "undefined") WeixinJSBridge.call('hideOptionMenu');
  }
  
  // 初始化匿名数据
  const initAnonyData = async () => {
    const requid: string = urlParams.requid || "";
    if (!requid) {
        Toast.info("地址不合法～");
        return
    }
    let data = {ucId: requid, userName: "游客"};
    props.saveUserInfo(data);

    try {
    let apiHost: any = await ImApi.getImApiUrlPrefix({ ucid: data.ucId, spm: urlParams.spm }, {'Access-Token': hex_md5(urlParams.spm)});
    if (apiHost) {
        apiHost = apiHost + hex_md5('@Token@' + data.ucId);
        setApiHost(apiHost);
        setWebHost(apiHost);
    }
    } catch (e) {
        console.log(e)
    // 避免接口挂了的兜底
    }
    return data;
  }

  // 初始化非匿名数据
  const initUnAnonyData = async () => {
     // 非匿名
     const token: string = urlParams.token || "";
     if (token) {
       // 当前域和ke域都setToken
       document.cookie = `lianjia_token=${token};`;
       document.cookie = `lianjia_token=${token};domain=.ke.com`;
     }
     await jsBridge.getAccessToken();
     let data = await UserApi.getUserInfo();
     props.saveUserInfo(data);
 
     try {
       const apiHost: any = await ImApi.getVersionUrl({ ucid: data.ucId, spm: urlParams.spm });
       if (apiHost) setApiHost(apiHost);
     } catch (e) {
       // 避免接口挂了的兜底
     }
     return data;
  }

  useEffect(() => {
    if (isAnony) {
        // 匿名访问的初始化
        initData();
        jsBridge.setPageTitle("直达老颜");
        // 微信中禁用右上角菜单
        disabledOptionMenu();
    } else {
        // 非匿名访问，数据初始化
        initData();
        jsBridge.setPageTitle("我的客服");
    }
    window.addEventListener("offline", () => {
      Toast.info("网络断开～")
    })
    return clearTimer;
  }, []);

  if (loading || !imConfig) return null;

  return (
    <ImContext.Provider value={{ imConfig, urlParams, isPc: !isMobile, initStatus, isChatGPT, disableInput, setDisableInput }}>
      <MsgListContext.Provider value={{sendMsg}}>
        {isMobile ? <Mobile /> : (isChatGPT ? <AIAgent /> : <Pc/>) }
      </MsgListContext.Provider>
      {isMobile ? <PicturePreview
        list={props.msgImages}
        index={0}
        handleClose={props.hideImageViewer}
        showImgViewer={props.showImgViewer}
      /> : <Viewer
        visible={props.showImgViewer}
        onClose={props.hideImageViewer}
        activeIndex={0}
        images={props.msgImages}
        noToolbar
        noFooter
        onMaskClick={isMobile ? props.hideImageViewer : () => { }}
      />}
      {!isAnony && <CommentModal userInfo={props.userInfo} /> }
    </ImContext.Provider>
  );
}

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Im);