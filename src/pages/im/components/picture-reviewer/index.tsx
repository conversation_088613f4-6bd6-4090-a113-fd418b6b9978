/**
 * @desc 移动端图片预览预 -- 支持手势放大
 */
import React, { useState } from 'react';
import { PhotoSwipe } from 'react-photoswipe';
import 'react-photoswipe/lib/photoswipe.css';

interface PropTypes {
  list: [{}],
  showImgViewer: boolean,
  index: number,
  handleClose: () => {}
}

function PicturePreview(props: PropTypes): JSX.Element {
  const [ options ] = useState({closeOnScroll: false,
    captionEl: true,
    fullscreenEl: false,
    zoomEl: false,
    shareEl: false,
    tapToClose: false,
    index: props.index,
    maxSpreadZoom: 4,
    bgOpacity: 0.8,
    currentIndex: props.index})
  return (
    props.list.length > 0 ? <PhotoSwipe
        isOpen={props.showImgViewer}
        items={props.list}
        options={options}
        onClose={props.handleClose}
      /> : null)
}

export default PicturePreview;
