.msg-wrapper .mp3-wrapper, .msg-wrapper.client-msg .mp3-wrapper {
  display: flex;
  flex-direction: column;
  position: relative;
  // 覆盖.msg-card-base样式
  background-color: inherit;
  padding: 0;

  .msg-neg3-audio {
    display: flex;
    flex-direction: row-reverse;
    justify-content: end;
    align-items: center;
    align-self: flex-end;
    max-width: 100%;
    background-color: #cde5ff;
    padding: 8px 12px;
    border-radius: 5px;
    cursor: pointer;
    
    &-icon {
      background: url(@/common/assets/image/voice-right.png) no-repeat;
      background-size: cover;
      width: 14px;
      height: 14px;
      display: inline-block;
      margin-left: 4px;
    }

    &.playing {
      .msg-neg3-audio-icon {
        background: url(@/common/assets/image/voice-right-playing.png) no-repeat;
        background-size: cover;
      }
    }
  }

  .msg-neg3-audio_reverse {
    flex-direction: row;
    justify-content: start;
    background-color: #FFF;
    align-self: start;
    .msg-neg3-audio-icon {
      background: url(@/common/assets/image/voice-left.png) no-repeat;
      background-size: cover;
      width: 14px;
      height: 14px;
      display: inline-block;
      margin-right: 4px;
    }
    &.playing {
      .msg-neg3-audio-icon {
        background: url(@/common/assets/image/voice-left-playing.png) no-repeat;
        background-size: cover;
      }
    }
  }

  audio {
    display: none;
  }

  .msg-neg3-text {
    border-top: rgba(0, 0, 0, 0.05) 1px solid;
    word-break: break-all;
    margin-top: 4px;
    padding-top: 4px;
    min-width: 20%;
    max-width: 100%;
    background-color: #cde5ff;
    padding: 8px 12px;
    border-radius: 5px;
    align-self: flex-end;
    &-spinner {
      position: relative;
      width: 22px;
      height: 22px;
      right: 28px;
      margin: 0 auto;
      div {
        transform-origin: 40px 16px;
        animation: lds-spinner 1.2s linear infinite;
      }
      div:after {
        content: " ";
        display: block;
        position: absolute;
        top: 20px;
        left: 38px;
        width: 2px;
        height: 6px;
        border-radius: 20%;
        background: #bbb;
      }
      div:nth-child(1) {
        transform: rotate(0deg);
        animation-delay: -0.7s;
      }
      div:nth-child(2) {
        transform: rotate(45deg);
        animation-delay: -0.6s;
      }
      div:nth-child(3) {
        transform: rotate(90deg);
        animation-delay: -0.5s;
      }
      div:nth-child(4) {
        transform: rotate(135deg);
        animation-delay: -0.4s;
      }
      div:nth-child(5) {
        transform: rotate(180deg);
        animation-delay: -0.3s;
      }
      div:nth-child(6) {
        transform: rotate(225deg);
        animation-delay: -0.2s;
      }
      div:nth-child(7) {
        transform: rotate(270deg);
        animation-delay: -0.1s;
      }
      div:nth-child(8) {
        transform: rotate(315deg);
        animation-delay: 0s;
      }
      @keyframes lds-spinner {
        0% {
          opacity: 1;
        }
        100% {
          opacity: 0;
        }
      }
    }
    &-icon {
      margin-right: 5px;
      display: inline-block;
      width: 12px;
      height: 12px;
      background-image: url(//file.ljcdn.com/psd-sinan-file/prod/audit_file/D7F7951D0EE24F89B96FF9502003BCAD/icon.png);
      background-size: auto 12px;
      background-repeat: no-repeat;
      vertical-align: middle;
    }
    &-failed {
      color: rgba(0, 0, 0, 30%);
      font-size: 12px;
      vertical-align: middle;
    }
  }

  .msg-neg3-text_reverse {
    background-color: #FFF;
    align-self: start;
  }
}