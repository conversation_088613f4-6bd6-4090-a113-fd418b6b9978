/*
 * @Author: liuzhenkun002
 * @Date: 2021-04-09 16:14:13
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-12 20:04:53
 */

import React, { useContext, useEffect, useState } from "react";
import { connect } from "react-redux";
import { debounce } from "lodash";
import ImApi from "@/common/service/im"
import { ImContext, MsgListContext } from "@/pages/im/context";

import "./index.less";

interface PropTypes {
  data: any,
  type: string,
  isClient: boolean,
  domId: string,
  inService: boolean,
  userInfo: any
}

const transferStatusMap = {
  0: "N0_TRANSFER",
  1: "TRANSFERING",
  2: "TRANSFER_SUCCESS",
  3: "TRANSFER_FAILED"
}

const openIntelligentStageVoiceMAP = {
  "close": 0,
  "open": 1
}

const mapStateToProps = (state: any) => ({
  inService: state.im.inService
});
function Mp3(props: PropTypes): JSX.Element {
  const { data, isClient, domId, inService, userInfo } = props;
  const { duration, url } = typeof data === "string" ? JSON.parse(data) : data;
  const [curAudio, setCurAudio] = useState(null);
  const [text, setText] = useState("");
  const [transferStatus, setTransferStatus] = useState(transferStatusMap[0]);
  const { imConfig: { extConfig = {} } } = useContext(ImContext);
  const { sendMsg } = useContext(MsgListContext);
  const { enableAutoToAudio } = extConfig.globalSwitch || {}
  const { openIntelligentStageVoice } = extConfig;
  const isOpenIntelligentStageVoice = (openIntelligentStageVoice || {}).status == openIntelligentStageVoiceMAP.open; // 是否开启智能语音转文字

  const handleVoiceStart = (event: any) => {
    const container = event.currentTarget;
    const audio = container.querySelector("audio");

    if (curAudio && curAudio !== audio) {
      this.endAudio(curAudio);
    }

    if (audio.paused) {
      playAudio(audio);
      return;
    }

    endAudio(audio);
  };

  const transferToText = (sendMsgParams?: any) => {
    if (text) {
      return;
    }
    setTransferStatus("TRANSFERING");
    ImApi.toBase64({filePath: url})
      .then((data) => {
        const property = {
          ...data.property,
          enable_punctuation_prediction: true
        }
        const asrId = `${Math.floor(Math.random() * 10000000000)}-${new Date().getTime()}`;
        /* 是否转文本开关 */
        if (enableAutoToAudio) {
          return ImApi.asrToText({...data, property}, asrId);
        } else {
          return new Promise<void>((res) => {res()})
        }
      })
      .then((data) => {
        const res = (data || []).reduce(((prev, curr) => prev + curr.text), "");
        setText(res);
        setTransferStatus("TRANSFER_SUCCESS");
        document.removeEventListener("scroll", handleScroll, true);

        // 智能阶段语音转文字成功后发送消息
        if (sendMsgParams) {
          sendINTMsg(sendMsgParams, res);
        }
      })
      .catch(e => {
        console.error(e);
        setText("--");
        setTransferStatus("TRANSFER_FAILED")
        document.removeEventListener("scroll", handleScroll, true);
      })
  }

  // 发送智能语音转文字消息 -228
  const sendINTMsg = (sendMsgParams: any, msgPayload: string) => {
    const { msgType } = sendMsgParams;
    const isRealEnd = !!sessionStorage.getItem("voiceSendStart"); // 是否真实端发送的, 只有触发真正 touch 的端才会有 sessionStorage
    if (!msgType || !msgPayload || !isRealEnd) return;

    sendMsg({ msgType, msgPayload, fromUcid: userInfo.ucId });

    sessionStorage.removeItem("voiceSendStart");
  }

  useEffect(() => {
    if (transferStatus === transferStatusMap[0]) {
      document.addEventListener("scroll", handleScroll, true);

      // 如果开启了智能语音转文字功能，且在视口内，直接转换, 转换后发送 -228 消息
      if (insideViewPoint() && isOpenIntelligentStageVoice && !inService) {
        transferToText({ msgType: -228 });
      }
    }
    return () => {
      document.removeEventListener('scroll', handleScroll, true);
    }
  }, [transferStatus]);

  const handleScroll = debounce(() => {
    if (transferStatusMap[0] === transferStatus && insideViewPoint()) {
      transferToText()
    }
  }, 300)

  const insideViewPoint = () => {
    const dom = document.getElementById(domId);
    if (!dom) return false;
    const offset = dom.getBoundingClientRect();
    const offsetTop = offset.top;
    const offsetBottom = offset.bottom;
    if (offsetTop <= window.innerHeight && offsetBottom >= 0) {
      return true;
    } 
    return false;
  }

  const handleAudioEnded = (event: any) => {
    endAudio(event.target)
  };

  const playAudio = (audio: any) => {
    setCurAudio(audio);
    audio.parentElement.classList.add("playing");
    audio.play();
  };

  const endAudio = (audio: any) => {
    setCurAudio(null);
    audio.parentElement.classList.remove("playing");
    audio.pause();
    audio.load();
  };

  const _renderSpinner = () => {
    return <div className="msg-neg3-text-spinner">
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
    </div>
  }

  const _renderText = () => {
    if (!enableAutoToAudio) return null;
    if (transferStatus === transferStatusMap[1]) {
      return <div className={`msg-neg3-text ${isClient ? "" : "msg-neg3-text_reverse"}`}>
        {_renderSpinner()}
      </div>;
    } else if (transferStatus === transferStatusMap[2]) {
      if (text) {
        return <div className={`msg-neg3-text ${isClient ? "" : "msg-neg3-text_reverse"}`}>{text}</div>
      } else {
        return <div className={`msg-neg3-text ${isClient ? "" : "msg-neg3-text_reverse"}`}>
          <span className='msg-neg3-text-icon'/>
          <span className="msg-neg3-text-failed">未识别到有效信息</span>
        </div>;
      }
    } else if (transferStatus === transferStatusMap[3]) {
      return <div className={`msg-neg3-text ${isClient ? "" : "msg-neg3-text_reverse"}`}>
          <span className='msg-neg3-text-icon'/>
          <span className="msg-neg3-text-failed">语音转换失败</span>
        </div>;
    }
    return null;
  }

  return (
    <div
      className={`msg-card-base mp3-wrapper`}
    >
      <div
        className={`msg-neg3-audio ${isClient ? "" : "msg-neg3-audio_reverse"}`}
        style={{ width: `${Math.max(duration * 3 + 60, 60)}px` }}
        onClick={handleVoiceStart}
      >
        <span className="msg-neg3-audio-icon" />
        <span className="msg-neg3-audio-duration">{ duration }"</span>
        <audio src={url} preload="none" controls onEnded={handleAudioEnded} />
      </div>
      {_renderText()}
    </div>
  );
};

export default connect(mapStateToProps)(Mp3);