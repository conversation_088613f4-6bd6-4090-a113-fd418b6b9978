/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-03-29 15:08:00
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-22 10:55:32
 */

import React from "react";

import { track } from "@/common/tracks/actions/im";
import jsBridge from "@/common/bridge";

import "./index.less";

interface PropTypes {
  data: {
    query: string,
    queryId: string,
    title?: string,
    content: string,
    url: string,
    icon?: string
  }
}

function JumpCard(props: PropTypes): JSX.Element {
  const { title, content, url, icon, query, queryId } = props.data;

  const cardClcik = () => {
    jsBridge.openPage(url);
    track(41827, {
      click_result: JSON.stringify({ url, title }),
      query_id: queryId,
      query_content: query
    });
  };

  return (
    <div className="msg-card-base jump-card-wrapper" onClick={cardClcik}>
      {
        icon && <div className="icon-wrapper">
          <img src={icon} />
        </div>
      }
      <div className="content-wrapper">
        {title && <div className="title">{ title }</div>}
        <div className="content">{ content }</div>
      </div>
    </div>
  )
};

export default JumpCard;