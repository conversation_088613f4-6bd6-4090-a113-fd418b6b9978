/*
 * @Author: liuzhenkun002
 * @Date: 2021-03-25 15:23:16
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-05-21 18:11:32
 */

import React, { useContext, useState, useEffect } from "react";

import { track } from "@/common/tracks/actions/im";
import MsgAttrMap from "@/pages/im/constant/msg-attr-map";
import UaHelper from "@/common/utils/ua";

import { MsgListContext } from "../../../../context";

import "./index.less";

interface Question {
  id: string,
  title: string,
  content?: string,
  index: number,
  queryId?: string;
}

interface PropTypes {
  type: string,
  msgGroup: number,
  data: {
    query: string,
    queryId: string,
    results: Question[],
    recommend?: Question[]
  }
}

function QuestionList(props: PropTypes): JSX.Element {
  const { data: { query, queryId } } = props;
  const [showMore, setShowMore] = useState(false);
  const { sendMsg, isHistory } = useContext(MsgListContext);
  const sourceType = UaHelper.isMobile() ? "app" : "pc";

  const recommendClick = (question: Question) => {
    const { id, title, index, queryId } = question;
    const msg = { msgType: -1, msgPayload: title, knowledgeId: id, msgAttr: MsgAttrMap.knowledge, queryId };

    track(41824, {
      click_result: title,
      knowledge_id: id,
      knowledge_location: index + 1,
      query_id: queryId,
      query_content: query,
      query,
      queryId,
      search_source: sourceType,
    });

    if (isHistory) {
      ImBus.emit("MSG_SEND", msg);
      return;
    }

    sendMsg(msg);
  };

  const showMoreClick = () => {
    setShowMore(true);
    track(41825, { query_id: queryId, query_content: query });
  };

  const showDetailModal = () => {
    const { results } = props.data;
    const { id, title } = results[0];
    track(41826, {
      click_result: title,
      knowledge_id: id,
      query_id: queryId,
      query_content: query
    });
    ImBus.emit("SHOW_KONWLEDGE_MODAL", { id });
  };

  // 唯一知识卡片默认直接打开功能
  // 不需要默认打开情况：
  // 1、卡片类型为-201（该组件支持-200，-201两种卡片）
  // 2、有“您是否还想问”列表
  // 3、该条数据为历史消息,msgGroup：0：历史消息，1：实时消息
  useEffect(() => {
    const { data, type, msgGroup } = props;
    const { recommend } = data;
    if (type === "-201"
      && !(recommend && recommend.length)
      && msgGroup === 1)
      setTimeout(showDetailModal, 500);
  }, [props.data])

  const Question = (props: Question) => {
    return (
      <div className="question-item" onClick={() => recommendClick(props)}>
        {props.title}
      </div>
    );
  };

  const NormalCard = () => {
    const { results, queryId } = props.data;
    if (!results || !results.length) return null;
    return (
      <>
        <div className="tips">您是否想问以下问题?</div>
        <Recommend list={results} queryId={queryId} />
      </>
    );
  };
  const ExactCard = () => {
    const { recommend, results } = props.data;
    const { title, content } = results[0];
    const hasRecommend = recommend && recommend.length;
    return (
      <>
        <div className="content-wrapper" onClick={() => showDetailModal()}>
          <div className="title">{title}</div>
          <div className="content">
            {content}
            {hasRecommend && <span className="load-detail">查看详情</span>}
          </div>
        </div>
        {hasRecommend ? (
          <>
            <div className="tips">您是否还想问?</div>
            <Recommend list={results} queryId={queryId} />
          </>
        ) : (
          <div className="load-detail-btn" onClick={() => showDetailModal()}>查看详情</div>
        )
        }
      </>
    )
  };

  const Recommend = (props: { list: Question[], queryId: string }) => {
    const { list, queryId } = props;
    if (!list.length) return null;
    const showLoadMore = list.length > 3;
    const listShow = list.slice(0, 3);
    const listHide = list.slice(3);

    return (
      <>
        <div className="question-list">
          {listShow.map((item, index) => <Question index={index} key={item.id + index} queryId={queryId} {...item} />)}
          {showMore && listHide.map((item, index) => <Question index={index} key={item.id + index} queryId={queryId}{...item} />)}
        </div>
        {showLoadMore && !showMore && <div className="load-more" onClick={showMoreClick}>查看更多</div>}
      </>
    )
  };

  return (
    <div className="msg-card-base question-list-wrapper">
      {props.type === "-200" ? <NormalCard /> : <ExactCard />}
    </div>
  );
}

export default QuestionList;
