import React from "react";
import { CardProps } from "../msg-331";
import "./index.less";

interface Item {
  leftText: string;
  rightText: string;
}

export default function Card7(props: CardProps): JSX.Element{
  const { data: { uiModel = {} } } = props;
  const { title, title_tail, items, button } = uiModel;
  const url = button?.action;

  const showDetailModal = (jumpUrl: string) => {
    // TODO title后续方案待定，暂时隐藏，用空格代替
    ImBus.emit("SHOW_KONWLEDGE_MODAL", { url: jumpUrl, title: "  " });
  };

return (
  <div className="msg-card-base msg-item-card">
    <div className="card-wrapper" onClick={button?.length ? (() => showDetailModal(url)) : (() => {})}>
      <div className="title">
        <span>{title}</span>
        {title_tail && <span className="msg-card-title-status">{title_tail}</span>}
      </div>
      <ul className="content">
        {(items || []).map((item: Item, i: number) => {
          return <li className="msg-card-content-item" key={i}>
            <span className="msg-card-content-item-left">{item.leftText}</span>
            <span className="msg-card-content-item-right">{item.rightText}</span>
          </li>
        })}
      </ul>
    </div>
    {button?.length && <div className="load-detail-btn" onClick={() => showDetailModal(url)}>查看详情</div>}
  </div>)
}