import React, { useContext } from "react";
import jsBridge from "@/common/bridge";
import ImApi from "@/common/service/im";
import { ImContext } from "@/pages/im/context";

import { CardProps } from "../msg-331";
import { connect } from "react-redux";
import { track } from "@/common/tracks/actions/im";
import ComposeInView from "../inview";
interface Button {
  text: string;
  textColor: string;
  action: string
}

const mapStateToProps = (state: any) => ({
  userInfo: state.user.userInfo
});

const parseScheme = (scheme: string) => {
  let query = "";
  const queryObj: {[key: string]: string} = {};
  if (scheme.indexOf("?") > -1) {
    query = scheme.substring(scheme.indexOf("?") + 1);
    const queryTmp = query.split("&");
    const len = queryTmp.length;
    for (let i = 0; i < len; i += 1) {
      const _q = queryTmp[i].split("=");
      const [key, value] = _q;
      queryObj[key] = value;
    }
  }
  return queryObj;
}

function Card1(props: any): JSX.Element{
  const { data: { uiModel = {}, query, queryId, intent } } = props;
  const { summary, title, buttons } = uiModel;
  const isPredictionCard = 
    ((buttons || []).filter((item: Button) => (item.action || "").indexOf("toprediction") > -1)).length > 0; // 是否是预测卡片

  const showDetailModal = (url: string) => {
    ImBus.emit("SHOW_KONWLEDGE_MODAL", { url });
  };

  const { imConfig, urlParams, isPc } = useContext(ImContext);

  const handleCardClick = (scheme: string) => {
    if(!scheme) return;
    if (scheme.indexOf("http") === -1) {
      ImApi.clickCard({
        scheme: scheme,
        spm: urlParams.spm,
        fromUcid: props.userInfo.ucId,
        channel: imConfig.bizInfo.bizChannel,
      })
    } else {
      const { action } = parseScheme(scheme) || {};
      if (action === "knowledgeDetail") {
        showDetailModal(scheme);
      } else { // action的值为blank或其他
        jsBridge.openPage(scheme);
      }
    }
  }

  const dig = (intent: string, query: string, queryId: string, action: string) => {
    const params = { 
      query, queryId, 
      click_result: {
        intent, 
        url: action
    }};
    if (intent) {
      track(41827, params);
    }
    // 仅预测卡片，埋点84989
    if (isPredictionCard) {
      track(84989, params);
    }
  }

  return (
    <ComposeInView 
      as='div' 
      triggerOnce
      threshold={0.3}
      className="msg-card-base msg-item-card"
      style={{ maxWidth: isPc ? "40%" : "65%" }}
      onChange={(inView) => {
        inView && isPredictionCard && track(84988)
      }
    }>
      <div className="card-wrapper">
        <div className="title">{ title }</div>
        <div className="content">
          { summary }
        </div>
      </div>
      <div className="msg-card-btns">
        {buttons.map((button: Button, index: Number) => {
          const { text, textColor, action: scheme } = button;
          return <div
            key={text}
            style={{color: textColor}}
            className="mag-card-btn"
            onClick={() => {handleCardClick(scheme);dig(intent, query, queryId, scheme)}}
          >
            {text || "--"}
          </div>})
        }
      </div>
    </ComposeInView>
  )
}

export default connect(mapStateToProps)(Card1)