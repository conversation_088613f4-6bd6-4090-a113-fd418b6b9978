import React, { useState, useContext, useEffect } from "react";
import { connect } from "react-redux";
import { ImContext, MsgListContext } from "@/pages/im/context";
import ImA<PERSON> from "@/common/service/im";
import {
  ADD_NEW_MSGS,
  UPDATE_MSG_MAP
} from "@/common/store";
import SkillModal from "@/pages/im/components/msg-section/components/skill-modal";
import { escape } from "lodash";


const mapStateToProps = (state: any) => ({
  msgList: state.im.msgList,
  userInfo: state.user.userInfo
});

const mapDispatchToProps = (dispatch: any) => ({
  addNewMsgs: (msgList: any[]) => dispatch({ type: ADD_NEW_MSGS, payload: msgList }),
  updateMsgMap: (list: any[]) => dispatch({type: UPDATE_MSG_MAP, payload: list}),
});

let timeout: NodeJS.Timeout = null;

function CardNegative9(props: any): JSX.Element{

  const { data: { uiModel = {} } } = props;
  const { items } = uiModel;
  const [skillList, setSkillList] = useState([]);
  const [skillModalVisible, setSkillModalVisible] = useState(false);
  const { imConfig, urlParams, isPc } = useContext(ImContext);


  useEffect(() => {
    setSkillModalVisible(true);
    setSkillList(items);
    return clearTimer;
  }, []);

  // 滚到底部
  const scrollToBottom = () => {
    const bottomDom = document.getElementById("bottomPlaceholder");
    if (!bottomDom) return;
    timeout = setTimeout(() => {
      bottomDom.scrollIntoView(true);
      clearTimeout(timeout);
    }, 0);
  };

  const addNewMsgs = (addList: any[]) => {
    props.addNewMsgs(addList);
    props.updateMsgMap(addList);
    scrollToBottom();
  };

  const addSingleMsg = (msgType: number, msgPayload: any, direction?: number) => {
    let data = msgPayload;
    if (typeof msgPayload === "string") {
      try {
        if (typeof JSON.parse(msgPayload) === "object") {
          data = JSON.parse(msgPayload);
        }
      } catch (err) {
        // ignore
      }
    }
    const msg = {
      id: Math.random(),
      direction: direction === undefined ? 1 : direction,
      type: msgType,
      data,
      manual: false // 为了direction不为1时头像为机器头像
    };
    if (msg.type == -1) {
      msg.data = escape(msg.data);
    }
    addNewMsgs([msg]);
  };

  const sendMsg = (msg: any) => {
    const data = {
      spm: urlParams.spm,
      fromUcid: props.userInfo.ucId,
      msgType: -1,
      msgPayload: "",
      sendTime: Date.now(),
      channel: imConfig.bizInfo.bizChannel,
      clientId: imConfig.baseInfo.clientId,
      ...msg
    };
    const { msgType, msgPayload } = data;

    addSingleMsg(msgType, msgPayload);

    window.dt?.sendInfo?.("发送消息3", {
      ...data
    })
    ImApi.sendMsg(data).catch(x => x);
  };

  const clearTimer = () => {
    if (timeout) {
      clearTimeout(timeout);
    }
  };

  const showSkillModal = (list: any[]) => {

    // 处理单一技能组选择否时，弹出技能组选择 modal 的情况
    if (list && list.length) {
      setSkillList(list);
      setSkillModalVisible(true);
      return;
    }

    const data = {
      spm: urlParams.spm,
      fromUcid: props.userInfo.ucId,
      channel: imConfig.bizInfo.bizChannel
    };

    ImApi.getSkillInfo(data)
      .then((data: any) => {
        if (!data) return;

        // 返回多个技能组
        if (Array.isArray(data)) {
          setSkillList(data);
          setSkillModalVisible(true);
          return;
        }

        // 只有一个技能组则会变成一条意图消息
        addNewMsgs([{ type: "fe02", data, id: Math.random(), manual: false }]);
      }).catch(x => x);
  };

  return (<MsgListContext.Provider value={{
      addNewMsgs,
      addSingleMsg,
      sendMsg,
      showSkillModal
    }}>
      <SkillModal
        isPc={isPc}
        modalVisible={skillModalVisible}
        skillList={skillList}
        setModalVisible={setSkillModalVisible}
      />
    </MsgListContext.Provider>
  )
}

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(CardNegative9);