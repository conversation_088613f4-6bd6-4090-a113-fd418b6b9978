.msg-card-base {
  .msg-card-title-status {
    white-space: nowrap;
    background: #ecefef;
    display: inline-block;
    padding: 0 4px;
    border-radius: 4px;
    color: #b0b3b6;
    font-weight: 400;
    font-size: 14px;
    margin-left: 4px;
  }
  
  .content {
    .msg-card-content-item {
      display: flex;
      font-size: 14px;
      list-style-type: none;
      &-left {
        flex: none;
        color: #8c8c8c;
      }
      &-right {
        color: #434343;
      }
    }
  }

  .msg-card-btns {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    width: 100%;
    height: 40px;
    line-height: 40px;
    text-align: center;
    .mag-card-btn {
      flex: 1;
      &:not(:last-child) {
        border-right: 1px solid #e8e8e8;
      }
    }
  }
}