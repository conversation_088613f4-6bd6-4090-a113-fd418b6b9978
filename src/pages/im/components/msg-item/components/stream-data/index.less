

  @import "../../../../../../styles/variable.less";
  .question-list-wrapper {
    &.qlist {
      flex-direction: column;
      align-self: flex-start;
      padding: 0;
      width: 100%;
    }
  
    .tips,
    .question-item {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding: 7px;
      width: 100%;
      border-bottom: 1px solid @border-base;
      cursor: pointer;
      word-break: break-all;
      color: #0984F9;
      
    }

    .question-item .svg {
        color: inherit;
        display: block;
        vertical-align: middle;
    }
  
    .tips {
      border: none;
      color: #999;
    }
  
    .question-list {
      display: flex;
      flex-direction: column;
      width: 100%;
    }
  
    .load-more {
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      color: @link-blue;
    }
  
    .content-wrapper {
      box-sizing: border-box;
      padding: 8px 12px;
      width: 100%;
      border-bottom: 1px solid @border-base;
      
      .title {
        margin-bottom: 8px;
        font-weight: 600;
      }
  
      .content {
        word-break: break-all;
  
        .load-detail {
          color: @link-blue;
          font-size: 13px;
        }
      }
    }
  
    .load-detail-btn {
      width: 100%;
      height: 40px;
      line-height: 40px;
      color: @font-blue;
      text-align: center;
    }

    .relKnowledgeTitle {
        margin-bottom: 5px;
    }
  }