
import React, { Component, useState } from "react";
import { track } from "@/common/tracks/actions/im";
import MarkdownComponent from "../markdown"
import "./index.less";

interface PropTypes {
  id: string,
  data: any,
  type: string,
}

interface QuestionType {
    id: string,
    title: string,
    content?: string,
    index: number,
    queryId?: string;
  }

const Question = (props: QuestionType) => {
    return (
      <div className="question-item" onClick={() => showDetailModal(props)}>
        {props.title}
        {/* <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="m5.27 11.812 5.266-5.266v4.6c0 .368.301.67.669.67a.664.664 0 0 0 .665-.664V4.938a.664.664 0 0 0-.665-.664l-6.213-.01a.664.664 0 1 0 0 1.33l4.6.01-5.265 5.265a.669.669 0 0 0 0 .943c.26.259.684.259.943 0Z"></path></svg> */}
      </div>
    );
};

const showDetailModal = (props: QuestionType) => {
    const knowledgeId = props.id
    track(41826, {
      click_result: props.title,
      knowledge_id: knowledgeId,
      query_id: props.queryId,
    });
    ImBus.emit("SHOW_KONWLEDGE_MODAL", { id: knowledgeId });
};

function StreamData(props: PropTypes): JSX.Element {
    const { content, queryId, knowledgeList } = props.data

    return (
      <>
        <div className="msg-card-base " >
            <MarkdownComponent content={content} />
            {
                knowledgeList && knowledgeList.length > 0 &&
                <div className="qlist question-list-wrapper">
                  <div className="question-list">
                    <h6 className="relKnowledgeTitle">相关内容：</h6>
                    {knowledgeList.map((item, index) => <Question index={index} key={item.id + index} queryId={queryId} {...item} />)}
                  </div>
              </div>
            }
        </div> 
      </>
    )
}

export default StreamData