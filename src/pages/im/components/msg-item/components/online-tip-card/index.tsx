/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-04-07 15:24:49
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-09 10:49:31
 */

import React from "react";
import { replaceProtocol } from "@/common/utils"
import ServiceAvatar from "@/common/assets/image/service.jpg";

import "./index.less";

interface PropTypes {
  avatar: string,
  data: {
    title: string,
    groupName: string
  }
}

function OnlineTipCard(props: PropTypes): JSX.Element {
  const avatar = props.avatar || ServiceAvatar;
  return (
    <div className="msg-card-base online-wrapper">
      <img className="avatar" src={replaceProtocol(avatar)} />
      <div className="content">
        <div className="text">{ props.data.title }</div>
        <div className="group">{ props.data.groupName }</div>
      </div>
    </div>
  )
};

export default OnlineTipCard;