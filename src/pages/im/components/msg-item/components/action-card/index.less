@import "../../../../../../styles/variable.less";

.action-card-wrapper {
  flex-direction: column;
  
  &.msg-card-base {
    padding: 0;
  }

  .action-content {
    padding: 10px 12px
  }

  .action-button {
    display: flex;
    width: 100%;
    border-top: 1px solid @border-base;

    .btn-item {
      padding: 12px 5px;
      width: 50%;
      white-space: nowrap;
      background: none;
      font-weight: 500;

      &.btn-active {
        color: @font-blue;
      }

      &.btn-inactive {
        color: @font-gray;
      }

      &:last-child {
        border-left: 1px solid @border-base;
      }
    }
  }
}