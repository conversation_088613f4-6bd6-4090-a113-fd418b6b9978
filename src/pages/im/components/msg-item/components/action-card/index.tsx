/*
 * @Author: liuzhenkun002
 * @Date: 2021-03-29 11:44:29
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-20 16:50:41
 */

import React, { useState, useContext } from "react";
import jsBridge from "@/common/bridge";
import ImApi from "@/common/service/im";
import { track } from "@/common/tracks/actions/im";
import { ImContext, MsgListContext } from "../../../../context";
import "./index.less";

interface PropTypes {
  type: string,
  data: {
    card: {
      cardTitle: string,
      cardContent: string,
      icon: string,
      url: string
    }
    menu: {
      name: string,
      key: string,
      type: string,
      callUrl: string
    },
    query: string,
    queryId: string,
    results: any,
  },
  msgAttr: string,
  id: string,
  userInfo: any
}

function ActionCard(props: PropTypes): JSX.Element {
  const [curActive, setCurActive] = useState(0);
  const { addNewMsgs, addSingleMsg, showSkillModal, isHistory } = useContext(MsgListContext);
  const { imConfig, urlParams } = useContext(ImContext);

  const setAction = (action: number, next: Function) => {
    if (isHistory || curActive > 0) return;
    setCurActive(action);
    if (next) next();
    track(50036, { 
      cardType: props.data.menu.type, 
      buttonType: action, 
      from: props.msgAttr || ""
    });
  };

  const normalYes = () => {
    const {
      card: { cardContent, cardTitle, icon, url },
      query,
      queryId
    } = props.data;

    addNewMsgs([
      {
        type: "fe01",
        data: {
          title: cardTitle,
          content: cardContent,
          icon,
          url,
          query,
          queryId
        },
        id: Math.random()
      }
    ]);
  };

  const normalNo = () => {
    const {results} = props.data;
    addNewMsgs([
      {
        type: "-200", //普通问答列表
        data: { results },
        id: Math.random()
      }
    ]);
  };

  const askCallback = (choose: boolean) => {
    const data = { 
      spm: urlParams.spm,
      fromUcid: props.userInfo.ucId,
      channel: imConfig.bizInfo.bizChannel,
      choose,
      key: props.data.menu.key
    };

    ImApi.askCallback(data)
      .then((data: any) => {
        if (choose) return;
        // 如果询问单个技能组选否，则弹出技能组选择弹窗
        ImBus.emit("SHOW_SKILL_MODAL", [data]);
      }).catch(x => x);
  };

  // TODO 有没有更好的办法功能解耦？
  const genAction = (): any => {
    let actionYes, actionNo;
    switch(props.type) {
      case "-112": // 进线人工卡片
        if (props.data.menu.type === "im") {
          actionYes = showSkillModal;
          actionNo = () =>
            addSingleMsg(-1, "看来您和客服小妹擦肩而过，期待下次为您服务。", 2);
        }
        if (props.data.menu.type === "multi") {
          actionYes = () => {
            jsBridge.openPage(props.data.menu.callUrl);
          };
          actionNo = showSkillModal;
        }
        break;
      case "-115": // 意图卡片
        actionYes = normalYes;
        actionNo = normalNo;
        break;
      case "fe02":
        actionYes = () => askCallback(true);
        actionNo = () => askCallback(false);
        break;
      default:
        break
    }
    return { actionYes, actionNo };
  };

  const { actionYes, actionNo } = genAction();

  const genBtnClass = (btnType: number) => {
    if (isHistory) return "btn-inactive";
    if (curActive === 0) return "";
    return curActive === btnType ? "btn-active" : "btn-inactive";
  };

  return (
    <div className="msg-card-base action-card-wrapper">
      <div className="action-content">
        { props.data.menu.name }
      </div>
      <div className="action-button">
        <button
          className={`btn-item ${genBtnClass(2)}`}
          onClick={() => setAction(2, actionNo)}
        >
          {props.data.menu.type === "multi" ? "在线人工客服" : "否"}
        </button>
        <button
          className={`btn-item ${genBtnClass(1)}`}
          onClick={() => setAction(1, actionYes)}
        >
          {props.data.menu.type === "multi" ? "语音人工客服" : "是"}
        </button>
      </div>
    </div>
  );
};

export default ActionCard;
