@import "../../../../../../styles/variable.less";

.msg-item-card {
  background-color: #FFF !important;
  &.msg-card-base {
    flex-direction: column;
    align-items: flex-start;
    padding: 0;
    max-width: 80%;
    width: 80%;
  }

  .card-wrapper {
    box-sizing: border-box;
    padding: 8px 12px;
    width: 100%;
    border-bottom: 1px solid @border-base;
    
    .title {
      margin-bottom: 8px;
      font-weight: 600;
    }

    .content {
      word-break: break-all;

      .load-detail {
        color: @link-blue;
        font-size: 13px;
      }
    }
  }

  .load-detail-btn {
    width: 100%;
    height: 40px;
    line-height: 40px;
    color: @font-blue;
    text-align: center;
  }
}