/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-04-15 18:50:54
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-15 19:29:35
 */

import React from "react";

import "./index.less";

interface PropTypes {
  type: string,
  data: {
    title: string,
    content: string,
    url: string
  }
}

function KnowledgeCard(props: PropTypes): JSX.Element {
  const { title, content, url } = props.data;
  const showDetailModal = (jumpUrl: string) => {
    ImBus.emit("SHOW_KONWLEDGE_MODAL", { url: jumpUrl });
  };
  return (
    <div className="msg-card-base msg-item-card">
      <div className="card-wrapper" onClick={() => showDetailModal(url)}>
        <div className="title">{ title }</div>
        <div className="content">
          { content }
        </div>
      </div>
      <div className="load-detail-btn" onClick={() => showDetailModal(url)}>查看详情</div>
    </div>
  );
};

export default KnowledgeCard;