/* 
 *   Typora Theme - Lapis  /  Author - YiN<PERSON> 
 *   https://github.com/YiNNx/typora-theme-lapis
 */

 :root {
    --text-color: rgb(58 64 73 / 97%);
    --primary-color: #4870ac;
    --block-background-color: #f6f8fa;
    --marker-color: #a2b6d4;
    --control-text-hover-color: #a2b6d4;
  }
 
  /*
   * Basic Style
   */
  
  .markdown {
    //   margin-left: 30px;
      padding: 19px;
      max-width: 950px;
      font-size: 1.1rem;
      // color: var(--text-color);
      line-height: 1.6;
      word-spacing: 0px;
      letter-spacing: 0px;
      word-break: break-word;
      word-wrap: break-word;
      text-align: justify;
      
      /* font-family: 'Cantarell','Source Han Serif CN','JetBrains Mono'; */
      font-family: 'Cantarell','SourceHanSerifCN','JetBrainsMono';
    //   margin-bottom: 20rem;

  }
  
  /* Strong */
  
  .markdown strong {
    color:var(--primary-color);
  }
  
  /* Link */
  
  .markdown a  {
    color: var(--primary-color);
  }
  .markdown a .copy  {
    color: lightsteelblue;
  }
  
  
  .markdown .md-p a,
  .markdown .md-heading a {
    text-decoration: none;
    word-wrap: break-word;
    border-bottom: 1px solid var(--primary-color);
    margin: 2px;
  }
  
  [md-inline=url], [md-inline=link]>.md-content, [md-inline=image]>.md-meta {
    word-break: break-all;
    font-weight: normal;
    font-family: 'JetBrainsMono';
    padding-left: 0.15rem;
    padding-right: 0.15rem;
    color: #a2b6d4;
  }
  
  /* 
   * TOC
   */
  
  .md-toc-content {
    font-family: 'SourceHanSerifCN';
  }
  
  /* 
   * Paragraph
   */
  
  .markdown + p, 
  .markdown blockquote p {
    font-size: 1.1rem;
    padding-top: .2rem;
    padding-bottom: .2rem;
    margin: 0;
    line-height: 1.8rem;
    // color: var(--text-color);
    color: #8b8b8b;
  }
  
  .markdown div[mdtype=toc] {
    font-size: 1.1rem;
  }
  
  
  
  /*
   * Header
   */
  
  .markdown h1,
  .markdown h2,
  .markdown h3,
  .markdown h4,
  .markdown h5,
  .markdown h6 {
    font-family: 'SourceHanSerifCN';
    padding: 0px;
    // color: var(--primary-color);
  }
  
  .markdown h1 {
    text-align: center;
  }
  
  .markdown h2 span.md-plain {
    background: var(--primary-color);
    padding: 1px 15px 2px;
    border-radius: 4px;
    font-weight: bold;
    color: #ffffff;
  }
  
  .markdown h2 span.md-link {
   background: var(--primary-color);
   padding: 1px 11px 6px;
   border-radius: 4px;
  }
  
  .markdown h2 span.md-link .md-plain {
    background: #ffffff00;
    padding: 1px 0px 3px;
  }
  
  .markdown h2 span.md-content {
   color: #ffffff;
  }
  
  .markdown h2.md-heading a {
    border-bottom: 1px solid white;
  }
  
  @media print{
    .markdown h2 span {
       display: inline-block;
       font-weight: bold;
       background: var(--primary-color);
       color: #ffffff;
       padding: 1px 15px 1px;
       border-radius: 4px;
       margin-right: 3px;
      }
  }
  
  .markdown h1 { font-size: 2rem; }
  .markdown h2 { font-size: 1.5rem; }
  .markdown h3 { font-size: 1.4rem; }
  .markdown h4 { font-size: 1.2rem; }
  .markdown h5 { font-size: 1.1rem; }
  .markdown h6 { font-size: 1.1rem; }
  
  .markdown h1 {
    padding-top: 0.9rem;
    margin-bottom: 2.3rem;
  }
  
  .markdown h2 {
    margin: 1.2em 0 1.2em;
  }
  
  .markdown h3 { margin: 1em 0 1em; }
  .markdown h4 { margin: 0.8em 0 0.8em; }
  .markdown h5 { margin: 0.6em 0 0.6em; }
  .markdown h6 { margin: 0.4em 0 0.4em; }
  
  /*
   * List
   */
  
  ::marker {
    color: #a8a8a8;
    // font-weight: bold;
    // color: var(--marker-color);
  }
  
  li.md-list-item {
    margin: 0.4rem 0;
  }
  
  .markdown ul,
  .markdown ol {
    margin-top: 8px;
    margin-bottom: 8px;
    padding-left: 20px;
  }
  
  .markdown ul {
    list-style-type: disc;
  }
  
  .markdown em {
    padding: 0 3px 0 0;
  }
  
  .markdown ul ul {
    list-style-type: square;
  }
  
  .markdown ol {
    list-style-type: decimal;
  }
  
  .markdown li section {
    margin-top: 5px;
    margin-bottom: 5px;
    line-height: 1.7rem;
    text-align: justify;
    color: var(--text-color); 
    font-weight: 500;
  }
  
  /*
   * Quote
   */
  .markdown blockquote {
    border-left: 2px solid #e5e5e5;
    color: #8b8b8b;
    white-space: normal;
    margin: 0;
    padding: 0 0 0 13px;
    line-height: 26px;
    position: relative;
  }
  
  /*
   * Inline code
   */
  
//   .markdown code {
//     color: var(--primary-color);
//     font-size: 94%;
//     font-weight: normal;
//     word-wrap: break-word;
//     padding: 2px 4px 2px;
//     border-radius: 3px;
//     margin: 2px;
//     background-color:  var(--block-background-color);
//     font-family: 'JetBrainsMono';
//     word-break: break-all;
//   }
  
  /*
   * Img
   */
  .markdown img {
    margin: 0 auto;
    max-width: 100%;
  }
  
  .markdown span img {
    filter: drop-shadow(#d2dff4 0px 0px 8px);
    border-radius: 3rem;
    display: block;
    margin: 0 auto;
    padding: 2rem;
  }
  
  /*
   * Table
   */
  .markdown table {
    display: table;
    text-align: justify;
    overflow-x: auto;
    border-collapse: collapse;
    border-spacing: 0px;
    font-size: 1em;
    margin: 0px 0px 20px;
    width: 100%;
    background-color: white;
  }
  
  .markdown tbody {
    border: 0;
  }
  .markdown table tr {
    border: 0;
    border-top: 1px solid #ccc;
  }
  
  .markdown table tr th,
  .markdown table tr th, .markdown table tr td {
    font-size: 1rem;
    border: 1px solid #d9dfe4;
    padding: 5px 10px;
    text-align: justify;
  }
  .markdown table tr th {
    text-align: center;
    font-weight: bold;
    color: var(--primary-color);
  }
  
  table.md-table td {
    min-width: 32px;
  }
  
  /*
   * Footnote Superscript
   */
  .markdown .md-footnote {
   font-weight: bold;
  }
  .markdown .md-footnote > .md-text:before {
   content: '[';
  }
  .markdown .md-footnote > .md-text:after {
   content: ']';
  
  }
  
  /* Footnote */
  
  .footnotes {
    opacity: 1;
    font-size: 0.95rem;
  }
  
  .markdown .md-def-name {
    padding-right: 1.8ch;
  }
  .markdown .md-def-name:before {
    content: '[';
    color: var(--text-color);
  }
  .markdown .md-def-name:after {
    color: var(--text-color);
  }
  
  span.md-plain {
      line-height: 2rem;
  }
  
  p.md-end-block.md-p {
      line-height: 2rem;
      color: var(--text-color);
      margin: 4px 0;
  }
  
  /* 
   * Comment
   */
  .md-comment {
    color: #b4bad4;
    opacity: 1;
    font-family: 'JetBrainsMono';
  }
  
  /*
   * Dividing line
   */
  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 2px solid #eef2f5;
    border-radius: 2px;
  }
  
  /* Checkbox */
  
  .markdown input[type=checkbox] {
    width: 0;
  }
  
  .task-list-item input::before {
    content: "";
    display: inline-block;
    width: 1.0125rem;
    height: 1.0125rem;
    vertical-align: middle;
    text-align: center;
    border: 1px solid var(--marker-color);
    border-radius: 1.2rem;
    background-color: #fdfdfd;
    margin-left: -0.1rem;
    margin-right: 0.1rem;
    margin-top: -.6rem;
  }
  
  
  .task-list-item input:checked::before {
    content: '✓';
    font-weight: bold;
    -webkit-text-stroke: 1px var(--primary-color);
    color: var(--primary-color);
    background-color: white;
    font-size: 0.75rem;
    line-height: 0.8rem;
  }
  
  /*
   * Sidebar
   */
  #typora-sidebar {
    font-family: 'Cantarell','Source Han Serif CN';
    height: 100%;
    color: #686d75;
    font-size: 0.92rem;
    background-color: white;
  }
  
  #sidebar-content.sidebar-content {
    margin-top: .5rem;
  }
  
  /*
   * Sidebar - FileNode
   */
  
  .active-tab-files #info-panel-tab-file .info-panel-tab-border,
  .active-tab-outline #info-panel-tab-outline .info-panel-tab-border,
  .ty-show-search #info-panel-tab-search .info-panel-tab-border {
    border-radius:10px;
    height: 1px;
    background-color: var(--primary-color);
  }
  
  .file-node-content {
     color: var(--primary-color); 
     line-height: 1.6rem;
    }
  
  span.file-node-title-name-part  { color: var(--text-color); }
  span.file-node-title-ext-part { color: var(--text-color); }
  .file-node-icon { padding-right: 0.2rem; }
  
  .file-tree-node.active>.file-node-background {
    background-color: var(--block-background-color);
    border-left: 4px solid var(--primary-color);
    border-color: var(--primary-color);
  }
  
  /*
   * Sidebar - Outline
   */
  
  #outline-content {
    height: 100%;
    overflow-x: hidden;
    line-height: 1.4rem;
    font-size: .96rem;
  }
  
  .outline-item {
    padding-left: 2em;
  }
  
  /* Meta Block */
  
  pre.md-meta-block {
    font-family: 'JetBrainsMono';
    color: var(--primary-color);
    background: #f6f8fa;
    padding: 1.5rem;
    margin: -37px 0rem 3.8rem;
    /* border-radius: 1rem; */
    filter: drop-shadow(#d2dff4 0px 3px 3px);
    /* border-top: 1px solid var(--primary-color); */
    /* border-bottom: 1px solid var(--primary-color); */
  }
  
  /*
   * Scrollbar
   */
  
  #outline-content::-webkit-scrollbar { width: 0; }
  #file-library::-webkit-scrollbar { width: 0; }
  
  ::-webkit-scrollbar-track {
    border-radius: 10px;
  }
  
  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: rgba(179, 179, 179, 0.425);
  }
  
  ::-webkit-scrollbar {
    width :1rem;
  }
  
  /* 
   * Code Block - Style
   */
  
  .md-fences:before {
    content: ' ';
    display: block;
    width: 100%;
    background-size: 40px;
    background-repeat: no-repeat;
    background-color: #282c34;
    margin-bottom: -7px;
    border-radius: 5px;
    background-position: 10px 10px;
  }
  
  .CodeMirror-wrap .CodeMirror-scroll {
    overflow-x: auto;
  }
  .cm-s-inner.CodeMirror {
    margin: 2rem 0;
    padding: 1.2rem .8rem;
    color: #4f5467;
    font-family: 'JetBrainsMono';
    border-radius: 10px;
    background-color: var(--block-background-color);
    /* border: 1px solid #eef2f5;*/
    line-height: 1.6rem;
  }
  
  .CodeMirror-gutters {
    border-right: 1px solid #a2b6d452;
    background: inherit;
    white-space: nowrap;
  }
  
  pre.CodeMirror-line {
    padding: 0 1.2rem;
  }
  
  .CodeMirror-linenumber {
    padding: 0 3px 0 5px;
    text-align: right;
    color: var(--primary-color);
  }
  
  /* 
   * Code Block - Color Scheme
   */
  
  .cm-s-inner .cm-keyword {
    color: #1694b6 !important;
  }
  .cm-s-inner .cm-operator {
    color: #2f86d2 !important;
  }
  .cm-s-inner .cm-variable,
  .cm-s-inner .cm-builtin,
  .cm-s-inner .cm-header,
  .cm-s-inner .cm-tag,
  .cm-s-inner .cm-property,
  .cm-s-inner .cm-quote {
    color: #b9218e !important;
  }
  
  .cm-s-inner .cm-variable-2 {
    color: #7aadad !important;
  }
  .cm-s-inner .cm-variable-3,
  .cm-s-inner .cm-type,
  .cm-s-inner .cm-atom {
    color: #378ed8 !important;
  }
  
  .cm-s-inner .cm-number {
    color: #1a5494 !important;
  }
  .cm-s-inner .cm-def,
  .cm-s-inner .cm-qualifier {
    color: #0744ac !important;
  }
  .cm-s-inner .cm-string {
    color: #6f42c2 !important;
  }
  .cm-s-inner .cm-string-2 {
    color: #27638f !important;
  }
  .cm-s-inner .cm-comment {
    color: #9a9a9a !important;
  }
  
  .cm-s-inner .cm-meta {
    color: #0b93be !important;
  }
  .cm-s-inner .cm-attribute {
    color: #8f6aa8 !important;
  }
  
  .cm-s-inner .cm-error {
    color: rgba(255, 255, 255, 1) !important;
    background-color: #b9218e40 !important;
  }
  .cm-s-inner .CodeMirror-matchingbracket {
    text-decoration: underline;
    color: white !important;
  }
  .CodeMirror div.CodeMirror-cursor {
    border-left: 1px solid var(--primary-color);
    z-index: 3;
  }
  
  .cm-s-inner div.CodeMirror-selected {
    background: rgba(167, 178, 189, 0.2) !important;
  }
  
  .cm-s-inner.CodeMirror-focused div.CodeMirror-selected {
    background: rgba(167, 178, 189, 0.2) !important;
  }
  
  .cm-s-inner .CodeMirror-selected,
  .cm-s-inner .CodeMirror-selectedtext {
    background-color: rgba(167, 178, 189, 0.0) !important;
  }
  
  .cm-s-inner .CodeMirror-line::-moz-selection,
  .cm-s-inner .CodeMirror-line > span::-moz-selection,
  .cm-s-inner .CodeMirror-line > span > span::-moz-selection {
    background-color: rgba(167, 178, 189, 0.2);
  }
  
  .cm-s-inner .CodeMirror-line::selection,
  .cm-s-inner .CodeMirror-line > span::selection,
  .cm-s-inner .CodeMirror-line > span > span::selection {
    background-color: rgba(167, 178, 189, 0.2);
  }
  
  .markdown .codeheader {
    position: absolute;
    top: 5px;
    right: 0;
    width: 100%;
    padding: 0 1rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: #b3b3b3;
  }
  .markdown .codeheader .copy-btn {
    color: white;
    cursor: pointer;
  }
  .markdown pre {
    position: relative;
    margin-top: 5px;
    margin-bottom: 5px;
  }

// .hljs {
//     display: block;
//     overflow-x: auto;
//     padding: 0.5em;
//     color: #abb2bf;
//     background: #282c34;
//     border-radius: 6px;
//   }
  
//   .hljs-comment,
//   .hljs-quote {
//     color: #5c6370;
//     font-style: italic;
//   }
  
//   .hljs-doctag,
//   .hljs-keyword,
//   .hljs-formula {
//     color: #c678dd;
//   }
  
//   .hljs-section,
//   .hljs-name,
//   .hljs-selector-tag,
//   .hljs-deletion,
//   .hljs-subst {
//     color: #e06c75;
//   }
  
//   .hljs-literal {
//     color: #56b6c2;
//   }
  
//   .hljs-string,
//   .hljs-regexp,
//   .hljs-addition,
//   .hljs-attribute,
//   .hljs-meta .hljs-string {
//     color: #98c379;
//   }
  
//   .hljs-attr,
//   .hljs-variable,
//   .hljs-template-variable,
//   .hljs-type,
//   .hljs-selector-class,
//   .hljs-selector-attr,
//   .hljs-selector-pseudo,
//   .hljs-number {
//     color: #d19a66;
//   }
  
//   .hljs-symbol,
//   .hljs-bullet,
//   .hljs-link,
//   .hljs-meta,
//   .hljs-selector-id,
//   .hljs-title {
//     color: #61aeee;
//   }
  
//   .hljs-built_in,
//   .hljs-title.class_,
//   .hljs-class .hljs-title {
//     color: #e6c07b;
//   }
  
//   .hljs-emphasis {
//     font-style: italic;
//   }
  
//   .hljs-strong {
//     font-weight: bold;
//   }
  
//   .hljs-link {
//     text-decoration: underline;
//   }
  pre.hljs
   {
    display: block;
    overflow-x: auto;
    padding: 1em;
    border-radius: 6px;
  }
  code.hljs {
    display: block;
    overflow-x: auto;
    padding: 1em;
    border-radius: 6px;
  }
  
//   code.hljs {
//     padding: 3px 5px;
//   }
  
  .hljs {
    // color: #e9e9f4;
    color: white;
    background: #282936;
  }
  
  .hljs::selection,
  .hljs ::selection {
    background-color: #4d4f68;
    color: white;
  }
  
  
  /* purposely do not highlight these things */
  .hljs-formula,
  .hljs-params,
  .hljs-property
  {}
  
  /* base03 - #626483 -  Comments, Invisibles, Line Highlighting */
  .hljs-comment {
    color: #626483;
  }
  
  /* base04 - #62d6e8 -  Dark Foreground (Used for status bars) */
  .hljs-tag {
    color: #62d6e8;
  }
  
  /* base05 - #e9e9f4 -  Default Foreground, Caret, Delimiters, Operators */
  .hljs-subst,
  .hljs-punctuation,
  .hljs-operator {
    color: #e9e9f4;
  }
  
  .hljs-operator {
    opacity: 0.7;
  }
  
  /* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
  .hljs-bullet,
  .hljs-variable,
  .hljs-template-variable,
  .hljs-selector-tag,
  .hljs-name,
  .hljs-deletion {
    color: #ea51b2;
  }
  
  /* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
  .hljs-symbol,
  .hljs-number,
  .hljs-link,
  .hljs-attr,
  .hljs-variable.constant_,
  .hljs-literal {
    color: #b45bcf;
  }
  
  /* base0A - Classes, Markup Bold, Search Text Background */
  .hljs-title,
  .hljs-class .hljs-title,
  .hljs-title.class_
  {
    color: #00f769;
  }
  
  .hljs-strong {
    font-weight:bold;
    color: #00f769;
  }
  
  /* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
  .hljs-code,
  .hljs-addition,
  .hljs-title.class_.inherited__,
  .hljs-string {
    color: #ebff87;
  }
  
  /* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
  .hljs-built_in,
  .hljs-doctag, /* guessing */
  .hljs-quote,
  .hljs-keyword.hljs-atrule,
  .hljs-regexp {
    color: #a1efe4;
  }
  
  /* base0D - Functions, Methods, Attribute IDs, Headings */
  .hljs-function .hljs-title,
  .hljs-attribute,
  .ruby .hljs-property,
  .hljs-title.function_,
  .hljs-section {
    color: #62d6e8;
  }
  
  /* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
  .hljs-type,
  /* .hljs-selector-id, */
  /* .hljs-selector-class, */
  /* .hljs-selector-attr, */
  /* .hljs-selector-pseudo, */
  .hljs-template-tag,
  .diff .hljs-meta,
  .hljs-keyword {
    color: #b45bcf;
  }
  .hljs-emphasis {
    color: #b45bcf;
    font-style: italic;
  }
  
  /* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
  .hljs-meta,
  /*
    prevent top level .keyword and .string scopes
    from leaking into meta by accident
  */
  .hljs-meta .hljs-keyword,
  .hljs-meta .hljs-string
  {
    color: #00f769;
  }
  
  .hljs-meta .hljs-keyword,
  /* for v10 compatible themes */
  .hljs-meta-keyword {
    font-weight: bold;
  }




  /* 行号样式 */
.line-numbers-rows {
  position: absolute;
  pointer-events: none;
  top: 18px;
  font-size: 100%;
  left: 0.5em;
  width: 2.1em;
  letter-spacing: -1px;
  border-right: 1px solid #0e0f12;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  background-color: #282c34;
}


.line-numbers-rows>span {
  display: block;
  counter-increment: linenumber;
}


.line-numbers-rows>span:before {
  content: counter(linenumber);
  color: #999;
  display: block;
  padding-right: 0.8em;
  text-align: right;
}

.language-name {
  position: absolute;
  top: 9px;
  color: #999999;
  right: 43px;
  font-size: 0.8em;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.copy-btn {
  position: absolute;
  right: 8px;
  top: 8px;
  background-color: #525252;
  border: none;
  padding: 3px 6px;
  border-radius: 3px;
  color: #cccccc;
  cursor: pointer;
  display: none;
}

pre:hover .copy-btn {
  display: block;
}

.copy-textarea {
  position: absolute;
  left: -9999px;
  top: -9999px;
}