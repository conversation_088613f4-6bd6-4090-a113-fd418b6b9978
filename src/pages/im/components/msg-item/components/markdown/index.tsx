import React, { useContext, useState, useEffect } from "react";
import markdownIt from 'markdown-it'
import hljs from 'highlight.js';
import mila from 'markdown-it-link-attributes';
import { randomId } from "@/common/utils"
import ClipboardJS from 'clipboard'
import { Toast } from "antd-mobile";
import xssFilter from "@/common/utils/xss";

import "./index.less";
interface PropTypes {
  content: string,
}

const md = new markdownIt({
    html: true,
    linkify: true,
    typographer: true,
    breaks: true,  
    // 设置代码高亮的配置
    highlight: function (code, language) {     
        const id = randomId()
        let copyBtn = `<div class="codeheader"><a class="copy-btn" title="复制" data-clipboard-action="copy" data-clipboard-target="#copy${id}"><span role="button" aria-label="copy"><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" focusable="false" aria-hidden="true"><path d="M7 4C7 2.89543 7.89543 2 9 2H20C21.1046 2 22 2.89543 22 4V15C22 16.1046 21.1046 17 20 17H19V8C19 6 18 5 16 5H7V4Z" fill="currentColor"></path><path d="M5 7C3.89543 7 3 7.89543 3 9V19C3 20.1046 3.89543 21 5 21H15C16.1046 21 17 20.1046 17 19V9C17 7.89543 16.1046 7 15 7H5Z" fill="currentColor"></path></svg></span></a></div>`
        let textAreaHtml = `<textarea class="copy-textarea" id="copy${id}">${code}</textarea>`
        let headerHtml = `<div>${copyBtn}${textAreaHtml}</div>`

        if (language && hljs.getLanguage(language)) {
          try {
            let highlightedHtml = hljs.highlight(code, { language: language }).value
            // return `<pre><code class="hljs language-${language}">` +
            //        hljs.highlight(code, { language  }).value +
            //        '</code></pre>';
            return `<pre>${headerHtml}<code class="language-${language} hljs">${highlightedHtml}</code></pre>`;
          } catch (__) {}
        }
    
        return `<pre class="hljs">${headerHtml}<code>${md.utils.escapeHtml(code)}</code></pre>`;
    }
})
  
md.use(mila, {
    attrs: {
      target: '_blank',
      rel: 'noopener noreferrer',
    },
});


const MarkdownComponent: React.FC<PropTypes> = ({ content }) => {
    const [htmlContent, setHtmlContent] = useState('');

    useEffect(() => {
      if (content) {
          const mk = content
                    .replace('\n><attempt_completion>', '')
                    .replace('\n\n><attempt_completion>', '')
                    .replace(/\\n\\n/g, '\n\n ')
                    .replace(/\\n/g, '\n ')
                    .replace(/<requirement_analysis>/g, '')
                    .replace(/<\/requirement_analysis>/g, '')
                    .replace(/<module_name>/g, '')
                    .replace(/<\/module_name>/g, '')
                    .replace('<thinking>', '')
                    .replace('</thinking>', '')
                    .replace('<attempt_completion>', '')
                    .replace('</attempt_completion>', '')
                    .replace('<intent>Q&A</intent>', '')
                    .replace('<intent>design</intent>', '')
                    .replace('<ask_followup_question>', '')
                    .replace('</ask_followup_question>', '')
                    ;

          
          setHtmlContent(md.render(mk));
          copyEvent();
      }
    }, [content]);

    const copyEvent = () => {
        var clipboard = new ClipboardJS('.copy-btn');
        clipboard.on('success', function (e) {
            Toast.info("复制成功", 1);
            e.clearSelection();
        });

        clipboard.on('error', function (e) {
            Toast.fail("复制失败，请稍后重试");
        });

        var clipboard2 = new ClipboardJS('.copy-share-url');
        clipboard2.on('success', function (e) {
            Toast.info("链接复制成功", 1);
            e.clearSelection();
        });

        clipboard2.on('error', function (e) {
            Toast.fail("复制失败，请稍后重试");
        });
    }
   
    return (
      <div className="mark">
        <div className="markdown" dangerouslySetInnerHTML={{ __html: htmlContent }} />
      </div>
    );
};

export default MarkdownComponent;
