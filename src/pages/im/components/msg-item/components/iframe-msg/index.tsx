import React from "react";
import { genUrlParams } from "@/common/utils/url";

import "./index.less";

interface PropTypes {
  isPc: boolean;
  data: {
    linkUrl: string
  };
  scrollToBottom: () => void;
}

export default function IframeItem(props: PropTypes): JSX.Element {

  const { data: { linkUrl } = {}, isPc } = props;
  // const [urlStatus, setUrlStatus] = useState(iframeStatus.EMPTY)
  const { ratio = "3:4" } = genUrlParams(linkUrl) || {};
  const width = isPc ? 42 : 90;
  let height = Math.ceil(width * 4 / 3);
  try {
    height = Math.ceil(width * ratio.split(":")[1] / ratio.split(":")[0]);
  } catch(err) {
    console.log(err)
  }
    return (<div 
    className="iframe-msg-item msg-card-base"
    style={{
      width: isPc ? `${width}vh` : `${width}vw`,
      height: isPc ? `${height}vh` : `${height}vw`
    }}
    >
      <iframe
        className="iframe-msg-content"
        src={linkUrl}
        style={{
          width: `${width}vh`,
          height:  `${height}vh`
        }}
      />
    </div>
  );
}

