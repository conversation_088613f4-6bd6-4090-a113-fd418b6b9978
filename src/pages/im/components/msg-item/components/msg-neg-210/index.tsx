import React, { useEffect, useState } from "react";
import { Modal } from "antd-mobile";
import { parseCookie } from "@/common/utils"
import "./index.less"

function MsgNeg210(props): JSX.Element {
  const { isPc, data } = props;
  const { linkUrl, target = "drawer", title, ratio = "3:4" } = typeof data === "string" ? JSON.parse(data) : data;
  const [modalShow, setModalShow] = useState(true);

  useEffect(() => {
    ImBus.on("CLOSE_ALL_PAGES", () => {
      setModalShow(false)
    })
  }, []) 

  const width = isPc ? 60 : 100;
  let height = Math.ceil(width * 4 / 3);
  try {
    height = Math.ceil(width * ratio.split(":")[1] / ratio.split(":")[0]);
  } catch(err) {
    console.log(err)
  }
  return target === "drawer" ? <Modal
        className={`iframe-modal ${isPc ? "iframe-pc-modal" : ""}`}
        transparent={isPc}
        maskClosable={false}
        popup={!isPc}
        visible={modalShow}
        onClose={() => setModalShow(false)}
        animationType="slide-up"
        style={{
          width: `${width}vw`,
          height: `${height}vw`
        }}
      >
        <div className="modal-header">
          <div className="btn-close" onClick={() => setModalShow(false)}>关闭</div>
          {title && <div className="title">{title}</div>}
        </div>
        {/* 占位，支持iframe按钮吸底 */}
        <div className="modal-header-placeholder" /> 
        <iframe className="iframe-page" frameBorder="0" src={`${linkUrl}&lianjia_token=${parseCookie().lianjia_token_sinan || parseCookie().lianjia_token || ""}`} />
      </Modal> : null
}

export default MsgNeg210;