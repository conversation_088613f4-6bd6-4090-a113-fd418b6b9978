@import "../../../../../../styles/variable.less";

.iframe-modal {
  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 50px;
    border-bottom: 1px solid @border-base;
    flex-direction: row-reverse;
    position: absolute;
    right: 0;
    width: calc(100% - 40px);
    background: #FFF;

    .title {
      font-size: 15px;
      font-weight: 500;
    }

    .btn-close {
      font-size: 14px;
      color: @font-blue;
      cursor: pointer;
    }
  }

  .modal-header-placeholder {
    height: 50px;
    width: 100%;
  }

  max-height: 90vh;

  .iframe-page {
    width: 100%;
    height: calc(100% - 50px);
  }

  // pc 兼容
  &.iframe-pc-modal {
    min-width: 500px;
  }
}