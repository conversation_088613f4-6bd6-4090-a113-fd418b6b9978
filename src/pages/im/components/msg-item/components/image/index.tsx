/*
 * @Author: liuzhenkun002
 * @Date: 2021-03-24 17:49:45
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-13 19:29:22
 */

import React, { useEffect } from "react";
import { connect } from "react-redux";
import { debounce } from "lodash";

import { SHOW_IMAGE_VIEWER } from "@/common/store";
import { getPreviewImgSizeInfoList } from "@/common/utils/file";
import ReplyMessage from "@/pages/im/components/reply-message";

import "./index.less";

interface PropTypes {
  data: {
    original: string
  },
  isClient: boolean,
  showImageViewer: Function
}

const mapDispatchToProps = (dispatch: any) => ({
  showImageViewer: (imageList: []) => dispatch({ type: SHOW_IMAGE_VIEWER, payload: imageList })
});

function ImageItem(props: PropTypes): JSX.Element {

  const imgClick = debounce(async (url: string) => {
    const imageList:any = await getPreviewImgSizeInfoList([{src: url}]);
    props.showImageViewer(imageList);
  }, 100);


  const imgLoad = () => {
    const { id, scrollToBottom, isHistoryMsg } = props;
    const dom = document.getElementById(`img-${id}`);
    if (dom) {
      const imgs = dom.getElementsByTagName("img")
      const lastImg = imgs[imgs.length - 1];
      if(lastImg && scrollToBottom && !isHistoryMsg){
        lastImg.onload = () => {
          scrollToBottom();
        }
      }
    }
  }

  useEffect(()=> {
    imgLoad();
  }, []);

  let msgAttr = props.msgAttr;
  try {
    if (typeof(msgAttr) === "string") {
      msgAttr = JSON.parse(msgAttr)
    }
  } catch (err) {
    console.log(err)
  }

  return (
    <div className={`msg-card-base image-wrapper ${props.isClient ? "client-msg" : ""}`} id={`img-${props.id}`}>
      {msgAttr?.replyMsgId && <ReplyMessage quoteId={msgAttr?.replyMsgId} />}
      <img src={props.data?.original} onClick={() => imgClick(props.data?.original)} />
    </div>
  );

}

export default connect(
  null,
  mapDispatchToProps
)(ImageItem);
