/*
 * @Author: liuzhenkun002
 * @Date: 2021-03-24 11:40:13
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-05-07 14:43:28
 */

import React, { Component } from "react";
import { connect } from "react-redux";
import onClickOutside from "react-onclickoutside";
import { Toast } from "antd-mobile";
import { debounce } from "lodash";
import ReplyMessage from "@/pages/im/components/reply-message";

import { SHOW_IMAGE_VIEWER, SET_ACTIVE_OPT_MSG } from "@/common/store";
import xssFilter from "@/common/utils/xss";
import { getPreviewImgSizeInfoList } from "@/common/utils/file";

import "./index.less";

interface PropTypes {
  id: string,
  activeOptMsg: string,
  data: any,
  type: string,
  showImageViewer: Function,
  setActiveMsg: Function,
  scrollToBottom: Function
}

const mapStateToProps = (state: any) => ({
  activeOptMsg: state.im.activeOptMsg
});

const mapDispatchToProps = (dispatch: any) => ({
  showImageViewer: (imageList: any) => dispatch({ type: SHOW_IMAGE_VIEWER, payload: imageList }),
  setActiveMsg: (index: number) => dispatch({ type: SET_ACTIVE_OPT_MSG, payload: index })
});

let timer: NodeJS.Timeout = null;

class RichText extends Component {
  constructor(props: PropTypes) {
    super(props);
  }

  onCopy(text: string) {
    let textArea = document.createElement("textarea");
    textArea.value = text.replace(/<p>|<\/p>|<br>|<br\/>/g, "");
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand("Copy", false, null);
    textArea.remove();
    Toast.info("复制成功", 1);
    this.props.setActiveMsg(null);
  };

  copyTouchStart() {
    this.props.setActiveMsg(null);
    // if (this.agent === "pc") return; // TODO

    timer = setTimeout(() => {
      this.props.setActiveMsg(this.props.id);
    }, 500);
  };

  copyTouchEnd() {
    if (timer) {
      clearTimeout(timer);
    }
  };

  componentDidMount() {
    const { id, scrollToBottom, isHistoryMsg } = this.props
    const dom = document.getElementById(`richtext-${id}`);
    if (dom) {
      const imgs = dom.getElementsByTagName("img")
      const lastImg = imgs[imgs.length - 1];
      if(lastImg && scrollToBottom && !isHistoryMsg){
        lastImg.onload = () => {
          scrollToBottom();
        }
      }
    }
    if (!this.refs.richTextRef) return;
    const msgDom: any = this.refs.richTextRef;
    msgDom.addEventListener("click",async (event: any) => {
      if (event.target.tagName === "IMG") {
        const imageList:any = await getPreviewImgSizeInfoList([{src: event.target.src}]);
        debounce(this.props.showImageViewer, 100)(imageList);
      }
    });
  }

  handleClickOutside = (event) => {
    if (event.target.innerText === "复制") return;
    this.props.setActiveMsg(null);
  };

  render() {
    const { id } = this.props;
    let { msgAttr } = this.props;
    try {
      if (typeof(msgAttr) === "string") {
        msgAttr = JSON.parse(msgAttr)
      }
    } catch (err) {
      console.log(err)
    }
    const content = this.props.type === "600" ? this.props.data.content.message : this.props.data;
    const isOnlyText = !/<[^>]+>/g.test(content); // 是否是纯文本
    return (
      <>
        <div
          className="msg-card-base rich-text-wrapper"
          onTouchStart={() => this.copyTouchStart()}
          onTouchEnd={() => this.copyTouchEnd()}
          id={`richtext-${id}`}
        >
          {msgAttr?.replyMsgId && <ReplyMessage quoteId={msgAttr?.replyMsgId} />}
          <pre ref="richTextRef" className="rich-text" dangerouslySetInnerHTML = {{ __html: xssFilter(content) }} />
        </div>
        { isOnlyText && this.props.id && this.props.activeOptMsg === this.props.id && (
          <div className="operation-box" onClick={() => this.onCopy(content.toString())}>复制</div>
        )}
      </>
    )
  }
}

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(onClickOutside(RichText));