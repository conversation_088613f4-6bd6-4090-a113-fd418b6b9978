import React from "react";
import { Card1, Card7, CardNegative9 } from "../card-item";

export interface Data {
  cardID: number,
  uiModel: any;
}

export interface CardProps {
  data: Data
}

function UNSupportCard(): JSX.Element{
  return <div className="msg-card-base rich-text-wrapper">
    <div className="rich-text">【暂不支持该类型卡片】</div>
  </div>
}

const cardMap: any = {
  1: Card1,
  7: Card7,
  "-9": CardNegative9,
  unsupport: UNSupportCard
}

function MSG331(props: CardProps):JSX.Element {
  let { data } = props;
  data = typeof data === "string" ? JSON.parse(data) : data;
  const { cardID } = data;
  const CurrCard = cardMap[cardID] || cardMap["unsupport"];
  return <CurrCard data={data} />
}

export default MSG331;