@import "../../../../chatgpt/msg-section/index.less";

.model-card-wrapper, .model-retry-wrapper {
  position: relative;
  background: #fff;
  border-radius: 4px;
  padding: 11px 12px;
  line-height: 20px;
  font-size: 14px;
}

.model-card-wrapper {
  width: 65%;

  .markdown {
    position: relative;
    margin-bottom: 12px;

    p {
      font-size: 14px;
    }

    .md-spread {
      position: absolute;
      right: 0;
      bottom: 2px;
      padding-right: 12px;
      font-weight: 500;
      color: #0984F9;
      font-size: 12px;
      background: url(https://file.ljcdn.com/psd-sinan-file/prod/audit_file/15A472665630400E97AB325608B5F629/12.png) no-repeat #fff;
      background-size: 12px 12px;
      background-position: right;
    }
  }

  .knowledge-list-wrapper {
    .list-title {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .list-content p {
      cursor: pointer;
      font-size: 12px;
      color: #0984F9;
      line-height: 14px;
      padding: 10px 0;
      border-bottom: 1px solid #F0F0F0;
    }

    .list-content p:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }

    .list-fold {
      padding-top: 10px;
      text-align: center;
      font-size: 14px;
      color: #0984F9;
      font-weight: 500;
      border-top: 1px solid #F0F0F0;
      margin-top: 10px;
    }
  }

  .like-wrapper {
    position: absolute;
    right: -40px;
    bottom: 0;

    .like, .unlike {
      width: 24px;
      height: 24px;
      background-color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .like {
      background: url("https://file.ljcdn.com/psd-sinan-file/prod/audit_file/6CB96D9E14BA41619610E6C40C3C1038/%E8%B5%9E-%E4%B8%8A%20(3).png") no-repeat #fff;
      background-position: center center;
      background-size: 16px 16px;
    }

    .unlike {
      margin-top: 8px;
      background: url("https://file.ljcdn.com/psd-sinan-file/prod/appeal_evidence/8BEC2F2A29FF42AFABEDDD2A7940103D/%E8%B5%9E-%E4%B8%8A%20(2).png") no-repeat #fff;
      background-position: center;
      background-size: 16px 16px;
    }

    .like-active {
      background: url(https://file.ljcdn.com/psd-sinan-file/prod/appeal_evidence/C82966A693B345CDA3C2F1E360D8CB86/%E8%B5%9E-%E4%B8%8A.png) no-repeat #fff;
      background-position: center center;
      background-size: 16px 16px;
    }

    .unlike-active {
      background: url("https://file.ljcdn.com/psd-sinan-file/prod/appeal_evidence/5C2EC58AED594C759B175465E5A410AA/%E8%B5%9E-%E4%B8%8A%20(1).png") no-repeat #fff;
      background-position: center center;
      background-size: 16px 16px;
    }
  }
}

.model-retry-wrapper {
  max-width: 65%;

  .retry-wrapper {
    .retry-btn {
      display: flex;
      justify-content: center;
      color: #0984F9;
      margin-top: 8px;
      cursor: pointer;
    }
  }
}