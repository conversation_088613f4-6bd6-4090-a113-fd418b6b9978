import React, { useState, useEffect, useContext, useRef } from "react";
import SseClient from "@/common/utils/SseClient";
import { track } from "@/common/tracks/actions/im";
import { randomId } from "@/common/utils"
import markdownIt from 'markdown-it';
import MsgAttrMap from "@/pages/im/constant/msg-attr-map";
import mila from 'markdown-it-link-attributes';
import { MsgListContext, ImContext } from "../../../../context";
import "./index.less";

// 初始化 markdown
const md = new markdownIt({
  html: true,
  linkify: true,
  typographer: true
});
md.use(mila, {
  attrs: {
    target: '_blank',
    rel: 'noopener noreferrer',
  },
});

const limit = 230;

function LargeModalCard(props): JSX.Element {
  const [describe, setDescribe] = useState("");
  const [inscribe, setInscribe] = useState("");
  const [konwledgeList, setKonwledgeList] = useState([]);
  const [whetherShow, setWhetherShow] = useState(undefined);
  const [isShowLoading, setIsShowLoading] = useState(true);
  const [isShowMore, setIsShowMore] = useState(undefined); // 是否展示查看更多
  const [isRetry, setIsRetry] = useState(false);
  const [likeStatus, setLikeStatus] = useState(undefined);
  const [limitContent, setLimitContent] = useState(""); // 设置内容是否展示更多
  const [hasConnected, setHasConnected] = useState(false); // 是否已经连接过
  const [hasClosed, setHasClosed] = useState(false); // 是否已经连接完成
  const [hasClickedShowMoreText, setHasClickedShowMoreText] = useState(false); // 是否已点击过展示更多内容
  const [queue, setQueue] = useState([]);

  const { sendMsg, isHistory } = useContext(MsgListContext);
  const { imConfig } = useContext(ImContext);

  // 历史消息直接设置数据
  useEffect(() => {
    if (isHistory) {
      const { data: { endMarkdownContent, markdownContent, results, whetherShow, whetherLike } } = props;

      setIsShowLoading(false);
      setDescribe(markdownContent);
      setInscribe(endMarkdownContent);
      setWhetherShow(whetherShow);
      setLikeStatus(whetherLike);

      if (Array.isArray(results.knowledgeList)) {
        setKonwledgeList(results.knowledgeList);

        setIsShowMore(results.knowledgeList.length > 3);
      }
    }
  }, []);


  useEffect(() => {
    const mk = composeDescribe();
    setLimitContent(hasClickedShowMoreText ? mk : mk.slice(0, limit));
  }, [describe, hasClickedShowMoreText]);

  useEffect(() => {
    if (JSON.stringify(queue) === JSON.stringify(props.modelQueue)) return;
    setQueue(JSON.parse(JSON.stringify(props.modelQueue || [])));
  }, [props.modelQueue]);

  // 非历史消息 流式获取消息; 如果 队列中排位不在前两名 等待连接
  useEffect(() => {
    const { id } = props;
    if (isHistory) return;
    if (!Array.isArray(queue)) return;
    if (!queue.includes(id)) return;
    if (hasConnected) return;
    if (queue.indexOf(id) > 1) return;

    getStreamData();
  }, [queue, hasConnected]);

  useEffect(() => {
    const { id } = props;
    ImBus.emit("DEL_MODEL_QUEQUE", { id, queue }); // 通知连接队列删除一个连接
  }, [hasClosed]);

  // 监听流式消息中其他端的点赞消息，进行匹配
  useEffect(() => {
    ImBus.on("LIKE_MSG_SEND", data => {
      const { specialEventType, targetMsgId, whetherLike } = data || {};

      if (!specialEventType || !targetMsgId || whetherLike === undefined) return;
      if (targetMsgId === props.id) {
        setLikeStatus(whetherLike);
      }
    });
  }, []);

  const composeDescribe = () => {
    return describe.replace(/\\n\\n/g, '\n\n').replace(/\\n/g, '\n\n');
  }

  const getStreamData = () => {
    const { data: { sseUrl = "" }, id } = props;
    const { baseInfo: { clientId } } = imConfig || {};

    setHasConnected(true);

    const sseClient = new SseClient(`${sseUrl}&clientId=${clientId}`, e => {
      const { data } = e || {};
      setDescribe(des => (des + data).replace(/\\n\\n/g, '\n\n').replace(/\\n/g, '\n\n'));

      ImBus.emit("SCROLL_TO_BOTTOM", "hasNoRead");
    }, error => {
      sseClient.close();

      setIsRetry(true);
      setDescribe("");
      setHasClosed(true);

      ImBus.emit("SCROLL_TO_BOTTOM", "hasNoRead");
    }, se => {
      // 在 正常 close 的时候，会返回落款信息以及知识列表数据
      const { data } = se || {};
      try {
        if (data && typeof data === "string") {
          const { endMarkdownContent, knowledgeList, whetherShow } = JSON.parse(data);

          setInscribe(endMarkdownContent);
          setKonwledgeList(knowledgeList);
          setIsShowMore(knowledgeList.length > 3);
          setWhetherShow(whetherShow);
        }

        ImBus.emit("SCROLL_TO_BOTTOM", "hasNoRead");
      } catch (error) {
        console.error("非正常close");
      }

      setHasClosed(true);
      sseClient.close();
      setIsShowLoading(false);
    });
    
  }

  // 大模型描述内容展示, 一期默认不考虑图片以及代码等情况，仅考虑文字展示
  const MarkdownRender = () => {
    // 展示内容字数
    const initContent = composeDescribe();
    const isLimit = initContent.length === limitContent.length ? false : initContent.length > limit;
    const isShowDire = isHistory ? true : (hasClickedShowMoreText ? false : true);

    if (!initContent) return null;
    return (
      <div className="markdown">
        <div dangerouslySetInnerHTML={{ __html: md.render(limitContent) }} />
        {isLimit && isShowDire && <span className="md-spread" onClick={() => {
          setLimitContent(composeDescribe());
          setHasClickedShowMoreText(true);
        }}>展开内容</span>}
      </div>
    );
  };

  // 落款标识展示
  const InscribeRender = () => {
    if (!inscribe) return null;
    const mk = inscribe.replace(/\\n\\n/g, '\n\n').replace(/\\n/g, '\n\n');
    return (<div className="markdown" dangerouslySetInnerHTML={{ __html: md.render(mk) }} />);
  };

  const showKnowledge = item => {
    const { data: { queryId, queryContent } } = props;
    const { title, id } = item;

    track(41824, {
      click_result: title,
      knowledge_id: id,
      query_id: queryId,
      query_content: queryContent
    });

    const msg = { msgType: -1, msgPayload: title, knowledgeId: id, msgAttr: MsgAttrMap.knowledge, queryId };
    if (isHistory) {
      ImBus.emit("MSG_SEND", msg);
      return;
    }

    sendMsg(msg);
  }

  // 知识列表展示
  const KnowledgeListRender = () => {
    if (!Array.isArray(konwledgeList) || (Array.isArray(konwledgeList) && !konwledgeList.length)) return null;

    const isShowFold = konwledgeList.length > 3 && isShowMore;
    const rebuildList = isShowFold ? konwledgeList.slice(0, 3) : konwledgeList;
    const { data: { queryId, queryContent } } = props;

    return (
      <div className="knowledge-list-wrapper">
        <p className="list-title">相关内容</p>
        <div className="list-content">
          { rebuildList.map((item, index) => <p key={`${item.id}-${index}`} onClick={() => { showKnowledge(item) }}>{item.title}</p>) }
        </div>
        {
          isShowFold && (<p className="list-fold" onClick={() => {
            setIsShowMore(false);
            track(41825, { query_id: queryId, query_content: queryContent });
          }}>查看更多</p>)
        }
      </div>
    );
  };

  // 重试
  const RetryRender = () => {
    const { id } = props;
    return (
      <div className="retry-wrapper">
        <p>回答失败，点击下方按钮，重新回答</p>
        <span className="retry-btn" onClick={() => {
          ImBus.emit("RETRY_MODEL_QUEQUE", { id, queue }); // 通知连接队列删除一个连接
          setHasConnected(false);
          setIsRetry(false);
        }}>重新回答</span>
      </div>
    );
  }

  // 点赞区域展示
  const JudgeRender = () => {
    if (!whetherShow) return null;
    const likeDom = (<div className={`like ${likeStatus === true && "like-active"}`} onClick={() => { clickLike(true) }} />);
    const unlikeDom = (<div className={`unlike ${likeStatus === false && "unlike-active"}`} onClick={() => { clickLike(false) }} />);

    return (
      <div className="like-wrapper">
        { likeStatus === undefined && !isHistory && (<>{likeDom}{unlikeDom}</>) }
        { likeStatus === true && likeDom }
        { likeStatus === false && unlikeDom }
      </div>  
    );
  };

  const clickLike = whetherLike => {
    if (likeStatus !== undefined) return;

    const { id, spm, userInfo, data: { queryId, queryContent } } = props;

    sendMsg({
      msgId: randomId(),
      spm,
      fromUcid: userInfo.ucId,
      msgType: -221,
      sendTime: Date.now(),
      channel: imConfig.bizInfo.bizChannel,
      clientId: imConfig.baseInfo.clientId,
      msgPayload: JSON.stringify({ specialEventType:"LIKE", whetherLike, targetMsgId: id })
    });

    setLikeStatus(whetherLike);

    track(61733, {
      query_id: queryId,
      query_content: queryContent,
      whetherLike,
      content: {
        describe,
        konwledgeList
      }
    });
  }

  return (
      <div className={`${isRetry ? "model-retry-wrapper" : "model-card-wrapper"}`}>
        { isRetry ? (<RetryRender />) : (
          <>
            <MarkdownRender />
            <InscribeRender />
            <KnowledgeListRender />
            <JudgeRender />
            {
              isShowLoading && (
                <div className="msg-wrapper service-msg loading">
                    <span /><span /><span />
                </div>
              )
            }
          </>  
        ) }
      </div>
  );
}

export default LargeModalCard;
