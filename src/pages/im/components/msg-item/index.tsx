/*
 * [IM消息/卡片定义](https://wiki.lianjia.com/pages/viewpage.action?pageId=12507415)
 * @Author: liuzhenkun002
 * @Date: 2021-03-24 11:35:10
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-08 15:24:57
 */

import React, { useContext } from "react";
import { connect } from "react-redux";

import ServiceAvatar from "@/common/assets/image/service.jpg";
import UserAvatar from "@/common/assets/image/user-avatar.png";

import { ImContext } from "../../context";
import { replaceProtocol } from "@/common/utils"

import ActionCard from "./components/action-card";
import Image from "./components/image";
import JumpCard from "./components/jump-card";
import KnowledgeCard from "./components/knowledge-card";
import Mp3 from "./components/mp3";
import OnlineTipCard from "./components/online-tip-card";
import QuestionList from "./components/question-list";
import RichText from "./components/rich-text";
import MSG331 from "./components/msg-331"
import IframeItem from "./components/iframe-msg";
import MsgNeg210 from "./components/msg-neg-210";
import MsgNeg215 from "./components/msg-neg-215";
import StreamData from "./components/stream-data";
// import StreamLargeModalCard from "./components/stream-large-modal-card";
import LargeModalCard from "./components/large-modal-card";
import ChatPMPilot from "../../agent-chat/components/msg-item/components/chat-pm-pilot";
import GroovyList from "../../agent-chat/components/msg-item/components/groovy-list";
import ToolResult from "../../agent-chat/components/msg-item/components/tool-result";
import ToolUse from "../../agent-chat/components/msg-item/components/tool-use";

import "./index.less";

interface PropTypes {
  domId?: string,
  direction: number,
  type: string,
  avatar?: string,
  manual?: boolean
  userInfo: any;
  data?: any;
  msgId?: string;
  id?: number;
}

// 过多的变量后续考虑抽离到单独的文件
const MsgComponentMap: any = {
  "-3": Mp3, // 语音, !!还包含一种是智能阶段开启语音, 单独发送语音转的文本内容 msgType: -228
  "-2": Image, // 图片
  "-1": RichText, // 文本
  "-113": RichText, // 选择技能组 TODO
  "-117": IframeItem, // 渲染iframe
  "120": RichText, // 富文本
  "600": RichText, // 客服消息
  "-204": RichText, // 特殊气泡消息
  "-112": ActionCard, // 转人工 TODO
  "-115": ActionCard, // 意图菜单
  "-200": QuestionList, // 普通列表
  "-201": QuestionList, // 精准+列表
  "99": KnowledgeCard, // 知识卡片
  "-207": OnlineTipCard, // 进线提示卡片
  "331": MSG331,
  "-210": MsgNeg210, // 页面消息（通过抽屉or新页面打开）
  "-215": MsgNeg215, // 事件通知消息，通过ImBus emit对应的事件
  "-218": StreamData, // 流式数据（SSE）
  "-219": LargeModalCard, // 流式大模型消息
  "-220": LargeModalCard, // 历史大模型消息

  "-225": ChatPMPilot, // 智能助理
  "-226": GroovyList, // AI生成/推荐groovy
  "-227": ToolResult, // AI工具结果
  "-228": ToolUse, // AI工具调用

  // 前端自定义类型
  "fe01": JumpCard, // 意图卡片选择是展示的跳转卡片
  "fe02": ActionCard // 单个技能组时展示的进线询问卡片 TODO drop
};

const WithoutAvatarTypes: string[] = [
  "-113", // 技能组选择菜单
  "-117", // iframe消息
  "-207",
  "331",
  "-210",
  "-215"
];

const WithoutAvatarCards: number[] = [
  -9, // card-9 技能组选择菜单
];

const mapStateToProps = (state: any) => ({
  userInfo: state.user.userInfo
});

function MsgItem(props: PropTypes): JSX.Element {
  const MsgComponent = MsgComponentMap[props.type];
  if (!MsgComponent) return null;

  const { imConfig, isPc } = useContext(ImContext);
  const isClient = props.direction === 1;
  const Avatar = () => {
    let avatar = "";
    if (isClient) {
      avatar = props.userInfo.avatar || UserAvatar; // 设置的头像或默认头像
    } else {
      const serviceAvatar = props.avatar || ServiceAvatar; // 客服头像或默认头像
      avatar = props.manual ? serviceAvatar : imConfig.baseInfo.robotAvatar;
    }
    
    return (
      <img className="avatar" src={replaceProtocol(avatar)} />
    );
  };

  const id = props.id || props.msgId;
  // TODO 优化ID的取值策略
  const domId = id ? `msg-${id}` : props.domId
  return (
    <div className={`msg-wrapper msg-${props.msgId} ${isClient ? "client-msg" : "service-msg"}`} id={domId}>
      {(!WithoutAvatarTypes.includes(props.type) 
      || (props.type === "331" && !WithoutAvatarCards.includes(props.data.cardID))
      ) && <Avatar />}
      <MsgComponent
        {...props}
        domId={domId}
        isPc={isPc}
        isClient={isClient}
      />
    </div>
  );
}

export default connect(
  mapStateToProps
)(MsgItem);