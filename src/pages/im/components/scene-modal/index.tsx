// PC 端场景卡片
import React, { useEffect } from "react";
import { Modal } from "antd-mobile";
import { track } from "@/common/tracks/actions/im";
import "./index.less";

interface PropTypes {
  visible: boolean,
  onChange: any,
  data: any,
  onClose: any,
  isPc: boolean
}

function SceneModal(props: PropTypes):JSX.Element {
  const { visible, onChange, onClose, data = {}, isPc } = props;
  const cardList = data?.data?.list || [];

  if (!visible) return null;
  if(!cardList.length) return null;

  useEffect(() => {
    track(58924);
  }, [])

  const handleClickItem = (item) => {
    if (!item || !onChange) return;
    const { extConfig, name, code } = item;
    track(58413, { name, code });
    onChange(extConfig);
  }

  const renderItem = item => {
    if (!item) return null;

    return (
      <div className="card-item" onClick={() => { handleClickItem(item); }} key={item.name}>
        <img className="card-icon" src={item.icon} />
        <div className="card-info">
          <p className="card-title">{item.name}</p>
          <p className="card-desc">{item.title?.slice(0, 14)}</p>
        </div>
      </div>
    );
  }

  const handleClose = () => {
    track(58925);
    props.onClose();
  }

  const modalWrapperClassName = isPc ? "pc-scene-modal" : "mobile-scene-modal";
  const modalHeaderClassName = isPc ? "pc-modal-header" : "mobile-modal-header";
  const modalContentClassName = isPc ? "pc-modal-content" : "mobile-modal-content";
  const modalProps = {
    popup: !isPc,
    visible,
    transparent: true,
    maskClosable: false,
    animationType: "slide-up",
    onClose: handleClose,
    className: modalWrapperClassName
  };

  return (
    <Modal {...modalProps}>
      <div className={`${modalHeaderClassName}`}>
        <p className="title">请选择您要咨询的问题场景</p>
        <div className="btn-close" onClick={handleClose}>×</div>
      </div>
      <div className={`${modalContentClassName}`}>
        { cardList.map(item => renderItem(item)) }
      </div>
    </Modal>
  )
};

export default SceneModal;
