@import "../../../../styles/variable.less";
.pc-scene-modal {
  width: 720px;

  .pc-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px 5px;
    height: 30px;
    border-bottom: 1px solid @border-base;

    .title {
      font-size: 15px;
      font-weight: 500;
    }

    .btn-close {
      font-size: 16px;
      color: #9297A6;;
      cursor: pointer;
    }
  }

  .pc-modal-content {
    padding: 25px 10px;
    display: flex;
    flex-wrap: wrap;

    .card-item {
      box-sizing: border-box;
      flex-shrink: 0;
      width: 327px;
      padding: 16px;
      display: flex;
      align-items: center;
      text-align: left;
      background: #F8F8F8;
      border-radius: 4px;
    }

    .card-item:nth-child(2n+1) {
      margin-right: 16px;
    }

    .card-item:nth-child(n+3) {
      margin-top: 16px;
    }
    
    .card-title {
      font-size: 18px;
      color: #222222;
      line-height: 18px;
      margin-bottom: 5px;
      font-weight: bold;
    }

    .card-desc {
      font-size: 14px;
      color: #666666;
      line-height: 16px;
    }

    .card-icon {
      width: 32px;
      height: 32px;
      object-fit: cover;
      margin-right: 12px;
    }
  }
}

.mobile-scene-modal {
  .mobile-modal-header {
    margin-bottom: 15px;
    padding: 0 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      font-size: 18px;
      color: #222222;
      letter-spacing: 0;
      line-height: 22px;
      font-weight: 500;
    }

    .btn-close {
      font-size: 20px;
      color: #ccc;;
    }
  }

  .mobile-modal-content {
    display: flex;
    flex-wrap: wrap;
    flex-shrink: 0;
    padding: 5px;

    .card-item {
      box-sizing: border-box;
      width: calc(50% - 6px);
      padding: 14px;
      background: #F8F8F8;
      border-radius: 4px;
      display: flex;
      align-items: center;
      text-align: left;

      img {
        width: 32px;
        height: 32px;
        margin-right: 6px;
      }

      .card-title {
        font-size: 16px;
        color: #222222;
        letter-spacing: 0;
        line-height: 16px;
        font-weight: bold;
      }

      .card-desc {
        font-size: 12px;
        color: #555555;
        letter-spacing: 0;
        line-height: 17px;
      }
    }

    .card-item:nth-child(2n+1) {
      margin-right: 12px;
    }

    .card-item:nth-child(n+3) {
      margin-top: 12px;
    }
  }
}