/*
 * @Author: l<PERSON><PERSON>kun002
 * @Date: 2021-05-08 16:22:11
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-17 16:28:43
 */

import React, { Component } from "react";
import Draggable from "react-draggable";
import onClickOutside from "react-onclickoutside";

import jsBridge from "@/common/bridge";
import { track } from "@/common/tracks/actions/im";

import "./index.less";

class Questionnaire extends Component {
  constructor(props) {
    super(props);
    this.isDragging = false;
    this.state = { isFolded: true };
  };

  componentDidMount() {
    if (this.refs.dragRef) {
      // 动态计算折叠距离，其实大概是元素长度减去漏出的部分
      this.refs.dragRef.style.right = `-${this.refs.dragRef.clientWidth - 36}px`;
    }
  }

  onStop = () => {
    // 确实是再拖拽
    if (this.isDragging) {
      setTimeout(() =>  {
        this.isDragging = false;
      }, 0);
      return;
    }

    // 这个组件移动端 onClick 有事件问题，裂开，通过这种方式模拟点击
    this.questionClick();
  };

  onDrag = () => {
    this.isDragging = true;
  };

  questionClick = () => {
    // 拖动状态不触发点击
    if (this.isDragging) return;

    // 收起状态转为展开
    if (this.state.isFolded) {
      this.setState({ isFolded: false });
      this.refs.dragRef.style.right = "5px";
      return;
    }

    // 展开状态点击跳转
    this.showDetail();
  };

  showDetail = () => {
    const { name, url, moduleType, spm, pageId, isPc, setModalUrl } = this.props;
    const moduleName = JSON.stringify({ name, url });
    track(41809, { module_type: moduleType, module_name: moduleName });
    const params = `pageId=${pageId}&spm=${spm}`;
    let paramsUrl = url.indexOf("?") < 0 ? `${url}?${params}` : `${url}&${params}`;

    if (isPc) {
      paramsUrl = `${paramsUrl}&isPc=true`;
      setModalUrl(paramsUrl);
      return;
    }
    
    jsBridge.openPage(paramsUrl);
  };

  handleClickOutside = () => {
    this.setState({ isFolded: true });
    if (this.refs.dragRef) {
      this.refs.dragRef.style.right = `-${this.refs.dragRef.clientWidth - 36}px`;
    }
  };

  renderPc() {
    const { name, index, icon } = this.props;

    return (
      <div
        className="question-bubble question-bubble-pc"
        style={{ top: `${index * 55 + 10}px` }}
        ref="dragRef"
        onClick={this.questionClick}
      >
        <div className="question">
          <img className="red-packet-icon" src={icon} />
          {name}
        </div>
      </div>
    );
  };

  render() {
    const { name, index, icon, isPc } = this.props;

    if (isPc) return this.renderPc();

    return (
      <Draggable
        axis="y"
        allowAnyClick={true}
        defaultClassName="question-bubble"
        defaultPosition={{ x: 0, y: index * 55 }}
        position={null}
        grid={[10, 10]}
        scale={1}
        onDrag={this.onDrag}
        onStop={this.onStop}
      >
        <div ref="dragRef">
          <div className="question">
            <img className="red-packet-icon" src={icon} />
            {name}
          </div>
        </div>
      </Draggable>
    );
  };
}

export default onClickOutside(Questionnaire);