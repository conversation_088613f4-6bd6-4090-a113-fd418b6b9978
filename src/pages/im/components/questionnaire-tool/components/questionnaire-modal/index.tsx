import React from "react";
import { Modal } from "antd-mobile";

import "./index.less";

interface PropTypes {
  modalVisible: boolean,
  setModalVisible: Function,
  url: string
}

function QuestionnaireModal(props: PropTypes): JSX.Element {
  const modalProps = {
    className: "questionnaire-modal",
    transparent: true,
    visible: props.modalVisible,
    maskClosable: false,
    onClose: () => props.setModalVisible(false),
    animationType: "slide-up"
  };

  return (
    <Modal {...modalProps} >
      <div className="modal-header">
        <div className="btn-close" onClick={() => props.setModalVisible(false)}>关闭</div>
      </div>
      <iframe className="questionnaire-iframe" frameBorder="0" src={props.url} />
    </Modal>
  )
};

export default QuestionnaireModal;