/*
 * @Author: liuzhenkun002
 * @Date: 2021-05-08 11:30:01
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-17 15:00:17
 */

import React, { useState, useEffect, useContext } from "react";

import { track } from "@/common/tracks/actions/im";

import Questionnaire from "./components/questionnaire";
import QuestionnaireModal from "./components/questionnaire-modal";
import { ImContext } from "../../context";

interface PropTypes {
  extRegionInfos: any,
  spm: string
}

// 本来这个组件是在写在 function section 中的，
// 但是后来做下拉加载历史记录时发现会有奇妙的互动关系，所以被迫拆出来了。
function QuestionnaireTool(props: PropTypes): JSX.Element {
  const { extRegionInfos, spm } = props;
  const [modalVisible, setModalVisible] = useState(false);
  const [modalUrl, setModalUrl] = useState("");
  const { imConfig: { bizInfo }, isPc } = useContext(ImContext);
  const config = extRegionInfos.find((item: any) => item.key === "survey");
  if (!config) return null;

  const { list = [] } = config.data || {};

  useEffect(() => {
    const moduleContent = list.reduce((acc: any[], cur: any) => {
      acc.push({ name: cur.name });
      return acc;
    }, []);
    track(41807, { module_type: config.key, module_content: JSON.stringify(moduleContent) });
  }, []);

  useEffect(() => {
    if (modalUrl) {
      setModalVisible(true);
    }
  }, [modalUrl]);

  useEffect(() => {
    if (!modalVisible) {
      setModalUrl("");
    }
  }, [modalVisible]);

  return (
    <>
      {
        list.map(
          (item: any, index: number) => (
            <Questionnaire
              {...item}
              key={item.name}
              index={index}
              moduleType={config.key}
              spm={spm}
              pageId={bizInfo.pageSession}
              isPc={isPc}
              setModalUrl={setModalUrl}
            />
          )
        )
      }

      <QuestionnaireModal
        modalVisible = {modalVisible}
        url={modalUrl}
        setModalVisible={setModalVisible}
      />
    </>
  );
};

export default QuestionnaireTool;