/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-04-07 11:04:52
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-15 14:05:25
 */

import React from "react";

import Announcement from "./components/announcement";
import CardList from "./components/card-list";
import RecommendList from "./components/recommend-list";
import ToolsList from "./components/tools-list";

interface PropTypes {
  extRegionInfos: any,
  toolBox?: any,
  isPc?: boolean,
  tab?: any
}

function FunctionSection(props: PropTypes): JSX.Element {
  const { extRegionInfos, toolBox = {}, isPc, tab } = props;

  const ConfigKeyComponentMap: any = {
    recommend: RecommendList, // 推荐
    tool_box: props.isPc ? undefined : ToolsList, // 工具 pc版需要特殊处理
    biz_card: CardList, // 业务卡片
    notice: Announcement, // 公告
  };
  
  if (!extRegionInfos) return null;

  const renderMobileToolBox = () => {
    if(isPc) return null;

    const NEW_TOOL_TYPE = "1";
    const { showToolType, toolBox: toolBoxData } = toolBox; // 0 渲染旧版工具 1 渲染新版工具
    const isNewToolType = showToolType == NEW_TOOL_TYPE;
    const toolModuleType = isNewToolType ? "new_tool_box" : "tool_box";
    const toolData = isNewToolType ? toolBoxData : toolBoxData[0]?.data?.list;

    return (
      <ToolsList
        url=""
        tab={tab.tabKey}
        key={tab.time}
        moduleType={toolModuleType}
        data={{ list: toolData }}
        isNewType={isNewToolType}
        />
    )
  }

  return (
    <div className="im-function-section">
      {
        extRegionInfos.map((section: SectionConfig) => {
          if (section.key === "tool_box") return renderMobileToolBox();

          const Comp = ConfigKeyComponentMap[section.key];
          return Comp ? <Comp {...section} moduleType={section.key} /> : null
        })
      }
    </div>
  );
};

export default FunctionSection;