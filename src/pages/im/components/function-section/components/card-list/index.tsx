/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-03-19 15:29:25
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-21 16:23:39
 */

import React, { useEffect } from "react";

import jsBridge from "@/common/bridge";
import { track } from "@/common/tracks/actions/im";
import { dateFromat, dateFormatType } from "@/common/utils/date";

import "./index.less";

interface Card {
  name: string,
  opDate: number, 
  state: string,
  title: string,
  url: string
}

interface PropTypes {
  data: {
    list: Card[]
  },
  url: string,
  moduleType: string
}

function CardList(props: PropTypes) {
  const { list } = props.data || {};
  const url = props.url;

  if (!list || !list.length) return null;

  const cardClick = (card: Card) => {
    const { name, url } = card;
    const moduleName = JSON.stringify({ name, url });

    jsBridge.openPage(url);
    track(41809, { module_type: props.moduleType, module_name: moduleName } );
  };

  const moreClick = () => {
    jsBridge.openPage(url);
    track(41814);
  };

  useEffect(() => {
    const moduleContent = list.reduce((acc: any[], cur: Card) => {
      acc.push({ url: cur.url, title: cur.name });
      return acc;
    }, []);
    track(41807, { module_type: props.moduleType, module_content: JSON.stringify(moduleContent) });
  }, []);

  const Card = (card: Card) => {
    const {url, name, state, title, opDate} = card;
    return (
      <div className="card-wrapper" key={url} onClick={() => cardClick(card)}>
        <div className="card-title">
          <div className="title">{ name }</div>
          <div className="state">{ state }</div>
        </div>
        <p className="card-content">{ title }</p>
        <div className="card-footer">
          {dateFromat(opDate, dateFormatType.dataTime2)}
        </div>
      </div>
    )
  };

  return (
    <div className="card-list-wrapper">
      {list.map(card => Card(card))}
      <div className="card-more">
        <div className="text" onClick={moreClick}>查看更多</div>
      </div>
    </div>
  )
}

export default CardList;