@import "../../../../../../styles/variable.less";

.card-list-wrapper {
  display: flex;
  margin-top: 16px;
  padding: 0 16px;
  overflow-x: scroll;
  overflow-y: hidden;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }

  .card-wrapper {
    box-sizing: border-box;
    margin-right: 20px;
    padding: 10px 12px;
    width: 250px;
    background: #fff;
    flex-shrink: 0;
    border-radius: 4px;
    cursor: pointer;

    .card-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: @font-gray;

      .state {
        padding: 2px 4px;
        color: #faad14;
        font-size: 12px;
        background: rgba(250, 173, 20, .1);
        border-radius: 2px;
      }
    }

    .card-content {
      margin: 8px 0;
      height: 18px;
      line-height: 18px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .card-footer {
      color: @font-gray;
    }
  }
  
  .card-more {
    display: flex;
    align-items: center;
    padding: 0 35px;
    background: #fff;
    border-radius: 4px;
    cursor: pointer;

    .text {
      width: 30px;
      text-align: center;
    }
  }

  // 用于最右侧空白
  &::after {
    content: "占";
    margin-left: 2px;
    visibility: hidden;
  }
}
