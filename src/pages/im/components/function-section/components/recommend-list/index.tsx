/*
 * @Author: liuzhenkun002
 * @Date: 2021-03-22 14:50:45
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-05-21 15:47:25
 */

import React, { useEffect, useState } from "react";
import { Tabs } from 'antd-mobile';

import request from "@/common/request";
import { genUrl, genUrlParams } from "@/common/utils/url";
import { track } from "@/common/tracks/actions/im";
import MsgAttrMap from "@/pages/im/constant/msg-attr-map";

import "./index.less";

interface RecommendItem {
	id: string,
	title: string
}

interface PropTypes {
	data: {
		list: { name: string, url: string }[]
	},
	moduleType: string
}

function RecommendList(props: PropTypes): JSX.Element {
	const list = props.data.list.map(({ name, url }) => ({ title: name, url }));
	const [loading, setLoading] = useState(true);
	const [fetchResult, setFetchResult] = useState([]);
	const [pageNos, setPageNos] = useState([]);
	const [tabNo, setTabNo] = useState(4); // 首屏展示的 tab 数量，小屏幕减少数量
	const [curTab, setCurTab] = useState(0);

	const recommendClick = (recommend: RecommendItem) => {
		const { id, title } = recommend;
		const moduleName = JSON.stringify({id, title});

		ImBus.emit("MSG_SEND", {
			msgType: -1,
			msgPayload: title,
			knowledgeId: id,
			msgAttr: MsgAttrMap.recommend
		});

		track(41809, { module_type: props.moduleType, module_name: moduleName });
	};

	const tabClick = (tab: any, index: number) => {
		setCurTab(index);
		track(41811, { module_type: props.moduleType, tab_name: tab.title });
	};

	const recommendChange = () => {
		// 需要从请求的 url 上取出各种参数，需要替换 page 字段。
		const { url } = list[curTab];
		const [host] = url.split("?");
		const params = genUrlParams(url);
		const searchCond = JSON.parse(params.knowledgeSearch); // 获取请求上的参数
		const newPageNos = [...pageNos];
		const curNo = newPageNos[curTab]; // 当前在操作哪一页
		const { total } = fetchResult[curTab]; // 当前的最多有多少条
		const maxNos = Math.ceil(total/searchCond.size);
		const maxPageNo = maxNos > 5 ? 5 : maxNos; // 限制最多5页或最大页数
		const nextNo = curNo === maxPageNo ? 1 : curNo + 1;
		searchCond.page = nextNo; // 设置搜索条件的 page
		params.knowledgeSearch = JSON.stringify(searchCond);
		newPageNos[curTab] = nextNo;

		request.get(genUrl(host, params))
			.then((data) => {
				if (data) {
					const copy = [...fetchResult];
					copy[curTab] = data;
					setFetchResult(copy);
					setPageNos(newPageNos);
					track(41813);
				}
			}).catch(x => x);
	};

	const Recommend = (props: {index: number}) => {
		const recommend = fetchResult[props.index];
		if (!recommend) return null;
		const { results } = recommend;
		if (!results || !results.length) return null;
		return (
			<div className="recommend-tab">
				{results.map((item: RecommendItem) => (
					<div className="recommend-item" key={item.id} onClick={() => recommendClick(item)}>
						{item.title}
					</div>
				))}
			</div>
		)
	};

	useEffect(() => {
		if (!list || !list.length) return;

		setPageNos(list.map(() => 1));

		// 小屏幕减少首页tab数量
		if ( document.body.clientWidth <= 375 ) {
			setTabNo(3);
		}

		Promise.all(list.map(tab => request.get(tab.url)))
			.then((data) => {
				if (!data || !data.every(d => d)) return;
				setFetchResult(data);
				setLoading(false);
				const curResults = data[0].results || [];
				const moduleContent = curResults.reduce((acc: any[], cur: RecommendItem) => {
					acc.push({ id: cur.id, title: cur.title });
					return acc;
				}, []);
				track(41807, { module_type: props.moduleType, module_content: JSON.stringify(moduleContent) });
			}).catch(x => x);
	}, []);

	if (loading) return null;

  return (
    <div className="recommend-wrapper">
			<Tabs tabs={list}
				initialPage={curTab}
				onTabClick={(tab, index) => tabClick(tab, index)}
				renderTabBar={props => <Tabs.DefaultTabBar {...props} page={tabNo} />}
			>
				{list.map((item, index) => <Recommend index={index} key={item.title}/>)}
			</Tabs>
			<div className="recommend-change" onClick={recommendChange}>换一批</div>		
    </div>
  )
}

export default RecommendList;
