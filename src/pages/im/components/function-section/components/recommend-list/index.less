@import "../../../../../../styles/variable.less";

.recommend-wrapper {
  position: relative;
  margin: 16px 16px 0 16px;
  background: #fff;
  border-radius: 4px;
  
  .recommend-tab {
    .recommend-item {
      display: flex;
      align-items: center;
      padding: 11px 12px;
      border-top: 1px solid @border-base;
      cursor: pointer;
    }
  }

  .recommend-change {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 66px;
    height: 44px;
    z-index: 1;
    background: #fff;
    box-shadow: 0 0 5px 0 rgba(0,0,0,0.10);
    color: @font-blue;
    cursor: pointer;
  }

  .am-tabs-tab-bar-wrap {
    margin-right: 66px;
  }

  .am-tabs-default-bar-tab {
    font-size: 14px;
    overflow: hidden;
    white-space: nowrap;

    &::after {
      height: 0 !important;
    }

    &.am-tabs-default-bar-tab-active {
      color: @font-blue;
    }
  }

  .am-tabs-default-bar-underline {
    border: 1px solid @font-blue;
  }
}