.tools-list-wrapper {
  display: flex;
  margin-top: 16px;
  padding: 0 16px;
  height: 80px;
  overflow-x: scroll;
  overflow-y: hidden;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }

  .tool-item {
    width: 64px;

    .tool-box {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 10px 4px 10px;
      width: 44px;
      height: 44px;
      border-radius: 12px;
      box-shadow: 0 2px 12px 0 rgba(0,0,0,0.08);
      background: #fff;

      img {
        width: 24px;
      }
    }

    .tool-name {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 8px;
      height: 26px;
      font-size: 12px;
      color: #999;
      text-align: center;
    }
  }

  .tool-list {
    height: auto;
    overflow-y: scroll;
    scrollbar-width: none;
    display: flex;
    flex-wrap: wrap;
    background: #fff;
    padding: 16px 20px;

    .new-item {
      width: 20%;
      display: flex;
      flex-direction: column;
      align-items: center;
      flex-shrink: 0;
      text-align: center;
      margin-bottom: 16px;

      img {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: #FFFFFF;
      }
  
      .tool-name {
        flex-shrink: 0;
        width: 3.75rem;
        font-size: 0.688rem;
        color: #222222;
      }
    }

    .hidden {
      display: none;
    }
  }

  .list-spread-status {
    // max-height: 422px;
  }

  .tool-spread {
    width: 100%;
    text-align: center;
    font-size: 12px;
    color: #222222;
    letter-spacing: 0;
    text-align: center;
    line-height: 12px;
    background: #fff;
    padding: 0 16px 16px;
  }

  .spread-icon {
    width: 8px;
    height: 8px;
    display: inline-block;
    background: url(@/common/assets/image/spread.png) no-repeat;
    background-size: 8px 8px;
    background-position: center;
    margin-left: 4px;
  }

  .fold-icon {
    width: 8px;
    height: 8px;
    display: inline-block;
    background: url(@/common/assets/image/fold.png) no-repeat;
    background-size: 8px 8px;
    background-position: center;
    margin-left: 4px;
  }
}

.new-list-wrapper {
  height: auto;
  // max-height: 422px;
  overflow-y: auto;

  .tool-name {
    margin-top: 8px;
  }

  .am-tabs-tab-bar-wrap {
    border-bottom: 1px solid #F0F0F0;
  }

  .am-tabs-default-bar-tab  {
    width: auto !important;
    padding-left: 10px;
    padding-right: 10px;
  }

  .am-tabs-default-bar-tab-active {
    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      width: 24px;
      height: 3px;
      background: #0984F9;
    }
  }

  .am-tabs-default-bar-top .am-tabs-default-bar-tab {
    border-bottom: none;
    &::after {
      background-color: transparent;
    }
  }

  .am-tabs-default-bar-underline {
    display: none;
  }
}
