/*
 * @Author: liuzhenkun002
 * @Date: 2021-03-19 14:11:05
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-05-08 16:41:14
 */

import React, { useEffect, useState } from "react";
import { Tabs } from 'antd-mobile';
import { track } from "@/common/tracks/actions/im";
import jsBridge from "@/common/bridge";

import "./index.less";

// 打开方式，pc为空，如果是h5,会有 miniApp、h5 两种枚举
enum ToolEndType {
  mini = "miniApp",
  h5 = "h5",
  other = ""
}

interface Tool {
  data?: any;
  icon: string,
  name: string,
  url: string,
  endType: ToolEndType
}

interface PropTypes {
  data: {
    list: Tool[]
  },
  url: string,
  moduleType: string,
  isNewType?: boolean,
  tab?: string;
}

function ToolsList(props: PropTypes): JSX.Element {
  const { isNewType, tab } = props;
  const { list } = props.data || {};
  const [curTab, setCurTab] = useState(0);
  const [spreadStatus, setSpreadStatus] = useState({}); // 存储每一个 小工具的展开状态

  if (!list || !list.length) return null;

  const toolClick = (tool: Tool, type?: string) => {
    const { name, url, endType } = tool;
    const moduleName = JSON.stringify({ name, url });
    if (endType === ToolEndType.mini) {
      // 打开方式为小程序，调用特殊api
      // 入参object类型，便于后续扩展
      jsBridge.openMiniPage({ url });
    } else {
      jsBridge.openPage(url);
    }

    track(41809, { module_type: props.moduleType, module_name: moduleName });
  };

  useEffect(() => {
    // 初始曝光的小工具 <= 15个 直接上报，超过则上报前15个
    let LimitExpoTool = [];

    list.forEach((item:any, index) => {
      const { bizKey, data } = item;
      if (tab === bizKey) {
        setCurTab(index);
        LimitExpoTool = data?.list?.slice(0, 15)?.map(item => { return { title: item.name } });
      }
    });

    const moduleContent = list.reduce((acc: any[], cur: Tool) => {
      acc.push({ title: cur.name });
      return acc;
    }, []);

    track(41807, { module_type: props.moduleType, module_content: JSON.stringify(isNewType ? LimitExpoTool : moduleContent) });
  }, []);

  const renderToolList = () => {
    return list.map(tool => (
        <div className="tool-item" key={tool.name} onClick={() => toolClick(tool)}>
          <div className="tool-box">
            <img src={tool.icon} />
          </div>
          <div className="tool-name">{ tool.name.slice(0, 10) }</div>
        </div>
      ))
  }

  const triggerFoldStatus = () => {
    const rebuildStatus = {...spreadStatus};
    const isSpread = !spreadStatus[`${curTab}`];
    rebuildStatus[`${curTab}`] = isSpread;
    setSpreadStatus(rebuildStatus);

    sendToolEventLog(curTab, isSpread);
    track(58926, { curTab, isSpread });
  }

  // tab 页工具区曝光
  const sendToolEventLog = (activeTab, isSpread?: boolean) => {
    const curListData = list[activeTab]?.data?.list || [];
    const curSpreadStatus = isSpread !== undefined ? isSpread : spreadStatus[activeTab];
    const expoList = curSpreadStatus ? curListData : curListData.slice(0, 15);

    const moduleContent = expoList.map(item => {return { title: item.name }});
    track(41807, { module_type: props.moduleType, module_content: JSON.stringify(moduleContent) });
  }

  const handleClickTab = index => {
    setCurTab(index);
    sendToolEventLog(index);

    const pNode = document.querySelector('.new-list-wrapper');
    if (!pNode) return;
    const parentWidth = pNode?.clientWidth;
    const tabNode: any = pNode?.querySelector(".am-tabs-default-bar-content");
    const children = pNode?.querySelectorAll(".am-tabs-default-bar-tab");
    let tabWidth = 0;

    if (!children) return;

    // 修复 antd 超长tab 点击后滑动到tab最前面
    list.forEach((item, itemIndex) => {
      if(itemIndex > index) return;
      tabWidth += children[itemIndex].clientWidth;
    });

    setTimeout(() => {
      if (tabWidth > parentWidth) {
        tabNode.style.transform = `translate3d(-${tabWidth - parentWidth + 200}px, 0px, 0px)`;
      }
    }, 0);
  }

  const renderNewToolList = () => {
    const foldCount = 15; // 超过15个小工具折叠
    const tabs = list.map(({ name }) => ({ title: name }));
    const curTabData = list[curTab]?.data || {};
    const realList = curTabData?.list || [];
    const hasFold = realList.length > foldCount;
    const foldText = !!spreadStatus[curTab] ? "收起工具" : "点击查看更多工具";

    return (
      <Tabs tabs={tabs}
        swipeable={false}
        page={curTab}
				initialPage={curTab}
				onTabClick={(tab, index) => { handleClickTab(index); }}
				renderTabBar={props => <Tabs.DefaultTabBar {...props} />}
			>
        <div className="tool-list-panel">
        <div className={`tool-list ${!!spreadStatus[curTab] && "list-spread-status"}`}>
          {realList.map((item, index) => {
            const foldClassName = index > foldCount - 1 && !spreadStatus[curTab] && "hidden";
            return (
              <div
                className={`new-item ${foldClassName}`}
                key={item.name}
                onClick={() => toolClick(item)}
              >
                <img src={item.icon} />
                <div className="tool-name">{ item.name.slice(0, 10) }</div>
              </div>
            )
            })}
          </div>
          {
            hasFold && (<p className={`tool-spread`} onClick={() => triggerFoldStatus()}>
              {foldText}
              <span className={`${!!spreadStatus[curTab] ? "fold-icon" : "spread-icon"}`} />  
            </p>)
          }
        </div>
			</Tabs>
    )
  }

  return (
    <div className={`tools-list-wrapper ${isNewType && "new-list-wrapper"}`}>
      { isNewType && renderNewToolList() }
      { !isNewType && renderToolList() }
    </div>
  );
};

export default ToolsList;
