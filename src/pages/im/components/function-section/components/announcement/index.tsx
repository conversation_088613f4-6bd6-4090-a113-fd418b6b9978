/*
 * @Author: liuzhenkun002
 * @Date: 2021-03-18 15:06:33
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-23 16:13:44
 */

import React, { useState, useRef, useEffect } from "react";
import { Icon, Modal } from "antd-mobile";

import request from "@/common/request";
import { track } from "@/common/tracks/actions/im";

import "./index.less";

interface AnnouncementItem {
  id: number,
  title: string,
  context: string
}

interface PropTypes {
  url: string,
  moduleType: string
}

function Announcement(props: PropTypes): JSX.Element {
  const [modalShow, setModalShow] = useState(false);
  const [list, setList] = useState([]);
  const [announcement, setAnnouncement] = useState(null);
  const [detailShow, setDetailShow] = useState(false);
  const iconRef = useRef();

  const toogleBody = () => {
    const icon: any = iconRef.current;
    icon.className = detailShow ? "toggle-icon" : `${icon.className} trans`;
    setDetailShow(!detailShow);
    track(41810, { module_type: props.moduleType });
  };
  
  const showAnnouncement = (announcement: AnnouncementItem) => {
    const { id, title } = announcement;
    const moduleName = JSON.stringify({ id, title });
    setAnnouncement(announcement);
    setModalShow(true);
    track(41809, { module_type: props.moduleType, module_name: moduleName });
  };

  useEffect(() => {
    request.get(props.url)
      .then((data: any) => {
        if (data) {
          setList(data);
          const moduleContent = data.reduce((acc: any[], cur: AnnouncementItem) => {
            acc.push({ id: cur.id, title: cur.title });
            return acc;
          }, []);
          track(41807, { module_type: props.moduleType, module_content: JSON.stringify(moduleContent) });
        }
      }).catch(x => x);
  }, []);

  const AnnouncementItem = (announcement: AnnouncementItem) => {
    const { id, title } = announcement;
    return (
      <li
        className="announcement-item"
        onClick={() => showAnnouncement(announcement)}
        key={id}
      >{ title }</li>
    );
  };

  const AnnouncementFirst = (announcement: AnnouncementItem) => {
    return (
      <li className={`announcement-first ${detailShow ? "active" : ""}`}>
        <div className="announcement-icon-wrapper">
          <div className="announcement-icon">
            公告
          </div>
        </div>
        <div className="title-wrapper" onClick={() => showAnnouncement(announcement)} >
          { announcement.title }
        </div>
        {list.length > 1 && (
          <div className="tools-wrapper" onClick={toogleBody}>
            <div className="announcement-count">
              <div className="icon">{ list.length }</div>
            </div>
            <div className="toggle-icon" ref={iconRef}>
              <Icon type="left" size="xs" />
            </div>
          </div>
        )}
      </li>
    );
  };

  const AnnouncementModal = () => {
    const {title = "", context = ""} = announcement || {};
    return (
      <Modal
        title={title}
        transparent
        className="modal-lg"
        visible={modalShow}
        maskClosable={true}
        onClose={() => setModalShow(false)}
        animationType="slide-up"
        footer={[{ text: "确认", onPress: () => setModalShow(false) }]}
      >
        <p className="modal-content-wrapper">
          {context}
        </p>
      </Modal>
    );
  };

  if (!list || !list.length) return null;

  return (
    <div className="announcement-wrapper">
      <ul className="announcement-body">
        {AnnouncementFirst(list.slice(0, 1)[0])}
        {detailShow && list.slice(1).map(item => AnnouncementItem(item))}
      </ul>
      <AnnouncementModal />
    </div>
  );
}

export default Announcement;