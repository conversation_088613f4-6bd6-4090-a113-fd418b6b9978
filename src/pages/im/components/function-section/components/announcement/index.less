.announcement-wrapper {
  display: flex;
  align-items: flex-start;
  margin: 16px 16px 0 16px;
  background: #fff;
  border-radius: 4px;

  .announcement-icon-wrapper {
    display: flex;
    align-items: center;
    width: 15%;
    height: 44px;

    .announcement-icon {
      width: 32px;
      height: 20px;
      line-height: 20px;
      background: #ee502f;
      border-radius: 10px;
      font-size: 10px;
      color: #fff;
      text-align: center;
    }
  }

  .announcement-body {
    width: 100%;

    .announcement-first {
      display: flex;
      padding: 0 16px;
      
      &.active {
        border-bottom: 1px solid #ccc;
      }
  
      .title-wrapper {
        width: 75%;
        line-height: 44px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }
  
    .announcement-item {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding: 7px 16px;
      min-height: 44px;
      border-bottom: 1px solid #ccc;

      &:last-child {
        border: none;
      }
    }
  
    .tools-wrapper {
      display: flex;
      height: 44px;
  
      .announcement-count {
        display: flex;
        align-items: center;
    
        .icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 16px;
          height: 16px;
          background: #ee502f;
          border-radius: 50%;
          font-size: 10px;
          color: #fff;
        }
      }
    
      .toggle-icon {
        display: flex;
        align-items: center;
        margin-left: 5px;
    
        &.trans {
          transform: rotate(-90deg);
        }
      }
    }
  }
}

// 注意这个是否影响全局
.modal-content-wrapper {
  max-height: 50vh;
  overflow-y: scroll;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}
