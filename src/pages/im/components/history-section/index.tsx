/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-04-01 17:24:09
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-07 12:00:34
 */

import React, { useContext, useEffect, useState, useRef } from "react";
import { connect } from "react-redux";

import request from "@/common/request";
import ImApi from "@/common/service/im";
import {
  UPDATE_MSG_MAP
} from "@/common/store";

import { ImContext, MsgListContext } from "../../context";
import InitStatusMap from "../../constant/init-status-map";
import MsgItem from "../msg-item";

let scrollTimeout: NodeJS.Timeout = null;

const mapStateToProps = (state: any) => ({
  userInfo: state.user.userInfo
});

const mapDispatchToProps = (dispatch: any) => ({
  updateMsgMap: (list: any[]) => dispatch({type: UPDATE_MSG_MAP, payload: list})
})


function HistorySection(props: any): JSX.Element {
  const [historyList, setHistoryList] = useState([]);
  const { imConfig, urlParams, initStatus, isPc } = useContext(ImContext);
  const { extRegionInfos } = imConfig;
  const historyRef = useRef();
  const [upestHistoryMsgTime, setUpestHistoryMsgTime] = useState();
  
  if (!extRegionInfos) return null;

  const historyConfig = extRegionInfos.find((config: any) => (config.key === "history_msg"));
  if (!historyConfig || !historyConfig.url) return null;

  const historyMsgHandle = (historyMsgs: any) => {
    let list: any[] = [];
    historyMsgs.forEach((msg: any) => {
      // 处理撤回消息
      if (msg.revocation === 1) {
        const undoMsg = list.find(item => item.id === msg.id);
        if (undoMsg) {
          undoMsg.type = -1;
          undoMsg.data = "[该消息已撤回]";
          undoMsg.revocation = 1;
          undoMsg.msgAttr = { ...undoMsg.msgAttr, replyMsgId: "" };
        }
      } else {
        switch(msg.type) {
          case "-204":
            // 添加新气泡
            break;
          default:
            list.push(msg);
            break;
        }
      }
    });
    const totalList = list.concat(historyList);
    setHistoryList(totalList);
    props.updateMsgMap(totalList);
    scrollToLast(list);
  };

  const getMsgHistory = () => {
    const params = { spm: urlParams.spm, fromUcid: props.userInfo.ucId, lastMessageTime: upestHistoryMsgTime };
    request.get(historyConfig.url, {params})
      .then((data: any) => {
        window.dt?.sendInfo?.("接受新消息6", {
          length: data?.length,
          stage: "fetch history"
        })
        props.setHistoryDone(true);
        if (!data || !data.length) {
          props.setHasHistory(false);
          return;
        }
        const msgListLength = data.length;
        const lastHistoryMsgId = data[msgListLength-1]?.msgId;
        setUpestHistoryMsgTime(data[0]?.timestamp);
        // 请求接口send已读
        sendMsgReadState(lastHistoryMsgId);
        historyMsgHandle(data);
        props.setHasHistory(true);
      }).catch(x => x);
  };

  const sendMsgReadState = (msgId: any) => {
    const data = {
      msgId,
      spm: urlParams.spm,
      fromUcid: props.userInfo.ucId,
      msgType: 101,
      sendTime: Date.now(),
      channel: imConfig.bizInfo.bizChannel,
      clientId: imConfig.baseInfo.clientId
    };

    ImApi.sendMsg(data).catch(x => x);
  };

  const scrollToBottom = () => {
     scrollToDom("bottomPlaceholder");
  };

  const scrollToLast = (list) => {
    if (props.loadCount <= 1) {
      // 第一次加载历史记录后，滚动到最底部
      scrollToBottom();
    } else {
      // 滚动到‘当前页面’的最后一条
      scrollToDom(`msg-${list[list.length - 1]?.msgId}`);
    }
  };

  const scrollToDom = (domId: string) => {
    const bottomDom = document.getElementById(domId);
    if (!bottomDom) return;
    scrollTimeout = setTimeout(() => {
      bottomDom.scrollIntoView(true);
    }, 0);
  };

  const clearTimer = () => {
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
      scrollTimeout = null;
    }
  };

  useEffect(() => {
    if (props.loadCount > 1) {
      getMsgHistory();
    } else {
      scrollToLast(historyList);
    }
  }, [props.loadCount]);
  
  useEffect(() => {
    getMsgHistory();
    return clearTimer;
  }, []);

  if (!props.visible) return null;

  return (
    <MsgListContext.Provider value={{ isHistory: true }}>
      <div id="historyList" ref={historyRef}>
        {historyList.map((msg, index) => (<MsgItem key={`${msg.id}${index}`} {...msg} msgId={msg.msgIdSequence} scrollToBottom={scrollToBottom} isHistoryMsg={true} />))}
      </div>
    </MsgListContext.Provider>
  );
}

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(HistorySection);