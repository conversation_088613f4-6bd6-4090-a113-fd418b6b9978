#mobileWrapper{
  display: flex;
  flex-direction: column;
  .m-wrapper-box{
    flex: 1;
  }
  #mobileInput
  {
    position:static;
  }
}
.im-wrapper {
  position: relative;
  box-sizing: border-box;
  height: 100vh;
  overflow-x: hidden;
  overflow-y: scroll;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }

  #welcomeDom {
    padding-top: 10px;
  }

  .mobile-content-wrapper {
    padding-bottom: 10px;
  }
}

.comment-iframe {
  height: 400px;
}
