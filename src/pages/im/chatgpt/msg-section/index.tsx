/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-03-25 11:16:20
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-03 16:28:26
 */

import React, { useEffect, useState, useContext, useRef } from "react";
import { connect } from "react-redux";
import { Toast } from 'antd-mobile';
import { randomId, replaceProtocol } from "@/common/utils"

import ImApi from "@/common/service/im";
import {
  CHANGE_SERVICE_STATUS,
  REVERT_MSG,
  ADD_NEW_MSGS,
  ADD_BUBBLE,
  REMOVE_BUBBLE,
  CHANGE_INQUEUE_STATUS,
  UPDATE_MSG_MAP
} from "@/common/store";
import initStatusMap from "@/pages/im/constant/init-status-map";
import { ImContext, MsgListContext } from "../../context";
import SseClient from "../../../../common/utils/SseClient"
import MsgItem from "../../components/msg-item"
import MarkdownComponent from "../../components/msg-item/components/markdown";
import KnowledgeModal from "../../components/msg-section/components/knowledge-modal";

import "./index.less";
import { escape } from "lodash";

let timeout: NodeJS.Timeout = null;

const mapStateToProps = (state: any) => ({
  msgList: state.im.msgList,
  userInfo: state.user.userInfo
});

const mapDispatchToProps = (dispatch: any) => ({
  inServiceChange: (inService: boolean) => dispatch({ type: CHANGE_SERVICE_STATUS, payload: inService }),
  inQueueChange: (inQueue: boolean) => dispatch({ type: CHANGE_INQUEUE_STATUS, payload: inQueue }),
  revertMsg: (msg: any[]) => dispatch({ type: REVERT_MSG, payload: msg }),
  updateMsgMap: (list: any[]) => dispatch({type: UPDATE_MSG_MAP, payload: list}),
  addNewMsgs: (msgList: any[]) => dispatch({ type: ADD_NEW_MSGS, payload: msgList }),
  addBubble: (bubble: any) => dispatch({ type: ADD_BUBBLE, payload: bubble }),
  removeBubble: (bubble: any) => dispatch({ type: REMOVE_BUBBLE, payload: bubble })
});

function MsgSection(props: any): JSX.Element {
  const { imConfig, urlParams, isPc, initStatus } = useContext(ImContext);
  const [markdownContent, setMarkdownContent] = useState("");
  const [hasContent, setHasContent] = useState(false);
  const [evtSourceClosed, setEvtSourceClosed] = useState(false);
  const [knowModalVisible, setknowModalVisible] = useState(false);
  const [knowModalTitle, setknowModalTitle] = useState("");
  const [knowledgeUrl, setKnowledgeUrl] = useState("");
  const { disableInput, setDisableInput } = useContext(ImContext);
  const contentRef = useRef("")
  const contentTempRef = useRef("")

  // 滚到底部
  const scrollToBottom = () => {
    const bottomDom = document.getElementById("bottomPlaceholder");
    if (!bottomDom) return;
    timeout = setTimeout(() => {
      bottomDom.scrollIntoView(true);
      clearTimeout(timeout);
    }, 0);
  };


  const addNewMsgs = (addList: any[]) => {
    props.addNewMsgs(addList);
    props.updateMsgMap(addList);

    scrollToBottom();
  };

  const addSingleMsg = (msgType: number, msgPayload: any, direction?: number, msgId?: string) => {
    let data = msgPayload;
    if (typeof msgPayload === "string") {
      try {
        if (typeof JSON.parse(msgPayload) === "object") {
          data = JSON.parse(msgPayload);
        }
      } catch (err) {
        // ignore
      }
    }
    const msg = {
      id: msgId,
      direction: direction === undefined ? 1 : direction,
      type: msgType,
      data,
      manual: false // 为了direction不为1时头像为机器头像
    };
    if (msg?.type == -1) {
      msg.data = escape(msg?.data);
    }
    addNewMsgs([msg]);
  };

  /**
   *  SSE监听器
   */
  const sseListener = () => {
    const { sseChatUrl } = imConfig;

    if (sseChatUrl) {
        ImBus.on("NEW_SSE_MSG_SEND", (data: any) => {
            setDisableInput(true);
            setHasContent(true);
            setEvtSourceClosed(false);

            let content = ''
            let esId: string
            let index = 0

            let url = sseChatUrl
            const { msgPayload } = data
            url += '&msgPayload=' + msgPayload.replace(/\`/g, '\"');

            const sseClient = new SseClient(url, e => {
                content += e.data;
                contentRef.current = content;
                if (index == 0) {
                    contentTempRef.current = contentRef.current.slice(0, markdownContent.length + 1)
                    setMarkdownContent(contentRef.current.slice(0, markdownContent.length + 1));
                }
               
                // setMarkdownContent(content);
                if (!esId) {
                    esId = e.lastEventId;
                }
                scrollToBottom();
                index++;
            }, ex => {
                if (!evtSourceClosed) {
                    Toast.info("连接断开，正在重连～");
                    console.error(ex);
                }
            }, se => {
                setEvtSourceClosed(true);
                setDisableInput(false);
                // 服务端已结束
                let knowledgeList = []
                try {
                    knowledgeList = JSON.parse(se.data)
                    if (knowledgeList) {
                      knowledgeList = knowledgeList.filter(k => k.id)
                    }
                } catch(e) {
                    //
                }
                
                sseClient.close();
                setMarkdownContent('');
                const data = {
                    content: content,
                    knowledgeList: knowledgeList || [],
                    // TODO 改为和之前一致的queryId
                    queryId: esId
                };
                addSingleMsg(-218, JSON.stringify(data), 2, esId.replace('chatcmpl-', ''));
            })

        });
    }
  }

  const initEvent = () => {
    window.ImBus.on("SHOW_KONWLEDGE_MODAL", (payload: any) => {
      // 有 url 则直接跳 url，无则用 id 拼
      const { url, id, title } = payload;

      let jumpUrl = url;

      if (!jumpUrl) {
        jumpUrl = imConfig.baseInfo.knowDetailPageUrl;
        if (jumpUrl) {
          jumpUrl = jumpUrl.replace("%s", id);
        }
      }

      if (!jumpUrl) return;

      setKnowledgeUrl(jumpUrl);
      setknowModalVisible(true);
      if (title) setknowModalTitle(title);
    });
  };

  useEffect(() => {
    sseListener();
    initEvent();

  }, []);

  useEffect(() => {
    // 模拟打字机效果
    const timer = setInterval(() => {
      if (contentRef.current.length > contentTempRef.current.length) {
        setMarkdownContent(contentRef.current.slice(0, contentTempRef.current.length + 1));
        contentTempRef.current = contentRef.current.slice(0, contentTempRef.current.length + 1)
        scrollToBottom();
      }
      if (evtSourceClosed) {
        setMarkdownContent('');
        clearInterval(timer);
      }
    }, 10);

    return () => clearInterval(timer);
  }, [markdownContent]);

  return (
      <>
       {props.msgList.map((msg: any) => (<MsgItem key={`${msg.id}${msg.revocation}`} {...msg} msgId={msg.msgIdSequence} scrollToBottom={scrollToBottom} />))}
       { hasContent && !evtSourceClosed && markdownContent.length > 0 && 
        <div className="msg-wrapper service-msg">
         <img className="avatar" src={replaceProtocol(imConfig.baseInfo.robotAvatar)} />
         <div className="msg-card-base ">
            <MarkdownComponent content={markdownContent} />

            { hasContent && !evtSourceClosed  &&
              <div className="msg-wrapper service-msg loading">
                  <span /><span /><span />
              </div>
            }
         </div> 
        </div>
       }
       { hasContent && !evtSourceClosed && markdownContent.length == 0 &&
       <div className="msg-wrapper service-msg">
        <img className="avatar" src={replaceProtocol(imConfig.baseInfo.robotAvatar)} />
        <div className="msg-wrapper service-msg loading">
            <span /><span /><span />
        </div>
       </div>
       }
       

      <KnowledgeModal
        isPc={isPc}
        modalVisible = {knowModalVisible}
        knowModalTitle={knowModalTitle}
        url={knowledgeUrl}
        setModalVisible={setknowModalVisible}
      />
      </>
  )
}

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(MsgSection);