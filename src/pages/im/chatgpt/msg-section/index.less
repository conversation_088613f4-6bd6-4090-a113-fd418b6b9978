.loading span {
    display: inline-block;
    width: 5px;
    height: 5px;
    margin-right: 10px;
    border-radius: 100%;
    background-color: #999;
    animation: loading 1s ease-in-out infinite;
  }

  .loading span:nth-child(2) {
    animation-delay: 0.2s;
  }

  .loading span:nth-child(3) {
    animation-delay: 0.4s;
  }

  @keyframes loading {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.5);
      opacity: 0.5;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

