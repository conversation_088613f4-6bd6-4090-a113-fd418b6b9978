/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-03-16 18:39:22
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-05-31 16:01:20
 */
import { track } from "@/common/tracks/actions/im";
import { parseCookie } from "@/common/utils"

export default {
   // 设置页面title
  setPageTitle(title) {
    if (!$ljBridge.webStatus.isApp) {
      document.title = title;
      return;
    }

    $ljBridge.ready(function(bridge) {
      try {
        bridge.setPageTitle(title);
      } catch (error) {
        this.toast.error(error.message);
      }
    });
  },

  //唤起app登录
  actionLogin([returnUrl]) {
    $ljBridge.ready(function(bridge) {
      if (bridge.actionLogin) bridge.actionLogin(returnUrl);
    });
  },

  // 在 Link A+ 中同步登陆
  getAccessToken() {
    if (!$ljBridge.webStatus.isApp) return;
    return new Promise(resolve => {
      $ljBridge.ready(bridge => {
        const token = bridge.getAccessToken() || "";
        document.cookie = `sinan_token=${token};expires=Fri, 31 Dec 9999 23:59:59 GMT`;
        const cookies = parseCookie();
        if (!cookies.lianjia_token) {
          document.cookie = `lianjia_token=${token};expires=Fri, 31 Dec 9999 23:59:59 GMT`;
        }
        resolve(token);
      });
    });
  },

  // 验证登录
  checkLogin() {
    return new Promise(resolve => {
      $ljBridge.ready(() => resolve(cookie.get("lianjia_token")));
    });
  },

  // 跳转逻辑，app 内无法新页面打开，使用本页跳转
  openPage(url) {
    if (!$ljBridge.webStatus.isApp) {
      // 贝壳管家获取UA特殊处理
      const appName = navigator.userAgent.includes('app=')
      ? navigator.userAgent.split('app=')[1].split(',')[0]
      : '';
      if (appName.indexOf("Athena") > -1) {
        location.href = url;
        return;
      }
      window.open(url);
      return;
    }
    // 跳转之前需要取消返回以及关闭事件的监听，否则后续页面将无法返回和监听
    $ljBridge.ready((bridge, webStatus) => {
      window.closeEvent = null;
      bridge.callAndBack(
        JSON.stringify({
            actionUrl: bridge.getSchemeLink(
                'callback/subscribe?tag=closeEvent&cancel=true'
            ),
            functionName: 'closeEvent'
        })
      );
      if (/^http(s)?\:\/\//.test(url)) {
        if(navigator.userAgent.includes('Lianjia/beikejinggong') || navigator.userAgent.includes('Lianjia/beikesteward')) {
          url = bridge.getSchemeLink(`webbrowser?url=${encodeURIComponent(url)}`)
        }else {
          if (webStatus.isBeike || webStatus.isLianjiaApp) {
            url = bridge.getSchemeLink(`web/main?url=${encodeURIComponent(url)}`)
          } else {
            url = bridge.getSchemeLink(`web/compaign?htmlurlstring=${encodeURIComponent(url)}`)
          } 
        }
      }
      bridge.actionWithUrl(url)
    });
  },

  // 打开小程序页面
  openMiniPage(param) {
    window.wx && window.wx.miniProgram.navigateTo(param);
  },

  backOrClose(setShowModal, setAction, getCommentStatus) {
    if(!$ljBridge.webStatus.isApp) return;
    $ljBridge.ready(async (bridge, webStatus) => {
      window.closeEvent = async json => {
          let type = 0; // 0是返回，1是关闭
          try {
              let res = JSON.parse(json);
              if (res && res.type === 1) {
                  type = 1;
              }
          } catch {
              type = 0;
          }
          const showCommentModal = await getCommentStatus();
          // showCommentModal = true
          $ljBridge.bridge = bridge;
          if(!showCommentModal) {
            if(type) {
              bridge.closeWeb("拜拜");
            } else {
              if(window.history.length > 1) {
                window.closeEvent = null;
                bridge.callAndBack(
                    JSON.stringify({
                        actionUrl: bridge.getSchemeLink(
                            'callback/subscribe?tag=closeEvent&cancel=true'
                        ),
                        functionName: 'closeEvent'
                    })
                );
                window.history.back();
              } else {
                bridge.closeWeb("拜拜");
              }  
            }
          } else {
            setAction(type);
            setShowModal(true);
            track(47339);
          }          
      };
      bridge.callAndBack(
          JSON.stringify({
              actionUrl: bridge.getSchemeLink(
                  'callback/subscribe?tag=closeEvent&cancel=false'
              ),
              functionName: 'closeEvent'
          })
      );
  });  
  }
}