/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-04-14 16:34:50
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-20 16:39:48
 */

// dig-log 配置
window.__UDL_CONFIG={
  "pid":"crm_m_cservice",
  "uicode": "helperSaas"
};

// Buffer 队列 记录异步脚本加载完成前的埋点信息
window.$ULOG = {};
window.$ULOG.buffer = [];

if (!IS_DEV) {
  window.$ULOG.target = "//dig.lianjia.com/delta.gif";
} else {
  window.$ULOG.target = "//test.dig.lianjia.com/delta.gif";
}

window.$ULOG.send = function(evtid, param) {
  window.$ULOG.buffer.push([evtid, param]);
};

// 异步加载脚本
const ulogScript = document.createElement("script");
ulogScript.src = `//s1.ljcdn.com/dig-log/static/lianjiaUlog.js?t=${new Date().getTime()}`;
document.getElementsByTagName("head")[0].appendChild(ulogScript);
