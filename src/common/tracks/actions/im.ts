/*
 * @Author: liuzhenkun002
 * @Date: 2021-04-19 17:27:01
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-05-31 16:40:47
 */

/**
 * 埋点Action
 *
 * @description 埋点参数包括 基本参数 和 行为参数
 * @param {Object} evt 事件ID
 * @param {Object} actions 行为参数
 * @param {Object} extra 额外参数
 */

import { dateFromat } from "@/common/utils/date";
import Store from "@/common/store";
import UaHelper from "@/common/utils/ua";

const channelType = UaHelper.getAppChannel();
const sourceType = UaHelper.isMobile() ? "app" : "pc";

export const track = (evt: number, actions = {}, extra = {}) => {
  const { user: { userInfo }, im: { imConfig } } = Store.getState();
  const { ucId, sysCode } = userInfo || {};
  const { baseInfo = {}, bizInfo = {} } = imConfig || {};
  const { bizPageSnapshot, bizPageUrl, bizSource, bizSys, pageSession } = bizInfo;
  const { accessChannel } = baseInfo;
  try {
    window.$ULOG.send(evt, {
      action: {
        access_channel: accessChannel,
        page_id: pageSession,
        biz_belong: bizSys,
        biz_source: bizSource,
        channel_type: channelType,
        source_type: sourceType,
        page_url: bizPageUrl,
        page_scene: bizPageSnapshot,
        time: dateFromat(Date.now()),
        sysCode,
        ...actions
      },
      pid: "crm_m_cservice",
      ucid: ucId, // 用户 id
      event: "mElementClick", // 默认类型
      ...extra
    });
  } catch (err) {
    // ignore
  }
};


