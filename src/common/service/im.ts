/*
 * @Author: liuzhenkun002
 * @Date: 2021-03-23 14:13:34
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-05-28 15:54:30
 */

import request, { postToAsr } from "@/common/request";
import { getFileName, getFileSuffix, getFileMd5 } from "@/common/utils/file";
import { parseCookie } from "@/common/utils"
import { api, imApi } from "./api";
import { Toast } from 'antd-mobile';
import axios from "axios";

interface shortUrlParams {
  project: string,
  businessType: string,
  type: string,
  busId: string,
  params: string
}

// 后端接口的 host
let apiHost: string = API_CONFIG.apiHost || "/web/fe/imv3"
let { webHost,httpHost }: { webHost: string, httpHost: string } = API_CONFIG;
console.log('apiHost: ', apiHost, ', webHost: ', webHost);
const getUploadPath = (data: any) => {
  const { name, type } = data;
  const genData = {
    fileName: getFileName(name),
    contentType: type,
    fileSuffix: getFileSuffix(name),
    busiType: "appeal_evidence",
    scheme: location.protocol.substr(0, location.protocol.length - 1),
  };

  return request.post(
    `${webHost}${api.getUploadPath}`,
    genData,
    {
      headers: { "Content-Type": "application/x-www-form-urlencoded" }
    });
};

//获取需要Content-MD5验证文件完整性的上传协议
const getV2UploadPath = async (file: any) => {
  const { name, type } = file;
  const fileMd5 = await getFileMd5(file);
  const genData = {
    fileName: getFileName(name),
    contentType: type,
    fileSuffix: getFileSuffix(name),
    busiType: "appeal_evidence",
    scheme: location.protocol.substr(0, location.protocol.length - 1),
    md5: fileMd5
  };

  return request.post(
    `${webHost}${api.getUploadV4Path}`,
    genData,
    {
      headers: { "Content-Type": "application/x-www-form-urlencoded" }
    });
};

//上传请求，上传s3提交MD5验证文件完整性
const uploadFile = async (file: any) => {
  let pathResp: any = await getV2UploadPath(file);
  const { uploadPath, filePath, base64Code } = pathResp;
  const { name, type } = file;
  const customResponse = (data: any) => {
    if (data === "") {
      return { code: 1, data: { filePath, name, type } };
    }
  };

  return request.post(
    uploadPath,
    {},
    {
      withCredentials: false,
      headers: {
        "Content-Type": type,
        "Content-MD5": base64Code
      },
      transformRequest: [() => file],
      transformResponse: [customResponse],
      timeout: 1000 * 60 * 60
    }
  ).catch(err=>{
    Toast.info("上传失败，请重新上传！");
    console.log(err)
  });
};

const asrToText = (data, asrId: string) => {
  const url = "//lrt-asr.api.ke.com/asr";
  return postToAsr(url, data, asrId)
}

export const setApiHost = (host: string) => {
  apiHost = host || apiHost;
};

export const setWebHost = (host: string) => {
  webHost = host || webHost;
};

export default {
  getImConfig: (data: any) => request.post(`${apiHost}${imApi.getImConfig}`, data),
  // 利用接口上报前端日志
  reportLog: (data: any) => { 
    return new Promise(() => {
      axios.post(`${httpHost}${api.reportLog}`, data, {
        headers: {
          "Content-Type": "application/json",
        }
      })
    })
  },
  getToolBox: (data: any) => request.post(`${apiHost}${imApi.getToolBox}`, data),
  receiveMsg: (params: any) => request.get(`${apiHost}${imApi.receiveMsg}`, { params }),
  sendMsg: (data: any) => {
    const { select_city }= parseCookie();
    const params = select_city ? {...data, cityCode: select_city} : data;
    window.dt?.sendInfo?.("发送消息", {
      ...params,
      referrer: document.referrer
    })
    return request.post(`${apiHost}${imApi.sendMsg}`, params)
  },
  getSkillInfo: (params: any) => request.get(`${apiHost}${imApi.getSkillInfo}`, { params }),
  askCallback: (params: any) => request.get(`${apiHost}${imApi.askCallback}`, { params }),
  getUserState: (params: any) => request.get(`${apiHost}${imApi.getUserState}`, { params }),
  // 获取满意度弹窗是否弹出
  getCommentStatus: (params: { spm: string }) => request.get(`${apiHost}${imApi.getCommentStatus}`, { params }),
  // 获取满意度短链接
  getShortCommentUrl: (params: shortUrlParams) => request.post(`${webHost}${imApi.getShortCommentUrl}`, params),

  getVersionUrl: (params: any) => request.get(`${apiHost}${imApi.getVersionUrl}`, { params }),
  getImApiUrlPrefix: (params: any, headers: any) => request.get(`${httpHost}${imApi.getVersionUrl}`, { params, headers}),
  clickCard: (params: any) => {
    const { select_city }= parseCookie();
    const param = select_city ? {...params, cityCode: select_city} : params
    return request.post(`${apiHost}${imApi.clickCard}`, param)
  },
  getUploadPath,
  uploadFile,
  toBase64: (params: any) => request.get(`${webHost}${api.toBase64}`, {params}),
  asrToText,
  getABtestUrl: (params: any) => request.get(`${apiHost}${imApi.getABtestUrl}`, { params: params, timeout: 2000 }),
  saveConversationMessage: (data: any) => request.post(`${apiHost}${imApi.saveConversationMessage}`, data),
  getConversationMessages: (data: any) => request.post(`${apiHost}${imApi.getConversationMessages}`, data),
}


