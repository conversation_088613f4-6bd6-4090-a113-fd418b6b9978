/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-03-16 15:42:51
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-05-28 15:53:01
 */

export const imApi = {
  getImConfig: "/chat/init",
  getToolBox: "/chat/getTool",
  receiveMsg: "/chat/receive",
  sendMsg: "/chat/send",
  getSkillInfo: "/skillApiService/getSkillInfo",
  askCallback: "/skillApiService/askCallback",
  getUserState: "/chat/getUserState",
  getCommentStatus: "/appraise/show", // 获取用户的满意度评价状态，判断是否展示满意度弹窗
  getShortCommentUrl: "/web/fe/satisfaction/rateVisitClientImpl/getSignShortUrl",
  clickCard: "/scheme/clickCard",
  getVersionUrl: "/saas/getVersionUrl",
  getABtestUrl: "/saas/getABTestUrl", // 获取AB测试链接
  saveConversationMessage: "/AIChatAssistant/saveMessage", // 保存会话消息,
  getConversationMessages: "/AIChatAssistant/pageMessages", // 查询会话消息
};

export const api = {
  getUserInfo: "/web/loginUser/info",
  getUploadPath: "/web/fe/customer/s3ServiceImpl/generatePath",
  getUploadV4Path: "/web/fe/customer/s3ServiceImpl/generatePathV4",
  toBase64: "/web/fe/pipeline/audioToBase64Impl/convertToBase64",
  reportLog: "/anon/report",
};
