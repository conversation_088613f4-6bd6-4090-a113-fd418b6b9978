/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-04-12 18:32:33
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-27 15:56:47
 */

import {
  CHANGE_SERVICE_STATUS,
  CHANGE_INQUEUE_STATUS,
  SET_IM_CONFIG,
  REVERT_MSG,
  ADD_NEW_MSGS,
  SHOW_IMAGE_VIEWER,
  HIDE_IMAGE_VIEWER,
  SET_BUBBLES,
  ADD_BUBBLE,
  REMOVE_BUBBLE,
  SET_ACTIVE_OPT_MSG,
  UPDATE_MSG_MAP
} from './actions';
import DEFAULT_STATE from './state';

interface Action {
  type: string,
  payload: any
}

export default (state = DEFAULT_STATE, action: Action) => {
    const {type, payload} = action;
    switch(type) {
      case CHANGE_SERVICE_STATUS:
        return { ...state, inService: payload };
      case CHANGE_INQUEUE_STATUS:
        return { ...state, inQueue: payload };
      case SET_IM_CONFIG:
        return { ...state, imConfig: payload };
      case REVERT_MSG:
        const msgCopy = [...state.msgList];
        const undo = msgCopy.find(msg => msg.id === payload.id);
        undo.type = -1;
        undo.data = "[该消息已撤回]";
        undo.msgAttr = { ...undo.msgAttr, replyMsgId: "" }
        return { ...state, msgList: msgCopy };
      case ADD_NEW_MSGS:
        return { ...state, msgList: [...state.msgList, ...payload] };
      case UPDATE_MSG_MAP:
        const newMsgMap = (payload || []).reduce((pre, cur) => {
          const msgId = cur.msgId || cur.id;
          return { ...pre, [msgId]: cur }
        }, {});
        return { ...state, allMsgMap: { ...state.allMsgMap, ...newMsgMap }};
      case SHOW_IMAGE_VIEWER:
        // TODO 以后可以扩展成遍历出所有的 image 放进去
        return { ...state, showImgViewer: true, msgImages: [...payload] };
      case HIDE_IMAGE_VIEWER:
        return { ...state, showImgViewer: false };
      case SET_BUBBLES:
        return { ...state, bubbleList: payload };
      case ADD_BUBBLE:
        let findIndex = state.bubbleList.findIndex(bubble => bubble.name === payload.name);
        if (findIndex > -1) {
          const bubbleCopy = [...state.bubbleList];
          bubbleCopy[findIndex] = { ...bubbleCopy[findIndex], ...payload }
          return { ...state, bubbleList: bubbleCopy };
        }
        
        const bubbleCopy = [...state.bubbleList];
        bubbleCopy.unshift(payload)
        return { ...state, bubbleList: bubbleCopy };
      case REMOVE_BUBBLE:
        const removeCopy = [...state.bubbleList];
        const index = removeCopy.findIndex(bubble => bubble.type === payload.type);
        if(index < 0) return state;
        removeCopy.splice(index, 1);
        return { ...state, bubbleList: removeCopy };
      case SET_ACTIVE_OPT_MSG:
        return { ...state, activeOptMsg: payload };
      default:
        return state;
    }
};
