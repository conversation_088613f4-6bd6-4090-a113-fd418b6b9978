/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-04-12 17:42:29
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-19 17:34:47
 */

 // 似乎留下了个坑，把状态没有维护在 store 里，如果后续状态变得复杂则需要直接在 store 中维护状态
 // 去掉 inQueue 和 inService。 但是这样就需要在每次变换状态的时候都更新 store。需要比较仔细的梳理状态
 // 但愿后续不需要复杂到这种程度 哈哈哈。
export default {
  inQueue: false, // 是否在排队中
  inService: false, // 是否在进线中
  imConfig: null, // im 配置, 目前 store context 都放了一份，后续是否要统一？
  msgList: [], // 消息队列
  bubbleList: [], // 气泡区队列
  showImgViewer: false, // 是否展示全局 image-viewer
  activeOptMsg: null, // 当前弹出操作窗的消息 id
  msgImages: [], // 全局 image-viewer images,
  allMsgMap: {}, // 全量消息，历史+新消息，用于快速查找引用消息
};
