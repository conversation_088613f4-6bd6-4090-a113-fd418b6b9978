/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-04-12 18:32:33
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-14 17:33:07
 */

import {
  SAVE_USER_INFO
} from './actions';
import DEFAULT_STATE from './state';

interface Action {
  type: string,
  payload: any
}

export default (state = DEFAULT_STATE, action: Action) => {
    const {type, payload} = action;

    switch(type) {
      case SAVE_USER_INFO:
        return { userInfo: payload };
      default:
        return state;
    }
};
