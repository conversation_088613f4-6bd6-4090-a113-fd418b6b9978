/*
 * @Author: liuzhenkun002
 * @Date: 2021-03-16 12:05:47
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-16 14:32:10
 */

import axios from "axios";
import Qs from "qs";
import { Toast } from 'antd-mobile';
import { once, parseCookie } from "@/common/utils"
import { genUrlParams } from "@/common/utils/url";
import ImApi from "@/common/service/im";
import UaHelper from "@/common/utils/ua";

Toast.config({
  mask: false
})

const saveIMLog = (params) => {
  try{
    ImApi.reportLog({
      ...params,
      url: location.href
    })
  } catch(err) {
    console.log(err)
  }
}


// 不处理错误的白名单
const ERROR_WHITE_LIST = [
  "/saas/getVersionUrl"
];

const request = axios.create({
  baseURL: API_CONFIG.webHost,
  headers: {},
  timeout: 10000,
  withCredentials: true
});

const handleSuccess = (res: ServiceResponse) => {
  const { data: { code, data } } = res;

  switch (code) {
    case -310:
      OAuthLogin(data);
      break;
    case 1:
    case 100000: // 感觉好像没有这种
      return Promise.resolve(data);
    case 500:
      return handleError(res, "服务器内部错误");
    case 100302:
      return handleError(res, "REDIRECT");
    case 100505:
      return handleError(res, "SERVER_ERROR");
    case 100403:
      return handleError(res, "FORBIDDEN");
    case 100404:
      return handleError(res, "NOT_FOUND");
    default: {
      return handleError(res);
    }
  }
}

const handleError = (res: (ServiceResponse | any), error?: string) => {
  const { msg } = res.data || {};
  let errMsg = msg || error || ""; // 接口返回的错误优先
  errMsg = typeof errMsg === "string" ? errMsg : JSON.stringify(errMsg);
  if (errMsg) Toast.info(errMsg, 1);
  return Promise.reject({ errMsg, res });
}

const OAuthLogin = once((url: string) => {
  const { href } = location;
  if (window.$ljBridge?.webStatus?.isApp) {
    $ljBridge.ready(function (bridge, webStatus) {
      if (webStatus.isApp) {
        if (webStatus.isLinkApp || webStatus.isDeyou) {
          return bridge.actionWithUrl(bridge.getSchemeLink('login/login')) // A+、link，需要端上登录后才能h5访问页面，故走不到这个case
        } else {
          bridge.actionWithUrl(bridge.getSchemeLink('actionlogin?'+encodeURIComponent(href)));
          if (!UaHelper.isAndroid) return;
          setTimeout(async () => {
            const token = parseCookie().lianjia_token;
            const isLogin = !!(parseCookie().lianjia_token);
            if (isLogin) {
              location.reload();
            }
          }, 2000);
        }
      } 
    })
    return;
  }

  const urlParams = genUrlParams() || {};
  const { homeTheme } = urlParams;
  // 圣都，被窝等小程序登录打通异常，通过免登录接口上报异常日志
  if(["HOME", "HOME2", "TUANZHUANG"].includes(homeTheme) && window.wx) {
    saveIMLog({
      cookie: document.cookie,
      token: urlParams.token || urlParams.wx_token || "",
      stage: "unlogin",
    })
  }

  const rurl = `?rurl=${encodeURIComponent(href)}`;
  console.log("rurl: ", rurl);
  // 登录过程：前端 -> 司南网关 -> passport
  // 网关默认会对 url 做一次 decode，然后 passport 302 会忽略 # 后面的参数，直接将 ticket 参数拼接到 # 之前导致登录跳转异常
  // 所以这里又做了一次decode
  location.replace(`${url}${encodeURIComponent(rurl)}`);
});

// 添加请求拦截器
// TODO 这里有必要每个都添加 json: login 吗？
request.interceptors.request.use(
  conf => {
    // POST传参序列化
    if (conf.method === "post") {
      conf.data = Qs.stringify({
        json: "login",
        ...conf.data
      });
    } else if (conf.method === "get") {
      conf.params = {
        json: "login",
        ...conf.params
      };
    }
    return conf;
  },
  err => Promise.reject(err)
);

request.interceptors.response.use(
  res => {
    if (ERROR_WHITE_LIST.some(url => res && res.config.url.indexOf(url) > -1)) {
      const { data } = res.data || {};
      return Promise.resolve(data);
    }
    return res.status === 200 ? handleSuccess(res) : handleError(res);
  },

  err => {
    if (typeof err === "string") return; // 处理请求中断(暂时没找到更好地实现)
    const { response: res } = err || {};
    if (ERROR_WHITE_LIST.some(url => res && res.config.url.indexOf(url) > -1)) {
      handleSuccess(res);
    }

    if (!res) return handleError({}, "网络异常");
    switch (res.status) {
      case 404:
        return handleError(res, "请求不存在");
      case 502:
        return handleError(res, "网关异常");
      default:
        return handleError(res, res?.data?.message);
    };
  }
);

export const postToAsr = (url, data, asrId) => {
  return new Promise((resolve, reject) => {
    axios.post(
      url,
      data,
      {
        headers: {
          "Content-Type": "application/json",
          "Session-Id": asrId,
          "Business-Id": asrId,
          "Application-Id": "imsdk"
        },
      })
      .then(res => {
        if (res.status === 200) {
          const { session = {} } = res.data || {};
          if (session.error_code === 0) {
            resolve(session.sentence);
          } else {
            reject(session.message);
          }
        }
      })
      .catch(err => {
        reject(err);
      })
    })
}

export default request;