/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-04-01 12:06:45
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-01 14:40:42
 */

// 简易 event bus
// 给不在 MsgListContext 中却想要调用其中方法的组件提供能力
// 有没有更好的方式？

export default class ImBus {
  constructor() {
    this.events = {};
  }

  on = (key, callback) => {
    if (Object.prototype.toString.call(callback) !== "[object Function]") {
      return;
    }

    const event = this.events[key];
  
    if (!event) {
      this.events[key] = [];
    }

    this.events[key].push(callback);
  };
  
  emit = (key, data) => {
    const callbacks = this.events[key] || [];
    callbacks.forEach(cb => cb(data));
  }
}
