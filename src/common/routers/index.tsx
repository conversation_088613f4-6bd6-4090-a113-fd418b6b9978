import Im from "@/pages/im/index";
import Anony from "@/pages/im/anonymous/index";
import Chatgpt from "@/pages/im/chatgpt/index";
import ChatAgent from "@/pages/im/agent-chat/components/chat-agent";
import ConversationShare from "@/pages/im/agent-chat/components/history-share";

const routers = [
  {
    path: "/",
    exact: true,
    component: Im
  },
  { // 处理 helper/saas 来的
    path: "/saas",
    exact: true,
    component: Im
  },
  { // 处理 /chat 来的
    path: "/chat",
    exact: true,
    component: Chatgpt
  },
  {
    path: "/chat-agent",
    exact: true,
    component: ChatAgent
  },
  { // 会话分享
    path: "/conversation/share",
    exact: true,
    component: ConversationShare
  },
  { // 处理 匿名（游客）来的
    path: "/anon",
    exact: true,
    component: Anony
  },
  { path: "*", exact: true, redirect: "/" },
];

export default routers;
