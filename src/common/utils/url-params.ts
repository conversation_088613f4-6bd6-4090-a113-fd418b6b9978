/*获取地址中参数*/
export function getParam(str: string) {
  const queries: Record<string, string> = {}
  if (!str) {
    return {}
  }
  str.split('&').forEach((c) => {
    if (c === '') {
      return
    }
    const [key, ...values] = c.split('=')
    let decoded<PERSON>ey
    let decodedValue
    try {
      decodedKey = decodeURIComponent(key)
    } catch (err) {
      decodedKey = key
    }
    try {
      decodedValue = decodeURIComponent(values.join('='))
    } catch (err) {
      decodedValue = values.join('=')
    }
    queries[decodedKey] = decodedValue || ''
  })
  return queries
}
export const urlQuery = () => {
  return parseQuery(window.location.href)
}

export const parseQuery = (url: string) => {
  const hash = url;
  const index = url.indexOf("?") + 1;
  const params = hash.substring(index);
  const mIndex = params.indexOf("#");
  return mIndex > -1 ? getParam(params.substring(0, mIndex)) : getParam(params)
}