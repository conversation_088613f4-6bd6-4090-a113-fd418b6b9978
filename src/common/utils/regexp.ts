/*
 * @Author: liuzhenkun002
 * @Date: 2021-04-20 17:14:08
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-22 12:23:47
 */

export const regStrFormat = (str: string) => {
  const reg = /(\+|\(|\)|\[|\]|\{|\}|\.|\*|\^|\$)/g;
  return str.replace(reg, (match: string) => (`\\${match}`));
};

export const blankTrim = (str: string) => str.replace(/(^\s*)|(\s*$)/g, "");


// 从appUrl中提取appCode
export const getAppCodeFromAppUrl = (appUrl: string) => {
  const match = appUrl.match(/[?&]appCode=([^&]*)/);
  return match ? match[1] : '';
};
 