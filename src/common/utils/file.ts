/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-04-13 15:02:21
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-13 15:03:30
 */
import { hex_md5 } from "@/common/utils/md5"

// 获取文件后缀
export function getFileSuffix(name: string) {
  return /([^./\\]+)\.([a-z3-4]+)$/i.exec(name).pop();
}

// 获取文件名
export function getFileName(name: string, needSuffix = false) {
  const rest = name.split("/").pop();
  return needSuffix ? rest : rest.replace(/\.([a-z3-4]+)$/i, "");
}

// 获取文件的md5
export function getFileMd5(file: any) {
  return new Promise(function (resolve, reject) {
    let reader = new FileReader()
    reader.readAsBinaryString(file)
    reader.onload = function () {
      const fileMd5 = hex_md5(reader.result);
      resolve(fileMd5)
    }
  })
}
// 获取图片相关信息（异步方法）。
export const getPreviewImgSizeInfoList = (list: [object]) => {
  if (!Array.isArray(list)) return 'needs Array';

  let promiseAll: Array<object> = [];
  list.forEach((imgItem, index) => {
    promiseAll[index] = new Promise(resolve => {
      let img: {src: any, complete: any, onload: any } = new Image()
      img.src = imgItem.src
      if (img.complete) return resolve(img)
      img.onload = () => resolve(img)
    })
  }) 
  return Promise.all(promiseAll).then(imageList => {
    const newImageList:any = imageList.map((img: {width: any, height: any, src: any}) => ({
      w: img.width,
      h: img.height,
      src: img.src,
      title: ""
    }));
    return newImageList;
  }) 
}