/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-04-22 11:28:46
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-22 14:41:18
 */

import xss from "xss";

// 过滤事件
const eventReg = /^on/;

// 黑名单，这些标签会被过滤
const xssTag = [
  "script",
  "input",
  "select",
  "link",
  "command",
  "dir",
  "form",
  "meta",
  "iframe",
  "output",
  "noframes",
  "noscript",
  "optgroup",
  "rt",
  "rp",
  "ruby",
  "style",
  "textarea",
  "button",
  "object"
];

const xssOptions = {
  // 属性处理
  onIgnoreTagAttr(tag: string, name: string, value: any) {
    return eventReg.test(name) ? "" : `${name}="${value}"`;
  },

  //标签处理
  onIgnoreTag(tag: string, html: string) {
    // 不在黑名单中的正常返回
    const tagLowerCase = tag.toLocaleLowerCase();
    if (!xssTag.includes(tagLowerCase)) {
      return html;
    }
  }
};

const xssInstance = new xss.FilterXSS(xssOptions);

const xssFilter = (str: string) => {
  if (!str) return str;
  return xssInstance.process(str, xssOptions);
};

export default xssFilter;