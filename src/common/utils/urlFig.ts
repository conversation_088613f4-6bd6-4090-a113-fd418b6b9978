/*
 * @file config
 */
const config = (() => {
  const host = location.host;
  if (host.includes('local') || host.includes('dev')) {
    return {
      aigcHost: '//im-saas-dev.ke.com:8080',
      gatewayHost: '//app-luoshu.test.ttb.test.ke.com',
      chatHost: '//im-saas-dev.ke.com',
    };
  }
  else if (host.includes('test')) {
    return {
      aigcHost: '//sinan-ai-ability.test.ttb.test.ke.com',
      gatewayHost: '//app-luoshu.test.ttb.test.ke.com',
      chatHost: '//im-saas-cdn.docker-400.ttb.test.ke.com',
    }
  } else if (host.includes('preview')) {
    return {
      aigcHost: 'https://preview-sinanai.ke.com',
      gatewayHost: 'https://preview01-app-luoshu.ke.com',
      chatHost: 'https://preview01-shelper.ke.com',
    };
  }
  else {
    return {
      aigcHost: 'https://sinanai.ke.com',
      gatewayHost: 'https://luoshu.ke.com',
      chatHost: 'https://shelper.ke.com',
    };
  }
})();

export const aigcHost = config.aigcHost;
export const gatewayHost = config.gatewayHost;
export const chatHost = config.chatHost;
