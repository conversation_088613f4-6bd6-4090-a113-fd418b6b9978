/*
 * @Author: liuzhenkun002
 * @Date: 2021-03-31 17:15:26
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-06 15:27:16
 */

export const genUrlParams = (url?: string) => {
  const realUrl = url || window.location.href;
  const index = realUrl.lastIndexOf("?");
  if (index === -1) return null; // 无参数
  const search = realUrl.substring(index);
  const result: any = {};
  const matchList = search.match(/([^?&=]+)=([^?&=]*)/g);
  if (!matchList || !matchList.length) return null; // 无参数
  matchList.forEach(match => {
    const [key, value] = match.split("=");
    const decodeKey = decodeURIComponent(key);
    const decodeValue = decodeURIComponent(value);
    result[decodeKey] = decodeValue;
  });
  return result;
};

export const genUrl = (host: string, params: any) => {
  const keys = Object.keys(params);
  let paramStr = keys.reduce((acc, cur) => {
    return `${acc}${cur}=${encodeURIComponent(params[cur])}&`;
  }, "?");

  // 干掉最后多的 &
  paramStr = paramStr.substring(0, paramStr.length - 1);

  return `${host}${paramStr}`;
};