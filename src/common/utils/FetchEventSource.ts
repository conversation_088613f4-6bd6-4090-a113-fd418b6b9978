import { fetchEventSource,EventStreamContentType } from '@microsoft/fetch-event-source';

class RetriableError extends Error { }
class FatalError extends <PERSON>rror { }


export default class FetchEventSourceClient {
    // 定义字段
    private url: string;
    private messageCallback: any;
    private errorCallback: any;
    private serverCloseCallback: any;
    private controller: AbortController;

    constructor(url, messageCallback, errorCallback, serverCloseCallback) {
      this.url = url;
      this.messageCallback = messageCallback;
      this.errorCallback = errorCallback;
      this.serverCloseCallback = serverCloseCallback;
      this.controller = new AbortController();
    }
  
    connect(data) {
      fetchEventSource(this.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data),
        signal: this.controller.signal,
        credentials: 'include',
        openWhenHidden: true,

        async onopen(response) {
          console.log("onopen:", response);
          if (response.ok && response.headers.get('content-type').includes(EventStreamContentType)) {
              return; // everything's good
          } else if (response.status >= 400 && response.status < 500 && response.status !== 429) {
              // client-side errors are usually non-retriable:
              throw new FatalError();
          } else {
              throw new RetriableError();
          }
        },

        onmessage: (event) => {
          this.messageCallback(event);
        },
        onerror: (error) => {
          this.errorCallback(error);
          console.error('SSE Error:', error);
        },
        onclose: () => {
          if (this.serverCloseCallback) {
            this.serverCloseCallback();
          }
          console.log('Connection closed by the server');
        }
      });
    }
  
    close() {
      this.controller.abort();
      console.log('Connection abort');
    }
   
  }
  
