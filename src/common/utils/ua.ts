/*
 * @Author: l<PERSON><PERSON>kun002
 * @Date: 2021-05-31 15:41:26
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-05-31 16:13:18
 */

const ua = window.navigator.userAgent.toLowerCase();


// 其实这些数据提前计算出来感觉也没问题
export default {
  ua,
  isMobile: () => /harmony|android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(ua),
  isAndroid: /android/i.test(ua),
  // 是否在小程序webview内
  isMiniProgram: (/miniProgram/i).test(ua),
  getAppChannel() {
    const { webStatus } = $ljBridge;
    if (ua.indexOf("wxwork") > -1) return "WxWrok";
    if (ua.indexOf("dtsaas") > -1) return "PcA+";
    if (webStatus.isLianjiaApp) return "LianjiaApp";
    if (webStatus.isLinkApp) return "Link";
    if (webStatus.isDeyou) return "A+";
    if (webStatus.isBeike) return "BeiKe";
    if (webStatus.isBaichuan) return "BaiChuan";
    if (webStatus.isAtom) return "Atom"
    if (webStatus.isVrstudio) return "Vr Webview";
    if (webStatus.isDecorateHome) return "DecorateHome";
    return "Web";
  }
}