export const randomId = (len = 24, radix = 24) => {
  const chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ".split(
    ""
  );
  const uuid = [];
  let i;
  radix = radix || chars.length;
  if (len) {
    for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)];
  } else {
    let r;
    uuid[8] = "-";
    uuid[13] = "-";
    uuid[18] = "-";
    uuid[23] = "-";
    uuid[14] = "4";
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | (Math.random() * 16);
        uuid[i] = chars[i === 19 ? (r & 0x3) | 0x8 : r];
      }
    }
  }
  return uuid.join("");
};

export const parseCookie = () => {
  const { cookie } = document;
  let cookies = {};
  if (!cookie) {
    return cookies;
  }
  const list = cookie.split(";");

  cookies = list.reduce((prev, curr) => {
    const [key, val] = curr.split("=");
    return { ...prev, [key.trim()]: val };
  }, {});
  return cookies;
};

/**
 * 只执行一次函数
 * @param {Function}
 * @return {Function}
 */
export const once = fn => {
  let called = false;
  return function(...args) {
    if (called) return;
    called = true;
    return fn.apply(this, args);
  };
};

export const replaceProtocol = (url) => {
  if (!url) return url;
  if (url.startsWith("http://")) {
    const currentProtocol = window.location.protocol;
    const newUrl = url.replace(/^https?:\/\//, currentProtocol + "//");
    return newUrl;
  }
  return url;
};