// 初始化配置，详细配置在下面有具体说明
export default ({ ucId } = {}) => {
  const { dt } = window;
  if (!dt || !dt.set || !dt.detect) {
    console.error("dt is error");
    return;
  }

  dt.set({
    // 项目id, 由灯塔项目组统一分配
    pid: 'sinan_im_saas',
    // 用户ucid, 用于发生异常时追踪用户信息/计算系统用户数. 一般在cookie中可以取到(lianjia_uuid), 没有可传空字符串
    ucid: ucId,
    // 是否为测试数据, 默认为false(测试模式下打点数据仅供浏览, 不会展示在系统中)
    env: location.host.indexOf("helper.ke.com") > -1 ? "production": "testing",
    // 静默模式：该模式下dev环境控制台不再打印log
    silent: false,
    // [可选]业务方的js版本号, 会随着打点数据一起上传, 方便区分数据来源
    // 可以不填, 默认为1.0.0
    version: '1.0.0',
    // [必填]设备唯一id, 不填则由sdk统一在cookie中生成一份设备id
    // 用于计算设备/浏览器/系统版本分布数据, 不填则无法区分不同设备的打点信息. 
    // 一般在cookie中可以取到(lianjia_uuid), 没有uuid可用设备mac/idfa/imei替代
    uuid: '',
    // [可选]在这里控制具体的上报数据种类, 可以按需关闭 
    record: {
      webVitals: true,
      // 是否为单页应用，默认false 【1.4.0新增，用于修正业务pv统计不准确的问题】
      spa: true,
      // 是否记录用户操作 toC 应用禁止打开此开关，涉及用户隐私问题 【1.5.0-tenet.0 新增】
      record_operation: false,
      // 是否监控用户在线时长数据, 默认为true
      time_on_page: true,
      // 是否监控页面载入性能, 默认为true
      performance: true,
      //  是否监控页面报错信息, 默认为true
      js_error: true,
      // 配置需要监控的页面报错类别, 仅在js_error为true时生效, 默认均为true(可以将配置改为false, 以屏蔽不需要上报的错误类别)
      js_error_report_config: {
        // js运行时报错
        ERROR_RUNTIME: true,
        // js资源加载失败
        ERROR_SCRIPT: true,
        // css资源加载失败
        ERROR_STYLE: true,
        // 图片资源加载失败
        ERROR_IMAGE: true,
        // 音频资源加载失败
        ERROR_AUDIO: true,
        // 视频资源加载失败
        ERROR_VIDEO: true,
        // vue运行时报错
        ERROR_CONSOLE: true,
        // 未catch错误
        ERROR_TRY_CATCH: true,
        // 自定义检测函数, 上报前最后判断是否需要报告该错误
        // 回调函数说明
        // 传入参数 => 
        //            desc:  字符串, 错误描述
        //            stack: 字符串, 错误堆栈信息
        //            reason:  字符串, 仅错误类型为 unhandledrejection 时传入该参数 【1.4.2、@lianjia/fee-sdk新增】
        //            message: 字符串, 仅错误类型为 unhandledrejection 时传入该参数 【1.4.2、@lianjia/fee-sdk新增】
        // 返回值 =>  
        //            true  : 上报打点请求
        //            false : 不需要上报
        checkErrorNeedReport: function (desc, stack, reason, message) {
          return true
        }
      },
      // 接口请求监控配置 【1.4.0新增，用于统计接口请求状况】
      api_report_config: {
        // 是否启用，默认为true
        enable: true,
        // 接口请求日志是否上报请求体
        withBody: false,
        // 接口请求日志是否上报响应体
        withResp: false,
        // 数据采样率，默认为1
        sampleRate: 1,
        /* 接口请求过滤方法
         * 
         * params 格式如下
          {
            url: '', // api url，
            status: 200, // http status
            response: '' // api 的响应，字符串
          }
         */
        reportFilter: function (param) {
          let customParams = param
          // 返回值可以是 false 或者 Object 对象。返回false 则此次api数据直接丢弃
          return customParams
        }
      }
    },
    // [可选]将不同url归并为同一类页面
    // 对于如同
    // xxx.com/detail/1.html
    // xxx.com/detail/2.html
    // xxx.com/detail/3.html
    // ...
    // 这种页面来说, 虽然url不同, 但他们本质上是同一个页面
    // 因此需要业务方传入一个处理函数, 根据当前url解析出真实的页面类型(例如: 二手房列表/经纪人详情页), 以便灯塔系统对错误来源进行分类
    // 回调函数说明
    // 传入参数 => window.location
    // 返回值 => 对应的的页面类型(50字以内, 建议返回汉字, 方便查看), 默认是返回当前页面的url
    getPageType: function (location) { return `${location.host}${location.pathname}` }
  })

  /** 
 * 检测页面是否出现白屏问题
 *  @param {HTMLElement} target：必填。需要监测的DOM节点
 *  @param {Object} notify：必填。错误信息上报配置
 *    errorName：[String]必填。上报的错误名称
 *    url：[String]必填。默认为当前页面的url
 *    extraInfo：[Object]选填。其他你想要提交到打点请求中的信息，会在相应的报错信息中展示。
 *  @param {Object} config: 选填。参数含义参见参考链接
 *    subtree: true,
 *    childList: true,
 *    attributes: false,
 *    characterData: false,
 *    timeout:监测变动的超时时间，若超过该时间之后仍然没有检测到页面dom变动，则视为白屏。默认值： 5000ms
 *  @param {Function} cb: 选填。需要执行的业务回调，会在检测到DOM变动时或者超过超时时间未检测到DOM变动时执行。当检测到DOM变动后，回调中会被注入两个参数（未检测到则只会传入第一个参数）：
 *    mo: Mutation Observer的实例，可以使用该实例的disconnect方法关闭DOM变动检测
 *    records: MutationRecord实例，这是一个数组，当且仅当DOM变动时才会回传该参数
 */
  dt.detect(
    document.getElementById("root"),
    {
      errorName: "加载页面异常_WhiteScreen",
      url: `${window.location.href}`,
      extraInfo: {}
    },
    {
      subtree: true,
      childList: true,
      attributes: false,
      characterData: false,
      timeout: 3 * 1000
    },
    () => {}
  );
}