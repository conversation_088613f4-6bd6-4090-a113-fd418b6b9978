/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-03-19 15:45:06
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-04-02 12:14:40
 */

import dayjs from "dayjs";

export const dateFormatType = {
  date: "YYYY-MM-DD",
  dateTime: "YYYY-MM-DD HH:mm:ss",
  dataTime2: "YYYY/MM/DD HH:mm:ss",
  time: "HH:mm:ss"
};

export const dateFromat = (timestamp = Date.now(), formatType = dateFormatType.dateTime) => {
  return dayjs(timestamp).format(formatType);
}
