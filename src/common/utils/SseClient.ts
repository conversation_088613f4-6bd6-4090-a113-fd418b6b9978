export default class SseClient {
    private es: EventSource
    constructor(url: string, messageCallabck: any, errorCallback: any, serverCloseCallback?: any) {
        this.es = new EventSource(url, {
            withCredentials: true
          })
        this.es.addEventListener('message', messageCallabck)
        this.es.addEventListener('error', errorCallback)
        if (serverCloseCallback) {
            this.es.addEventListener('close', serverCloseCallback)
        }
    }

    close() {
        this.es.close()
    }

    addEventListener(event: string, callback: any) {
        this.es.addEventListener(event, callback)
    }
}