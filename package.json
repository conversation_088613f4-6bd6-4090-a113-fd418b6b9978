{"name": "luoshu-agent-chat", "version": "1.0.0", "description": "洛书AI Chatbot", "main": "app.tsx", "scripts": {"test": "jest", "start": "cross-env NODE_ENV=development NODE_EM=dev webpack serve --color --config ./webpack/webpack.dev.config.js --host 0.0.0.0", "build.test": "cross-env NODE_ENV=development NODE_EM=test webpack --config ./webpack/webpack.prod.config.js", "build.preview": "cross-env NODE_ENV=production NODE_EM=preview webpack --config ./webpack/webpack.prod.config.js", "build": "cross-env NODE_ENV=production NODE_EM=prod webpack --config ./webpack/webpack.prod.config.js ", "analyze": "webpack --config ./webpack/webpack.analyze.js"}, "repository": {"type": "git", "url": "*******************:base-fe/odin-platform/dataFactoryWebIDE.git"}, "keywords": ["webpack5", "ide", "datafactory"], "author": "jadestar", "license": "ISC", "devDependencies": {"@ant-design/compatible": "^1.0.8", "@ant-design/icons": "^4.3.0", "@babel/cli": "^7.12.10", "@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.12.12", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/preset-env": "^7.12.11", "@babel/preset-react": "^7.12.10", "@babel/register": "^7.12.10", "@types/node": "^14.14.21", "@types/webpack-env": "^1.16.0", "@typescript-eslint/eslint-plugin": "^4.11.0", "@typescript-eslint/parser": "^4.11.0", "antd-dayjs-webpack-plugin": "^1.0.4", "axios": "^0.21.1", "babel-loader": "^8.2.2", "babel-plugin-import": "^1.13.3", "case-sensitive-paths-webpack-plugin": "^2.3.0", "clean-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^8.1.1", "core-js": "^3.8.2", "css-loader": "^5.0.1", "eslint": "^7.22.0", "html-webpack-plugin": "^5.0.0-beta.5", "less": "^4.1.1", "less-loader": "^8.0.0", "loadsh": "0.0.4", "mini-css-extract-plugin": "^1.3.4", "node-notifier": "^9.0.0", "react-hot-loader": "^4.13.0", "speed-measure-webpack-plugin": "^1.3.3", "style-loader": "^2.0.0", "thread-loader": "^3.0.1", "typescript": "^4.2.3", "webpack": "^5.14.0", "webpack-bundle-analyzer": "^4.3.0", "webpack-cli": "^4.3.1", "webpack-dev-server": "^3.11.2", "webpack-merge": "^5.7.3", "webpackbar": "^5.0.2"}, "dependencies": {"@babel/preset-typescript": "^7.12.7", "@ke/voice-cmd": "^1.0.11", "@lianjia/diglog-js": "^3.8.2", "@lianjia/jsbridge-core": "^0.2.9", "@microsoft/fetch-event-source": "^2.0.1", "@milkdown/crepe": "^7.15.2", "@types/lodash": "^4.14.168", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-router-dom": "^5.1.6", "antd-mobile": "^2.3.4", "clipboard": "^2.0.11", "cross-env": "^7.0.3", "dayjs": "^1.10.4", "eventsource-polyfill": "^0.9.6", "highlight.js": "^11.8.0", "lodash": "^4.17.21", "markdown-it": "^13.0.1", "markdown-it-link-attributes": "^4.0.1", "moment": "^2.30.1", "punycode": "^2.3.1", "qs": "^6.10.1", "react": "^17.0.1", "react-dom": "^17.0.1", "react-draggable": "^4.4.3", "react-intersection-observer": "^9.16.0", "react-onclickoutside": "^6.10.0", "react-photoswipe": "^1.3.0", "react-redux": "^7.2.3", "react-router-dom": "^5.2.0", "react-tooltip": "^5.28.0", "react-typewriter-hook": "^1.0.1", "react-viewer": "^3.2.2", "redux": "^4.0.5", "vconsole": "^3.4.1", "xss": "^1.0.8"}}