#!/bin/bash

## build产物在dist，重命名方便后面挪动文件
rm -rf disttemp
mv dist disttemp

# server文件放在server中, htdocs是nginx开放出去的目录，谨慎放置文件
mkdir -p dist/server/htdocs/
## html是构建产出，在cdn文件处理时被mv到了client目录
mv disttemp/index.html dist/server/htdocs/
mv disttemp/introduction.html dist/server/htdocs/

# # cdn文件放在client中
mkdir -p dist/client/
mv disttemp/* dist/client

# nginx配置文件放到server目录中
# 如果不同环境要使用不同的配置文件，可以通过环境变量 $NG_CONF_NAME 指定配置文件 
if [[ -n "$NG_CONF_NAME" && -f "public/$NG_CONF_NAME.conf" ]]; then
    cp "public/$NG_CONF_NAME.conf" dist/server/default.conf
else
    cp "public/default.conf" dist/server/default.conf
fi

