#!/bin/bash
START_TIME=`date +%s`
SPLIT_LINE="\n##############################\n"
function log_time(){
  if [ $1 = 'start' ]
  then 
    START_TIME=`date +%s`
    echo $SPLIT_LINE
    echo "    $2开始"
    echo $SPLIT_LINE
  elif [ $1 = 'end' ]
  then
    CURRENT_TIME=`date +%s`
    COST_TIME=$(($CURRENT_TIME - $START_TIME))
    START_TIME=$CURRENT_TIME
    echo $SPLIT_LINE
    echo "    $2结束，共执行了 $(($COST_TIME/60))m$(($COST_TIME%60))s"
    echo $SPLIT_LINE
  fi
}