/* eslint-disable import/no-extraneous-dependencies */
const path = require('path');

const {basePath, distPath} = require('./config/paths-config');

const styleLoaders = require('./loaders/style-loaders');
const jsLoaders = require('./loaders/js-loaders');
const fileLoaders = require('./loaders/file-loaders');
const { IS_PROD, FILE_EXTENSIONS } = require('./constants')
const { aliasConfig } = require('./config/alias-config');
const config = require(`../config/${process.env.NODE_EM}.js`);

module.exports = {
    mode: process.env.NODE_ENV,
    cache: { type: 'filesystem' },
    devtool: IS_PROD ? 'cheap-module-source-map' : 'source-map',
    context: basePath,
    stats: {
      all: false,
      errors: true,
      warnings: true,
      errorDetails: true,
      assets: true,
      hash: true,
      builtAt: true,
      colors: true,
    },
    entry: {
      index: './src/index.tsx',
    },
    output: {
      path: distPath,
      publicPath: config.CDN_PATH,
      filename: IS_PROD ? 'js/[name].[contenthash].js' : 'js/[name].js',
      chunkFilename: IS_PROD ? 'js/[name].[contenthash].js' : 'js/[name].js',
      // 灯塔监控要求所有按需加载js需要允许跨域访问，才能监控错误
      crossOriginLoading: 'anonymous'
    },
    module: {
      rules: [...styleLoaders, ...jsLoaders, ...fileLoaders],
    },
    resolve: {
      alias: aliasConfig,
      extensions: FILE_EXTENSIONS,
      fallback: {
        punycode: require.resolve('punycode/')
      }
    }
};
