// 开发环境的plugins配置
const webpack = require("webpack")
const HtmlWebpackPlugin = require("html-webpack-plugin")
const CaseSensitivePathsPlugin = require("case-sensitive-paths-webpack-plugin")
const basePlugins = require("./base-plugins")
const CopyWebpackPlugin = require("copy-webpack-plugin");

const path = require("path");
const {basePath} = require("../config/paths-config");
const devPlugins = [
    new HtmlWebpackPlugin({
        filename: "index.html",
        template: path.join(basePath, "/public/index.html"),
        chunksSortMode: "none"
      }),
    new webpack.HotModuleReplacementPlugin(),
    new CaseSensitivePathsPlugin(),
    new CopyWebpackPlugin(
      {
        patterns: [
          { from: path.join(basePath, '/public/introduction.html')},
          { from: path.join(basePath, '/public/static'), to: 'static'}
        ]
      })
]

module.exports = basePlugins.concat(devPlugins);