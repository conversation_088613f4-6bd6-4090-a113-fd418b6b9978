const webpack = require("webpack");
const path = require("path");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");
// const { WebpackManifestPlugin } = require("webpack-manifest-plugin");
const WebpackBar = require("webpackbar");
const AntdDayjsWebpackPlugin = require("antd-dayjs-webpack-plugin");
const CopyWebpackPlugin = require("copy-webpack-plugin");

// 根据参数动态获取配置参数
const config = require(`../../config/${process.env.NODE_EM}.js`);

module.exports = [
  new CleanWebpackPlugin(),
  // new WebpackManifestPlugin({
  //     fileName: "cdnResource.json",
  // }),
  new webpack.DefinePlugin({
    API_CONFIG: JSON.stringify(config.BASE_API),
    IS_DEV: (process.env.NODE_EM !== "prod" && process.env.NODE_EM !== "preview")
  }),
  new WebpackBar(),
  new AntdDayjsWebpackPlugin(),
];