// 线上环境的plugins配置
// const WorkboxPlugin = require('workbox-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const basePlugins = require('./base-plugins');

const path = require('path');
const { basePath } = require('../config/paths-config');
const prodPlugins = [
  new HtmlWebpackPlugin({
    filename: 'index.html',
    template: path.join(basePath, '/public/index.html'),
    inject: true,
    minify: {
      removeComments: true,
      collapseWhitespace: true,
      // more options:
      // https://github.com/kangax/html-minifier#options-quick-reference
    }
  }),
  new MiniCssExtractPlugin({
    filename: 'css/[name].[contenthash].css',
    chunkFilename: 'css/[id].[contenthash].css',
    ignoreOrder: true,
  }),
  new CopyWebpackPlugin(
    {
      patterns: [
        { from: path.join(basePath, '/public/introduction.html')},
        { from: path.join(basePath, '/public/static'), to: 'static'}
      ]
    })
];

module.exports = basePlugins.concat(prodPlugins);
