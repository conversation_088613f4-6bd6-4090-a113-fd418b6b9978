'use strict'
const webpack = require('webpack');
const path = require('path');
const baseWebpackConfig = require('./webpack.base.config');
const { merge } = require('webpack-merge');
const devPlugins = require('./plugins/dev-plugins');
const { IS_PROD } = require('./constants');

module.exports = merge(baseWebpackConfig, {
  optimization: {
    moduleIds: 'deterministic',
    chunkIds: 'deterministic',
    minimize: IS_PROD,
  },
  plugins: devPlugins,
  devServer: {
    port: 80,
    quiet: false,
    contentBase: path.resolve(__dirname, '../dist'),
    hot: true,
    disableHostCheck: true,
    noInfo: false,
    inline: true,
    overlay: true,
    publicPath: '/',
    stats: {
      all: false,
      errors: true,
      warnings: true,
      errorDetails: true,
      // chunks: true,
      assets: true,
      hash: true,
      builtAt: true,
      colors: true,
    },
    host: 'localhost',
    historyApiFallback: true,
    open: true,
    clientLogLevel: 'error',
    progress: false,
  },
})
