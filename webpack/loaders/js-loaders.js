const path = require('path');
const { basePath } = require('../config/paths-config');
const threadLoader = require('./thread-loader');

module.exports = [
  {
    test: /\.(j|t)sx?$/,
    exclude: /node_modules/,
    include: [path.join(basePath, './src/')],
    use: [
      threadLoader(),
      {
        loader: 'babel-loader',
        options: {
          cacheDirectory: true,
          babelrc: false,
          presets: [
            [
              '@babel/preset-env',
              // https://github.com/babel/babel/blob/master/packages/babel-preset-env/data/plugins.json#L32
              {
                targets: {
                  browsers: ['chrome >= 47'],
                },
                useBuiltIns: 'usage',
                corejs: 3,
              },
            ],
            '@babel/preset-typescript',
            '@babel/preset-react',
          ],
          plugins: [
            [
              "import",
              { libraryName: "antd-mobile", style: "css" }
            ],
            ['@babel/plugin-proposal-decorators', { legacy: true }],
            ['@babel/plugin-proposal-class-properties', { loose: true }],
            '@babel/plugin-syntax-dynamic-import',
          ],
        },
      },
    ],
  }
];
