const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const { basePath } = require('../config/paths-config');
const { IS_PROD } = require('../constants')
module.exports = [
  {
    test: /\.(css|less)$/,
    // exclude: /node_modules/,
    include: [
      path.join(basePath, '/node_modules/antd-mobile'),
      path.join(basePath, '/node_modules/@ant-design-mobile'),
      path.join(basePath, '/node_modules/normalize.css/normalize.css'),
      path.join(basePath, '/node_modules/react-photoswipe/lib/photoswipe.css'),
      path.join(basePath, '/src'),
    ],
    use: [
      IS_PROD ? MiniCssExtractPlugin.loader : 'style-loader',
      {
        loader: 'css-loader?modules',
        options: {
          modules: false,
          import: true,
          importLoaders: 1,
        },
      },
      {
        loader: 'less-loader?modules',
        options: {
          lessOptions: {
            javascriptEnabled: true
          },
        },
      },
    ]
  }
]