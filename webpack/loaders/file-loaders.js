const path = require('path');
const {basePath} = require('../config/paths-config');
// 初级版
module.exports = [
  {
    test: /\.(jpe?g|png|gif|svg|ico|ttf|woff|woff2)$/i,
    type: 'asset',
    include: [
      path.join(basePath, './src/'),
      path.join(basePath, '/node_modules/react-photoswipe/'),
      path.join(basePath, '/node_modules/katex/')
    ],
    parser: {
      dataUrlCondition: {
        maxSize: 4 * 1024,
      },
    },
  },
];
