const { IS_DEV } = require('../constants');

const cpus = require('os').cpus();

console.log("IS_DEV:",IS_DEV);
// node-sass 中有个来自 Node.js 线程池的阻塞线程的 bug。 当使用 thread-loader 时，需要设置 workerParallelJobs: 2
// https://webpack.docschina.org/guides/build-performance/#sass
const threadLoader = (workerParallelJobs, timeout) => {
    const options = { 
        workers: cpus,
        workerParallelJobs: workerParallelJobs || 2,
        poolTimeout: timeout || 2000,
     }
    if (IS_DEV) {
        Object.assign(options, { poolTimeout: 2000 })
    }
    return { loader: 'thread-loader', options }
}

module.exports = threadLoader;
