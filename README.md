# 洛书AI
洛书AI Chatbot

### 线上地址 


# 资料
## 业务资料

## 技术资料
### 云平台
http://cloud.intra.ke.com/console/project/cloud/application/luoshu-agent-chat/dashboard

### 浮屠
<!-- https://buddha.ke.com/micro-fe/feci/buddha/21790/jobs?service_id=1518 -->

### 技术栈
- 框架 react@17
- 状态管理 react-redux@7
- UI库 antd-mobile@2
- 路由管理 react-router-dom@5

# 本地启动
## 初始化
```
npm install 
```

## 启动服务
```
npm start
```

配置host（如下）后，访问页面 http://dev.crm.test.ke.com:80/?spm=ff0wesd22sdfx4sdf
```
127.0.0.1 dev.crm.test.ke.com
```

# 测试&发布
## 测试环境
<!-- 大禹项目地址： https://dayu.ke.com/project/pipeline?id=1948  
大禹模块名： im-saas-cdn  

http://im-saas-cdn.docker-400.tta.test.ke.com/?spm=ff0wesd22sdfx4sdf（进线选择“房源系统”，账号不限），搭配https://crm.docker-400.tta.test.ke.com/#/使用（需要置闲，账号使用 26522323）

## 预发环境（云平台or浮屠均可发布）
https://preview01-shelper.ke.com/?spm=27367f976c53bbe3564a90aab75543ed  

## 线上发布
线上发布使用流水线： https://ones.ke.com/system/pipeline/v2/pipelines/1010845/current  
手动发布地址： https://ones.ke.com/system/injustice/application/service/sinan-sinan-im-saas/publish   -->

## 珊瑚代客

## 测试账号

# 维护人员
## FE
李帅伟 

## RD
李帅伟

## QA
谢逸雯(离线)  

## PM
谢凯恩