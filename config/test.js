/*
 * @Author: liu<PERSON>kun002
 * @Date: 2021-03-22 16:25:34
 * @LastEditor: liuzhenkun002
 * @LastEditTime: 2021-06-02 20:03:12
 */

module.exports = {
  BASE_API: {
    webHost: "",
    apiHost: "/web/fe/imv3",
    httpHost: "//im-api.docker-400.tta.test.ke.com"
  },
  // 大禹构建时会通过环境变量把CDN模块传过来，因此在大禹的构建里要传 CDN_HOST 参数
  // 只有test环境这样，preview prod写死
  CDN_PATH: process.env.CDN_HOST ? `//${process.env.CDN_HOST}/luoshu-agent-chat/` : "//test-s1.ljcdn.com/luoshu-agent-chat/"
}
